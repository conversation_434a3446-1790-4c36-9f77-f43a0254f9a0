
# AutoRepurpose Worker Service

This is the video processing worker service for AutoRepurpose SaaS. It handles video conversion jobs using FFmpeg and BullMQ.

## Deployment on Railway

### 1. Push to GitHub

First, push this worker directory to a GitHub repository:

```bash
git init
git add .
git commit -m "Initial worker service"
git remote add origin <your-github-repo-url>
git push -u origin main
```

### 2. Deploy to Railway

1. Go to [Railway.app](https://railway.app)
2. Click "New Project"
3. Choose "Deploy from GitHub repo"
4. Select your repository
5. Railway will automatically detect the Node.js project

### 3. Configure Environment Variables

In Railway dashboard, go to your project > Variables and add all the environment variables listed in `.env.example` with your actual values:

```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

AWS_REGION=your-aws-region
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
S3_BUCKET_NAME=your-s3-bucket

UPSTASH_REDIS_REST_URL=your-redis-url
UPSTASH_REDIS_REST_TOKEN=your-redis-token

WORKER_CONCURRENCY=3
MAX_PROCESSING_TIME_MS=600000
LOG_LEVEL=info
```

### 4. Deploy

Railway will automatically build and deploy your worker. The logs will show:
- FFmpeg installation
- Node.js dependencies installation  
- Worker startup and connection to Redis

### 5. Monitor

Check Railway logs to ensure the worker is:
- ✅ Connecting to Redis successfully
- ✅ Connecting to Supabase successfully  
- ✅ Ready to process video jobs

## Local Development

```bash
npm install
cp .env.example .env
# Fill in your environment variables in .env
npm run dev
```

## Features

- ✅ BullMQ job processing with Redis
- ✅ FFmpeg video conversion
- ✅ AWS S3 file handling
- ✅ Supabase database integration
- ✅ Multiple platform support
- ✅ Error handling and retry logic
- ✅ Progress tracking
- ✅ Graceful shutdown
- ✅ Health checks

## Architecture

The worker:
1. Connects to Upstash Redis for job queue
2. Downloads video from S3 when job received
3. Processes video for each selected platform using FFmpeg
4. Uploads converted videos back to S3
5. Updates job status in Supabase database
6. Handles errors and retries
