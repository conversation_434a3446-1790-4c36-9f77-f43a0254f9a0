
import { Worker } from 'bullmq';
import { createClient } from '@supabase/supabase-js';
import { S3Client, GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import Redis from 'ioredis';
import ffmpeg from 'fluent-ffmpeg';
import { promises as fs } from 'fs';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize services
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

const redis = new Redis(process.env.REDIS_URL, {
  maxRetriesPerRequest: null, // Required by BullMQ
});

// Configuration
const WORKER_CONCURRENCY = parseInt(process.env.WORKER_CONCURRENCY || '3');
const MAX_PROCESSING_TIME = parseInt(process.env.MAX_PROCESSING_TIME_MS || '600000');
const TEMP_DIR = '/tmp/autorepurpose';

// Ensure temp directory exists
await fs.mkdir(TEMP_DIR, { recursive: true });

console.log('🚀 AutoRepurpose Worker starting...');
console.log(`📊 Concurrency: ${WORKER_CONCURRENCY}`);
console.log(`⏱️  Max processing time: ${MAX_PROCESSING_TIME}ms`);

// Video processing worker
const worker = new Worker(
  'video-processing',
  async (job) => {
    const { jobId, inputS3Key, selectedPlatforms, platformRules } = job.data;
    
    console.log(`🎬 Processing job ${jobId} with ${selectedPlatforms.length} platforms`);
    
    try {
      // Update job status to processing
      await supabase
        .from('video_jobs')
        .update({
          status: 'processing',
          processing_started_at: new Date().toISOString(),
          queue_job_id: job.id
        })
        .eq('id', jobId);

      // Download input video from S3
      const inputPath = join(TEMP_DIR, `input_${jobId}.mp4`);
      await downloadFromS3(inputS3Key, inputPath);
      
      console.log(`📥 Downloaded input video: ${inputPath}`);

      // Process each platform
      const results = [];
      for (let i = 0; i < selectedPlatforms.length; i++) {
        const platformId = selectedPlatforms[i];
        const platformRule = platformRules[platformId];
        
        if (!platformRule) {
          console.warn(`⚠️  Platform rule not found for ${platformId}`);
          continue;
        }

        await job.updateProgress(Math.floor((i / selectedPlatforms.length) * 100));

        try {
          const result = await processPlatform(jobId, inputPath, platformRule, platformId);
          results.push(result);
          
          console.log(`✅ Processed ${platformId}: ${result.outputS3Key}`);
        } catch (error) {
          console.error(`❌ Failed to process ${platformId}:`, error);
          
          // Create failed output record
          await supabase
            .from('video_outputs')
            .insert({
              job_id: jobId,
              platform_id: platformId,
              preset_name: platformRule.name,
              s3_output_key: '',
              filename: '',
              resolution: `${platformRule.output_preset.resolution.width}x${platformRule.output_preset.resolution.height}`,
              status: 'failed',
              error_message: error.message
            });
        }
      }

      // Cleanup input file
      await fs.unlink(inputPath).catch(() => {});

      // Update job status to completed
      await supabase
        .from('video_jobs')
        .update({
          status: 'completed',
          processing_completed_at: new Date().toISOString()
        })
        .eq('id', jobId);

      console.log(`🎉 Job ${jobId} completed with ${results.length} successful outputs`);
      
      return { success: true, results };

    } catch (error) {
      console.error(`💥 Job ${jobId} failed:`, error);
      
      // Update job status to failed
      await supabase
        .from('video_jobs')
        .update({
          status: 'failed',
          error_message: error.message,
          processing_completed_at: new Date().toISOString()
        })
        .eq('id', jobId);

      throw error;
    }
  },
  {
    connection: redis,
    concurrency: WORKER_CONCURRENCY,
    removeOnComplete: 10,
    removeOnFail: 50,
    maxStalledCount: 3,
    stalledInterval: 30000,
    maxRetriesPerJob: 2,
  }
);

// Process single platform
async function processPlatform(jobId, inputPath, platformRule, platformId) {
  const outputFilename = `${jobId}_${platformId}.mp4`;
  const outputPath = join(TEMP_DIR, outputFilename);
  const outputS3Key = `outputs/${jobId}/${outputFilename}`;

  // Build FFmpeg command
  const command = ffmpeg(inputPath);
  const { output_preset } = platformRule;

  // Apply video settings
  command
    .videoCodec(output_preset.video_codec)
    .audioCodec(output_preset.audio_codec)
    .videoBitrate(output_preset.bitrate.video)
    .audioBitrate(output_preset.bitrate.audio)
    .fps(output_preset.frame_rate);

  // Apply resolution and scaling
  const { width, height } = output_preset.resolution;
  
  switch (output_preset.fit_strategy) {
    case 'scale_crop':
      command.size(`${width}x${height}`).aspect(`${width}:${height}`);
      break;
    case 'scale_pad':
      const padColor = output_preset.padding_color || 'black';
      command.videoFilters(`scale=${width}:${height}:force_original_aspect_ratio=decrease,pad=${width}:${height}:(ow-iw)/2:(oh-ih)/2:${padColor}`);
      break;
    case 'scale_stretch':
      command.size(`${width}x${height}`);
      break;
  }

  // Duration limits
  if (output_preset.max_duration_seconds) {
    command.duration(output_preset.max_duration_seconds);
  }

  // Quality settings
  if (platformRule.optimization.two_pass_encoding) {
    command.addOption('-preset', 'slow');
  }

  // Hardware acceleration
  if (platformRule.optimization.hardware_acceleration) {
    command.addOption('-hwaccel', 'auto');
  }

  // Process video
  await new Promise((resolve, reject) => {
    command
      .output(outputPath)
      .on('start', (cmd) => {
        console.log(`🔄 FFmpeg started: ${cmd}`);
      })
      .on('progress', (progress) => {
        if (progress.percent) {
          console.log(`📊 ${platformId}: ${Math.round(progress.percent)}%`);
        }
      })
      .on('end', () => {
        console.log(`✅ FFmpeg completed for ${platformId}`);
        resolve();
      })
      .on('error', (err) => {
        console.error(`❌ FFmpeg error for ${platformId}:`, err);
        reject(err);
      })
      .run();
  });

  // Get output file stats
  const stats = await fs.stat(outputPath);
  
  // Upload to S3
  await uploadToS3(outputPath, outputS3Key);
  
  // Create output record in database
  await supabase
    .from('video_outputs')
    .insert({
      job_id: jobId,
      platform_id: platformId,
      preset_name: platformRule.name,
      s3_output_key: outputS3Key,
      filename: outputFilename,
      file_size_bytes: stats.size,
      resolution: `${width}x${height}`,
      bitrate: output_preset.bitrate.video,
      video_codec: output_preset.video_codec,
      audio_codec: output_preset.audio_codec,
      status: 'completed'
    });

  // Cleanup output file
  await fs.unlink(outputPath).catch(() => {});

  return {
    platformId,
    outputS3Key,
    filename: outputFilename,
    fileSize: stats.size
  };
}

// S3 helpers
async function downloadFromS3(s3Key, localPath) {
  const command = new GetObjectCommand({
    Bucket: process.env.S3_BUCKET_NAME,
    Key: s3Key,
  });

  const response = await s3Client.send(command);
  const stream = response.Body;
  
  const writeStream = await fs.open(localPath, 'w');
  const chunks = [];
  
  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  
  await writeStream.writeFile(Buffer.concat(chunks));
  await writeStream.close();
}

async function uploadToS3(localPath, s3Key) {
  const fileBuffer = await fs.readFile(localPath);
  
  const command = new PutObjectCommand({
    Bucket: process.env.S3_BUCKET_NAME,
    Key: s3Key,
    Body: fileBuffer,
    ContentType: 'video/mp4',
  });

  await s3Client.send(command);
}

// Error handling
worker.on('completed', (job) => {
  console.log(`✅ Job ${job.id} completed successfully`);
});

worker.on('failed', (job, err) => {
  console.error(`❌ Job ${job.id} failed:`, err.message);
});

worker.on('error', (err) => {
  console.error('🔥 Worker error:', err);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('📤 Shutting down worker...');
  await worker.close();
  await redis.quit();
  process.exit(0);
});

console.log('✅ AutoRepurpose Worker is ready and waiting for jobs!');
