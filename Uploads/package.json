{"name": "autorepurpose-worker", "version": "1.0.0", "description": "Video processing worker for AutoRepurpose SaaS", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "build": "echo 'No build needed for Node.js worker'"}, "dependencies": {"bullmq": "^5.0.0", "ioredis": "^5.3.2", "@aws-sdk/client-s3": "^3.400.0", "@aws-sdk/s3-request-presigner": "^3.400.0", "@supabase/supabase-js": "^2.38.0", "fluent-ffmpeg": "^2.1.2", "dotenv": "^16.3.1", "uuid": "^9.0.1"}, "engines": {"node": ">=20.0.0"}}