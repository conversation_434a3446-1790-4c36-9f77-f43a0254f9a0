it looks like the frontend/client crashed, i got this: "Application error: a client-side exception has occurred (see the browser console for more information).".
and here are the messages from console:
[11:22:38 AM] Starting upload process: {"fileName":"Music to Listen to While Studying - <PERSON><PERSON> (1080p, h264).mp4","fileType":"video/mp4","fileSize":8080363,"platforms":["instagram_reel","twitter"],"userId":"user_31jOxSeruXW9tGq0tsAqInqyqlC"}
page-a2c8de7d061ab50a.js:1 [11:22:38 AM] Requesting presigned URL from API
page-a2c8de7d061ab50a.js:1 [11:22:39 AM] Presigned URL response received: {"status":200,"statusText":""}
page-a2c8de7d061ab50a.js:1 [11:22:39 AM] Presigned URL data received: {"hasUploadUrl":true,"hasKey":true,"hasJobId":true}
page-a2c8de7d061ab50a.js:1 [11:22:39 AM] Starting S3 upload: {"uploadUrlLength":849}
page-a2c8de7d061ab50a.js:1 [11:22:39 AM] Configuring XMLHttpRequest: {"method":"PUT","timeout":300000,"contentType":"video/mp4"}
page-a2c8de7d061ab50a.js:1 [11:22:39 AM] XMLHttpRequest sent, waiting for response
page-a2c8de7d061ab50a.js:1 [11:22:39 AM] Upload progress: 1.3%
page-a2c8de7d061ab50a.js:1 [11:22:39 AM] Upload progress: 1.8%
page-a2c8de7d061ab50a.js:1 [11:22:40 AM] Upload progress: 3.1%
page-a2c8de7d061ab50a.js:1 [11:22:40 AM] Upload progress: 4.5%
page-a2c8de7d061ab50a.js:1 [11:22:40 AM] Upload progress: 6.2%
page-a2c8de7d061ab50a.js:1 [11:22:41 AM] Upload progress: 7.6%
page-a2c8de7d061ab50a.js:1 [11:22:41 AM] Upload progress: 9.2%
page-a2c8de7d061ab50a.js:1 [11:22:42 AM] Upload progress: 11.2%
page-a2c8de7d061ab50a.js:1 [11:22:42 AM] Upload progress: 13.1%
page-a2c8de7d061ab50a.js:1 [11:22:42 AM] Upload progress: 15.1%
page-a2c8de7d061ab50a.js:1 [11:22:43 AM] Upload progress: 17.0%
page-a2c8de7d061ab50a.js:1 [11:22:43 AM] Upload progress: 19.5%
page-a2c8de7d061ab50a.js:1 [11:22:43 AM] Upload progress: 22.1%
page-a2c8de7d061ab50a.js:1 [11:22:44 AM] Upload progress: 25.3%
page-a2c8de7d061ab50a.js:1 [11:22:44 AM] Upload progress: 28.5%
page-a2c8de7d061ab50a.js:1 [11:22:44 AM] Upload progress: 31.6%
page-a2c8de7d061ab50a.js:1 [11:22:45 AM] Upload progress: 35.5%
page-a2c8de7d061ab50a.js:1 [11:22:45 AM] Upload progress: 39.4%
page-a2c8de7d061ab50a.js:1 [11:22:45 AM] Upload progress: 43.8%
page-a2c8de7d061ab50a.js:1 [11:22:45 AM] Upload progress: 49.0%
page-a2c8de7d061ab50a.js:1 [11:22:46 AM] Upload progress: 51.6%
page-a2c8de7d061ab50a.js:1 [11:22:46 AM] Upload progress: 54.7%
page-a2c8de7d061ab50a.js:1 [11:22:46 AM] Upload progress: 60.2%
page-a2c8de7d061ab50a.js:1 [11:22:46 AM] Upload progress: 66.8%
page-a2c8de7d061ab50a.js:1 [11:22:47 AM] Upload progress: 73.3%
page-a2c8de7d061ab50a.js:1 [11:22:47 AM] Upload progress: 79.6%
page-a2c8de7d061ab50a.js:1 [11:22:48 AM] Upload progress: 80.0%
page-a2c8de7d061ab50a.js:1 [11:22:48 AM] S3 upload completed: {"status":200,"statusText":"OK","responseHeaders":"content-length: 0\r\netag: \"2508ff3afc54ece39fa36262497a5059\"\r\n"}
page-a2c8de7d061ab50a.js:1 [11:22:48 AM] S3 upload successful, creating job record
page-a2c8de7d061ab50a.js:1 [11:22:48 AM] Sending job creation request: {"jobId":"b763f95d-b48b-431e-9d95-a57a7195c766","fileName":"Music to Listen to While Studying - Gohar Khan (1080p, h264).mp4","s3Key":"uploads/user_31jOxSeruXW9tGq0tsAqInqyqlC/b763f95d-b48b-431e-9d95-a57a7195c766/Music to Listen to While Studying - Gohar Khan (1080p, h264).mp4","platforms":["instagram_reel","twitter"]}
page-a2c8de7d061ab50a.js:1 [11:22:49 AM] Job creation response: {"status":200,"statusText":""}
page-a2c8de7d061ab50a.js:1 [11:22:49 AM] Job created successfully: {}
117-e798375bc42c4e20.js:1 RangeError: Invalid time value
    at 182-cfc109aedbd7c86a.js:1:12875
    at y (182-cfc109aedbd7c86a.js:1:14731)
    at page-a2c8de7d061ab50a.js:1:17587
    at Array.map (<anonymous>)
    at O (page-a2c8de7d061ab50a.js:1:17002)
    at rE (fd9d1056-46cbeed89bb3f867.js:1:40341)
    at iZ (fd9d1056-46cbeed89bb3f867.js:1:117026)
    at ia (fd9d1056-46cbeed89bb3f867.js:1:95162)
    at fd9d1056-46cbeed89bb3f867.js:1:94984
    at il (fd9d1056-46cbeed89bb3f867.js:1:94991)
push.2304.window.console.error @ 117-e798375bc42c4e20.js:1Understand this error
api/jobs:1  Failed to load resource: the server responded with a status of 500 ()Understand this error
117-e798375bc42c4e20.js:1 Error fetching jobs: Error: HTTP error! status: 500
    at page-a2c8de7d061ab50a.js:1:28255
push.2304.window.console.error @ 117-e798375bc42c4e20.js:1Understand this error
api/jobs:1  Failed to load resource: the server responded with a status of 500 ()Understand this error
117-e798375bc42c4e20.js:1 Error fetching jobs: Error: HTTP error! status: 500
    at page-a2c8de7d061ab50a.js:1:28255
i copied from console directly can you understand. it looks like the job was created but for some reason it is giving  an error fetching jobs internal server error 500.