-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.payment_transactions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  stripe_payment_intent_id character varying,
  stripe_subscription_id character varying,
  amount_cents integer NOT NULL,
  currency character varying NOT NULL DEFAULT 'usd'::character varying,
  status character varying NOT NULL,
  transaction_type character varying NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  provider character varying DEFAULT 'razorpay'::character varying,
  razorpay_payment_id character varying,
  razorpay_order_id character varying,
  razorpay_subscription_id character varying,
  razorpay_customer_id character varying,
  stripe_customer_id character varying,
  provider_transaction_id character varying,
  metadata jsonb,
  CONSTRAINT payment_transactions_pkey PRIMARY KEY (id),
  CONSTRAINT payment_transactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.platform_rules_cache (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  rules_version character varying NOT NULL,
  rules_data jsonb NOT NULL,
  checksum character varying NOT NULL,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT platform_rules_cache_pkey PRIMARY KEY (id)
);
CREATE TABLE public.usage_logs (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  job_id uuid,
  action character varying NOT NULL,
  details jsonb,
  ip_address inet,
  user_agent text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT usage_logs_pkey PRIMARY KEY (id),
  CONSTRAINT usage_logs_job_id_fkey FOREIGN KEY (job_id) REFERENCES public.video_jobs(id),
  CONSTRAINT usage_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.users (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  clerk_user_id character varying NOT NULL UNIQUE,
  email character varying NOT NULL,
  full_name character varying,
  subscription_status character varying NOT NULL DEFAULT 'trial'::character varying,
  subscription_id character varying,
  current_period_end timestamp with time zone,
  free_repurposings_used integer NOT NULL DEFAULT 0,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  stripe_customer_id character varying,
  razorpay_customer_id character varying,
  preferred_payment_provider character varying DEFAULT 'razorpay'::character varying,
  CONSTRAINT users_pkey PRIMARY KEY (id)
);
CREATE TABLE public.video_jobs (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  original_filename character varying NOT NULL,
  s3_input_key character varying NOT NULL,
  file_size_bytes bigint NOT NULL,
  duration_seconds numeric NOT NULL,
  width integer NOT NULL,
  height integer NOT NULL,
  aspect_ratio numeric NOT NULL,
  form_type character varying NOT NULL,
  status character varying NOT NULL DEFAULT 'pending'::character varying,
  error_message text,
  selected_platforms jsonb NOT NULL,
  queue_job_id character varying,
  processing_started_at timestamp with time zone,
  processing_completed_at timestamp with time zone,
  expires_at timestamp with time zone NOT NULL DEFAULT (now() + '3 days'::interval),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  progress integer DEFAULT 0,
  download_url text,
  CONSTRAINT video_jobs_pkey PRIMARY KEY (id),
  CONSTRAINT video_jobs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.video_outputs (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  job_id uuid NOT NULL,
  platform_id character varying NOT NULL,
  preset_name character varying NOT NULL,
  s3_output_key character varying NOT NULL,
  filename character varying NOT NULL,
  file_size_bytes bigint,
  resolution character varying NOT NULL,
  bitrate character varying,
  video_codec character varying,
  audio_codec character varying,
  status character varying NOT NULL DEFAULT 'pending'::character varying,
  error_message text,
  processing_time_seconds integer,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  download_url text,
  duration numeric,
  CONSTRAINT video_outputs_pkey PRIMARY KEY (id),
  CONSTRAINT video_outputs_job_id_fkey FOREIGN KEY (job_id) REFERENCES public.video_jobs(id)
);
