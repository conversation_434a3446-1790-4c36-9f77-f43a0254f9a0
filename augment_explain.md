# AutoRepurpose SaaS - Complete Technical Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Project Structure](#project-structure)
3. [Third-Party APIs and Services](#third-party-apis-and-services)
4. [Workflow & Execution Flow](#workflow--execution-flow)
5. [User Flow](#user-flow)
6. [Dependencies & Environment Setup](#dependencies--environment-setup)
7. [Database Architecture](#database-architecture)
8. [API Architecture](#api-architecture)
9. [Worker Service Architecture](#worker-service-architecture)
10. [Frontend Components](#frontend-components)
11. [Payment Integration](#payment-integration)
12. [Platform Rules Engine](#platform-rules-engine)
13. [File Management & Storage](#file-management--storage)
14. [Authentication & Security](#authentication--security)
15. [Deployment & Infrastructure](#deployment--infrastructure)
16. [Development Workflow](#development-workflow)

## Project Overview

**AutoRepurpose** is a comprehensive SaaS platform that automatically converts videos into multiple social media platform formats. The system intelligently classifies videos as "short-form" (≤120s, vertical ≥1.6 aspect ratio) or "long-form" content and only allows same-form conversions to maintain content integrity.

### Key Features
- **Intelligent Video Classification**: Automatic form-type detection based on duration and aspect ratio
- **Multi-Platform Support**: TikTok, Instagram (Reels/Stories), YouTube Shorts, Facebook, LinkedIn, etc.
- **Real-time Processing**: Live job status updates with progress tracking
- **Dual Payment System**: Stripe (global) and Razorpay (India) integration
- **File Management**: 3-day retention with automatic cleanup
- **Admin Dashboard**: Platform rules management and system monitoring

### Business Model
- **Pricing**: $30/month (Stripe) or ₹2,500/month (Razorpay)
- **Free Trial**: 1 free video repurposing per user
- **File Limits**: 5GB maximum upload size
- **Processing Time**: 2-5 minutes per video
- **Retention**: 3-day automatic file cleanup

## Project Structure

```
autorepurpose-saas/
├── app/                          # Next.js Frontend Application
│   ├── app/                      # App Router Structure
│   │   ├── (auth)/              # Authentication pages
│   │   │   ├── sign-in/         # Clerk sign-in page
│   │   │   └── sign-up/         # Clerk sign-up page
│   │   ├── api/                 # API Routes
│   │   │   ├── auth/            # Authentication webhooks
│   │   │   ├── jobs/            # Job management endpoints
│   │   │   ├── payments/        # Payment processing
│   │   │   │   ├── stripe/      # Stripe integration
│   │   │   │   └── razorpay/    # Razorpay integration
│   │   │   ├── upload/          # File upload endpoints
│   │   │   ├── user/            # User management
│   │   │   ├── webhooks/        # External webhooks
│   │   │   └── worker/          # Worker communication
│   │   ├── dashboard/           # Protected dashboard pages
│   │   │   ├── files/           # File management
│   │   │   └── page.tsx         # Main dashboard
│   │   ├── globals.css          # Global styles
│   │   ├── layout.tsx           # Root layout with Clerk
│   │   └── page.tsx             # Landing page
│   ├── components/              # Reusable UI Components
│   │   ├── ui/                  # Shadcn/ui components
│   │   ├── Dashboard/           # Dashboard-specific components
│   │   ├── FileManagement/      # File management components
│   │   ├── payment/             # Payment components
│   │   ├── job-status-list.tsx  # Job monitoring
│   │   └── video-upload.tsx     # Upload interface
│   ├── data/                    # Static data files
│   │   └── video_platforms_rules_updated.json  # Platform specifications
│   ├── hooks/                   # Custom React hooks
│   ├── lib/                     # Core business logic
│   │   ├── supabase/            # Database clients
│   │   ├── db.ts                # Database utilities
│   │   ├── queue.ts             # Job queue management
│   │   ├── platform-rules.ts    # Platform rules engine
│   │   ├── payment-utils.ts     # Payment utilities
│   │   └── types.ts             # TypeScript definitions
│   ├── prisma/                  # Database schema and migrations
│   │   ├── schema.prisma        # Prisma schema
│   │   └── migrations/          # Database migrations
│   ├── middleware.ts            # Clerk authentication middleware
│   ├── next.config.js           # Next.js configuration
│   ├── package.json             # Frontend dependencies
│   └── tailwind.config.ts       # Tailwind CSS configuration
├── worker/                      # Video Processing Worker Service
│   ├── src/                     # Worker source code
│   │   ├── config/              # Configuration management
│   │   ├── services/            # Core services
│   │   │   ├── apiClient.ts     # API communication
│   │   │   ├── platformRules.ts # Platform rules service
│   │   │   ├── s3Service.ts     # AWS S3 operations
│   │   │   └── videoProcessor.ts # FFmpeg processing
│   │   ├── types/               # TypeScript definitions
│   │   ├── worker/              # Job processing logic
│   │   │   └── jobProcessor.ts  # Main job processor
│   │   └── index.ts             # Worker entry point
│   ├── dist/                    # Compiled JavaScript
│   ├── Dockerfile               # Docker configuration
│   ├── railway.toml             # Railway deployment config
│   ├── package.json             # Worker dependencies
│   └── README.md                # Worker documentation
├── ARCHITECTURE.md              # System architecture documentation
├── PAYMENT_INTEGRATION.md       # Payment system documentation
└── AutoRepurpose_SaaS_Architecture.md  # Comprehensive architecture
```

### File Naming Conventions
- **Components**: PascalCase (e.g., `VideoUploadComponent.tsx`)
- **API Routes**: kebab-case (e.g., `create-order/route.ts`)
- **Utilities**: camelCase (e.g., `platformRules.ts`)
- **Database**: snake_case (e.g., `video_jobs`, `user_id`)
- **Environment**: UPPER_SNAKE_CASE (e.g., `AWS_ACCESS_KEY_ID`)

### Separation of Concerns
- **Frontend (`app/`)**: User interface, authentication, API communication
- **Worker (`worker/`)**: Video processing, FFmpeg operations, file handling
- **Database**: User data, job tracking, payment records
- **Storage**: Original and processed video files
- **Queue**: Job orchestration and status tracking

## Third-Party APIs and Services

### 1. Clerk Authentication
**Purpose**: User authentication and session management
**Integration**: Frontend authentication, user sync webhooks
**Configuration**:
```env
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_...
CLERK_SECRET_KEY=sk_live_...
CLERK_WEBHOOK_SECRET=whsec_...
```
**Key Features**:
- Social login (Google, GitHub, etc.)
- Email/password authentication
- Session management
- User profile management
- Webhook-based user synchronization

### 2. Supabase (PostgreSQL)
**Purpose**: Primary database for user data, jobs, and payments
**Integration**: Prisma ORM with direct Supabase client
**Configuration**:
```env
DATABASE_URL=postgresql://...
NEXT_PUBLIC_SUPABASE_URL=https://...supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiI...
```
**Key Features**:
- PostgreSQL database with real-time subscriptions
- Row-level security
- Automatic backups
- Built-in authentication (not used, Clerk preferred)

### 3. AWS S3
**Purpose**: Video file storage (original and processed)
**Integration**: AWS SDK v3 with presigned URLs
**Configuration**:
```env
AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=twF21iE+cHN8/P4lzbz3+fuFZLDKAvv2fIxQ8/19
S3_BUCKET_NAME=auto-repurpose
```
**Storage Structure**:
```
auto-repurpose/
├── uploads/user-{userId}/{jobId}/original.{ext}
├── outputs/user-{userId}/{jobId}/platform_preset.mp4
└── temp/                    # Temporary processing files
```
**Key Features**:
- Presigned URLs for secure uploads
- 3-day lifecycle policy for automatic cleanup
- CORS configuration for browser uploads
- Versioning and backup policies

### 4. Upstash Redis
**Purpose**: Job queue management with BullMQ
**Integration**: BullMQ worker queues
**Configuration**:
```env
REDIS_URL=rediss://default:...@...upstash.io:6379
UPSTASH_REDIS_REST_URL=https://...upstash.io
UPSTASH_REDIS_REST_TOKEN=...
```
**Queue Structure**:
- `video-processing`: Main processing queue
- `file-cleanup`: Automated cleanup jobs
- `job-retry`: Failed job retry queue

### 5. Stripe Payment Processing
**Purpose**: Global payment processing ($30/month)
**Integration**: Stripe SDK with webhooks
**Configuration**:
```env
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PRICE_ID=price_...
```
**Key Features**:
- Subscription management
- Customer portal
- Webhook event handling
- International payment methods

### 6. Razorpay Payment Processing
**Purpose**: India-specific payment processing (₹2,500/month)
**Integration**: Razorpay SDK with signature verification
**Configuration**:
```env
RAZORPAY_KEY_ID=rzp_live_...
RAZORPAY_KEY_SECRET=...
RAZORPAY_WEBHOOK_SECRET=...
RAZORPAY_PLAN_ID=plan_...
```
**Key Features**:
- UPI, Net Banking, Cards, Wallets
- EMI options
- Subscription management
- INR currency support

### 7. Railway (Worker Hosting)
**Purpose**: Video processing worker deployment
**Integration**: Docker-based deployment with auto-scaling
**Configuration**:
```toml
[build]
builder = "NIXPACKS"

[deploy]
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10
```
**Key Features**:
- Automatic deployments from GitHub
- Environment variable management
- Health checks and monitoring
- Auto-restart on failures

### 8. Vercel (Frontend Hosting)
**Purpose**: Next.js application hosting
**Integration**: Git-based deployments
**Key Features**:
- Automatic deployments
- Edge functions for API routes
- Global CDN
- Environment variable management

### 9. FFmpeg (Video Processing)
**Purpose**: Video format conversion and optimization
**Integration**: fluent-ffmpeg wrapper in worker service
**Key Features**:
- Multi-format support (MP4, MOV, AVI, etc.)
- Resolution and aspect ratio conversion
- Codec optimization (H.264, AAC)
- Progress tracking during processing

## Workflow & Execution Flow

### End-to-End Request Lifecycle

#### 1. User Upload → Frontend Processing
```
User selects video file
    ↓
Frontend validates file (type, size ≤5GB)
    ↓
Request presigned S3 URL from API
    ↓
Direct upload to S3 with progress tracking
    ↓
Create job record in database
    ↓
Add job to BullMQ queue
    ↓
Redirect to dashboard with real-time updates
```

#### 2. Backend → Worker Processing
```
Worker picks job from Redis queue
    ↓
Download original video from S3
    ↓
Extract metadata (duration, dimensions, aspect ratio)
    ↓
Classify as short_form or long_form
    ↓
Validate platform compatibility
    ↓
Process video for each selected platform:
    - Apply platform-specific settings
    - Convert with FFmpeg
    - Upload to S3
    - Update database
    ↓
Mark job as completed
    ↓
Schedule cleanup job (3 days)
```

#### 3. Worker → Frontend → User
```
Worker updates job status in database
    ↓
Frontend polls/subscribes to status changes
    ↓
Real-time UI updates via Server-Sent Events
    ↓
User downloads processed videos
    ↓
Files automatically deleted after 3 days
```

### Step-by-Step Data Flow

#### Phase 1: Upload & Validation
1. **File Selection**: User drags/selects video file
2. **Client Validation**: Check file type and size limits
3. **Presigned URL**: Request secure S3 upload URL
4. **S3 Upload**: Direct browser-to-S3 upload with progress
5. **Job Creation**: Create database record with metadata
6. **Queue Addition**: Add processing job to Redis queue

#### Phase 2: Video Processing
1. **Job Pickup**: Worker retrieves job from queue
2. **File Download**: Download original from S3 to temp storage
3. **Metadata Extraction**: Use ffprobe to get video properties
4. **Form Classification**: Apply duration + aspect ratio rules
5. **Platform Validation**: Check selected platforms against form type
6. **Parallel Processing**: Convert for each valid platform
7. **Upload Results**: Store processed videos in S3
8. **Database Updates**: Update job and output records

#### Phase 3: User Access & Cleanup
1. **Status Updates**: Real-time progress via SSE
2. **Download Access**: Generate presigned download URLs
3. **File Serving**: Secure file access with authentication
4. **Automatic Cleanup**: Cron job removes files after 3 days

### Background Jobs & Worker Execution

#### Job Queue Structure
```typescript
interface VideoProcessingJob {
  jobId: string;
  userId: string;
  s3Key: string;
  platforms: string[];
  originalFileName: string;
  platformRules: PlatformRulesData;
}
```

#### Worker Concurrency
- **Concurrent Jobs**: 3 simultaneous video processing jobs
- **Retry Logic**: 3 attempts with exponential backoff
- **Timeout**: 10 minutes maximum per job
- **Memory Management**: Automatic temp file cleanup

#### Error Handling & Fallback Flows
1. **Upload Failures**: Retry with exponential backoff
2. **Processing Errors**: Mark job as failed, notify user
3. **S3 Connectivity**: Queue job for retry
4. **Worker Crashes**: Jobs automatically redistributed
5. **Database Issues**: Graceful degradation with local logging

## User Flow

### New User Journey
1. **Landing Page**: User visits autorepurpose.com
2. **Sign Up**: Create account via Clerk (email/social)
3. **Database Sync**: Webhook creates user record in Supabase
4. **Dashboard Access**: Redirect to protected dashboard
5. **Free Trial**: 1 free video repurposing available
6. **Upload Video**: Select file and target platforms
7. **Processing**: Real-time status updates
8. **Download**: Access processed videos
9. **Upgrade Prompt**: After free trial usage

### Subscription User Journey
1. **Payment Selection**: Choose Stripe or Razorpay
2. **Checkout Process**: Complete payment flow
3. **Webhook Processing**: Update subscription status
4. **Unlimited Access**: Remove usage restrictions
5. **Ongoing Usage**: Upload and process videos
6. **Billing Management**: Access customer portal

### Video Processing User Flow
1. **File Selection**: Drag & drop or browse (≤5GB)
2. **Platform Selection**: Choose target platforms
3. **Upload Progress**: Real-time upload tracking
4. **Processing Queue**: Job added to queue
5. **Status Monitoring**: Live progress updates
6. **Completion Notification**: Email/dashboard alert
7. **Download Access**: Secure file downloads
8. **File Expiration**: 3-day countdown display

### Edge Cases & Error Scenarios
1. **File Too Large**: Clear error message with size limit
2. **Unsupported Format**: Format validation with suggestions
3. **Processing Failure**: Retry option with error details
4. **Payment Failure**: Clear payment error handling
5. **Expired Files**: Graceful handling with re-upload option
6. **Network Issues**: Offline detection and retry logic

## Dependencies & Environment Setup

### Frontend Dependencies (app/package.json)
```json
{
  "dependencies": {
    "@clerk/nextjs": "^6.30.2",           // Authentication
    "@supabase/supabase-js": "^2.55.0",   // Database client
    "@aws-sdk/client-s3": "^3.864.0",     // S3 operations
    "@stripe/stripe-js": "^7.8.0",        // Stripe payments
    "razorpay": "^2.9.6",                 // Razorpay payments
    "bullmq": "^5.58.1",                  // Job queue
    "ioredis": "^5.7.0",                  // Redis client
    "next": "14.2.28",                    // React framework
    "react": "18.2.0",                    // UI library
    "tailwindcss": "3.3.3",               // CSS framework
    "@radix-ui/react-*": "^1.x.x",        // UI components
    "framer-motion": "10.18.0",           // Animations
    "react-hook-form": "7.53.0",          // Form handling
    "zod": "3.23.8",                      // Schema validation
    "date-fns": "3.6.0",                  // Date utilities
    "lucide-react": "0.446.0"             // Icons
  }
}
```

### Worker Dependencies (worker/package.json)
```json
{
  "dependencies": {
    "@aws-sdk/client-s3": "^3.864.0",     // S3 operations
    "bullmq": "^5.58.0",                  // Job processing
    "ioredis": "^5.7.0",                  // Redis client
    "fluent-ffmpeg": "^2.1.2",            // Video processing
    "fs-extra": "^11.2.0",                // File operations
    "axios": "^1.7.2",                    // HTTP client
    "dotenv": "^16.5.0",                  // Environment variables
    "uuid": "^9.0.1"                      // ID generation
  }
}
```

### Environment Variables Structure

#### Frontend (.env.local)
```env
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_...
CLERK_SECRET_KEY=sk_live_...
CLERK_WEBHOOK_SECRET=whsec_...

# Database
DATABASE_URL=postgresql://...
NEXT_PUBLIC_SUPABASE_URL=https://...supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiI...

# AWS S3
AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=twF21iE+cHN8/P4lzbz3+fuFZLDKAvv2fIxQ8/19
S3_BUCKET_NAME=auto-repurpose
S3_UPLOAD_EXPIRES_IN=3600

# Redis Queue
REDIS_URL=rediss://default:...@...upstash.io:6379

# Stripe Payments
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PRICE_ID=price_...

# Razorpay Payments
RAZORPAY_KEY_ID=rzp_live_...
RAZORPAY_KEY_SECRET=...
RAZORPAY_WEBHOOK_SECRET=...
RAZORPAY_PLAN_ID=plan_...

# Application
NEXT_PUBLIC_APP_URL=https://autorepurpose.com
CLEANUP_SECRET_TOKEN=cleanup-secret-123
```

#### Worker (.env)
```env
# Redis Queue
REDIS_URL=rediss://default:...@...upstash.io:6379

# AWS S3
AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=twF21iE+cHN8/P4lzbz3+fuFZLDKAvv2fIxQ8/19
S3_BUCKET_NAME=auto-repurpose

# Worker Configuration
WORKER_CONCURRENCY=3
WORKER_NAME=video-processor-1
MAX_PROCESSING_TIME_MS=600000
TEMP_DIR=/tmp/autorepurpose

# FFmpeg
FFMPEG_PATH=/usr/bin/ffmpeg
FFPROBE_PATH=/usr/bin/ffprobe

# Application
APP_API_URL=https://autorepurpose.com
NODE_ENV=production
LOG_LEVEL=info
```

### Deployment Configuration

#### Vercel (Frontend)
- **Build Command**: `npm run build`
- **Output Directory**: `.next`
- **Node Version**: 18.x
- **Environment Variables**: All frontend env vars
- **Domains**: Custom domain with SSL

#### Railway (Worker)
- **Build Pack**: Nixpacks (auto-detected)
- **Start Command**: `npm start`
- **Health Check**: `/health` endpoint
- **Auto-restart**: On failure with 10 max retries
- **Environment Variables**: All worker env vars

## Database Architecture

### Prisma Schema Overview
The application uses Prisma ORM with PostgreSQL (Supabase) for type-safe database operations.

#### Core Tables

##### Users Table
```sql
model User {
  id                    String    @id @default(dbgenerated("uuid_generate_v4()"))
  clerkUserId          String    @unique @map("clerk_user_id")
  email                String
  fullName             String?   @map("full_name")
  subscriptionStatus   String    @default("trial") @map("subscription_status")
  subscriptionId       String?   @map("subscription_id")
  currentPeriodEnd     DateTime? @map("current_period_end")
  freeRepurposingsUsed Int       @default(0) @map("free_repurposings_used")

  // Payment provider customer IDs
  stripeCustomerId     String?   @map("stripe_customer_id")
  razorpayCustomerId   String?   @map("razorpay_customer_id")
  preferredPaymentProvider String? @default("stripe")

  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @default(now()) @updatedAt

  // Relations
  videoJobs            VideoJob[]
  paymentTransactions  PaymentTransaction[]
  usageLogs            UsageLog[]
}
```

##### VideoJob Table
```sql
model VideoJob {
  id                      String    @id @default(dbgenerated("uuid_generate_v4()"))
  userId                  String    @map("user_id")
  originalFilename        String    @map("original_filename")
  s3InputKey              String    @map("s3_input_key")
  fileSizeBytes           BigInt    @map("file_size_bytes")
  durationSeconds         Decimal   @map("duration_seconds")

  // Video metadata
  width                   Int
  height                  Int
  aspectRatio             Decimal   @map("aspect_ratio")
  formType                String    @map("form_type") // 'short_form' | 'long_form'

  // Processing status
  status                  String    @default("pending") // pending, processing, completed, failed
  progress                Int       @default(0) // 0-100
  errorMessage            String?   @map("error_message")

  // Platform selection
  selectedPlatforms       Json      @map("selected_platforms") // Array of platform IDs

  // Processing metadata
  queueJobId              String?   @map("queue_job_id")
  processingStartedAt     DateTime? @map("processing_started_at")
  processingCompletedAt   DateTime? @map("processing_completed_at")

  // Retention
  expiresAt               DateTime  @default(dbgenerated("(NOW() + INTERVAL '3 days')"))

  createdAt               DateTime  @default(now())
  updatedAt               DateTime  @default(now()) @updatedAt

  // Relations
  user                    User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  outputs                 VideoOutput[]
  usageLogs               UsageLog[]
}
```

##### VideoOutput Table
```sql
model VideoOutput {
  id                      String    @id @default(dbgenerated("uuid_generate_v4()"))
  jobId                   String    @map("job_id")
  platformId              String    @map("platform_id") // e.g., 'tiktok', 'instagram_reel'
  presetName              String    @map("preset_name") // e.g., 'quality_high'

  // Output file details
  s3OutputKey             String    @map("s3_output_key")
  downloadUrl             String?   @map("download_url")
  filename                String
  fileSizeBytes           BigInt?   @map("file_size_bytes")
  duration                Decimal?  // Duration in seconds

  // Technical specs applied
  resolution              String    // e.g., '1080x1920'
  bitrate                 String?   // e.g., '2500k'
  videoCodec              String?   @map("video_codec") // e.g., 'H.264'
  audioCodec              String?   @map("audio_codec") // e.g., 'AAC'

  // Processing status
  status                  String    @default("pending") // pending, processing, completed, failed
  errorMessage            String?   @map("error_message")
  processingTimeSeconds   Int?      @map("processing_time_seconds")

  createdAt               DateTime  @default(now())
  updatedAt               DateTime  @default(now()) @updatedAt

  // Relations
  job                     VideoJob  @relation(fields: [jobId], references: [id], onDelete: Cascade)
}
```

##### PaymentTransaction Table
```sql
model PaymentTransaction {
  id                      String    @id @default(dbgenerated("uuid_generate_v4()"))
  userId                  String    @map("user_id")

  // Payment provider identification
  provider                String    // 'stripe' | 'razorpay'

  // Stripe-specific fields
  stripePaymentIntentId   String?   @map("stripe_payment_intent_id")
  stripeSubscriptionId    String?   @map("stripe_subscription_id")
  stripeCustomerId        String?   @map("stripe_customer_id")

  // Razorpay-specific fields
  razorpayPaymentId       String?   @map("razorpay_payment_id")
  razorpayOrderId         String?   @map("razorpay_order_id")
  razorpaySubscriptionId  String?   @map("razorpay_subscription_id")
  razorpayCustomerId      String?   @map("razorpay_customer_id")

  // Common fields
  amountCents             Int       @map("amount_cents")
  currency                String    @default("usd") // 'usd' | 'inr'
  status                  String    // 'pending' | 'succeeded' | 'failed' | 'refunded'
  transactionType         String    @map("transaction_type") // 'subscription' | 'one_time'
  providerTransactionId   String?   @map("provider_transaction_id")
  metadata                Json?

  createdAt               DateTime  @default(now())

  // Relations
  user                    User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

### Database Relationships
- **User → VideoJob**: One-to-many (user can have multiple jobs)
- **VideoJob → VideoOutput**: One-to-many (job can have multiple platform outputs)
- **User → PaymentTransaction**: One-to-many (user can have multiple payments)
- **User → UsageLog**: One-to-many (user activity tracking)

### Indexing Strategy
```sql
-- Performance indexes
CREATE INDEX idx_video_jobs_user_id ON video_jobs(user_id);
CREATE INDEX idx_video_jobs_status ON video_jobs(status);
CREATE INDEX idx_video_jobs_expires_at ON video_jobs(expires_at);
CREATE INDEX idx_video_outputs_job_id ON video_outputs(job_id);
CREATE INDEX idx_video_outputs_status ON video_outputs(status);
CREATE INDEX idx_users_clerk_id ON users(clerk_user_id);
CREATE INDEX idx_users_subscription ON users(subscription_status);
CREATE INDEX idx_payment_transactions_user ON payment_transactions(user_id);
CREATE INDEX idx_payment_transactions_provider ON payment_transactions(provider);
```

### Data Retention Policies
- **Video Files**: Automatically deleted after 3 days
- **Job Records**: Kept indefinitely for analytics
- **Payment Records**: Kept indefinitely for compliance
- **Usage Logs**: Kept for 1 year for analytics

## API Architecture

### Next.js API Routes Structure

#### Authentication & User Management
```typescript
// POST /api/auth/webhook - Clerk webhook for user sync
export async function POST(request: NextRequest) {
  const webhook = new Webhook(process.env.CLERK_WEBHOOK_SECRET);
  const evt = webhook.verify(req.body, req.headers);

  switch (evt.type) {
    case 'user.created':
      await createUserRecord(evt.data);
      break;
    case 'user.updated':
      await updateUserRecord(evt.data);
      break;
    case 'user.deleted':
      await deleteUserRecord(evt.data);
      break;
  }
}

// GET /api/user/profile - Get current user profile
// PUT /api/user/profile - Update user profile
// GET /api/user/usage - Get usage statistics
```

#### Video Upload & Job Management
```typescript
// POST /api/upload/presigned - Get S3 presigned URL for upload
export async function POST(req: NextRequest) {
  const { userId } = await auth();
  const { fileName, fileType, fileSize } = await req.json();

  // Validate file size (5GB limit)
  if (fileSize > 5 * 1024 * 1024 * 1024) {
    return NextResponse.json({ error: 'File size exceeds 5GB limit' }, { status: 400 });
  }

  // Generate unique file key
  const jobId = randomUUID();
  const key = `uploads/${userId}/${jobId}/${fileName}`;

  // Create presigned URL
  const uploadUrl = await getSignedUrl(s3Client, new PutObjectCommand({
    Bucket: process.env.S3_BUCKET_NAME,
    Key: key,
    ContentType: fileType,
    ContentLength: fileSize,
  }), { expiresIn: 3600 });

  return NextResponse.json({ uploadUrl, key, jobId });
}

// POST /api/jobs/create - Create new video processing job
// GET /api/jobs - List user's jobs (paginated)
// GET /api/jobs/:id - Get specific job details
// DELETE /api/jobs/:id - Cancel/delete job
```

#### Payment & Subscription APIs
```typescript
// Stripe Integration
// POST /api/payments/stripe/create-session - Create Stripe checkout session
export async function POST(request: NextRequest) {
  const { userId } = await auth();

  const session = await stripe.checkout.sessions.create({
    customer_email: await getUserEmail(userId),
    line_items: [{ price: process.env.STRIPE_PRICE_ID, quantity: 1 }],
    mode: 'subscription',
    success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?payment=success`,
    cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/pricing?payment=cancelled`,
    metadata: { userId },
  });

  return NextResponse.json({ url: session.url });
}

// POST /api/payments/stripe/webhook - Stripe webhook handler
// Handles: checkout.session.completed, invoice.payment_succeeded, customer.subscription.updated

// Razorpay Integration
// POST /api/payments/razorpay/create-order - Create Razorpay order
export async function POST(request: NextRequest) {
  const { userId } = await auth();

  const options = {
    amount: 250000, // ₹2,500 in paise
    currency: 'INR',
    receipt: `order_${userId}_${Date.now()}`,
    notes: { user_id: userId, subscription_type: 'monthly' }
  };

  const order = await razorpay.orders.create(options);
  return NextResponse.json({ order });
}

// POST /api/payments/razorpay/verify - Verify Razorpay payment signature
// POST /api/payments/razorpay/webhook - Razorpay webhook handler
```

#### File Downloads & Management
```typescript
// GET /api/download/[outputId] - Generate presigned download URL
export async function GET(req: NextApiRequest, res: NextApiResponse) {
  const { outputId } = req.query;
  const userId = await getCurrentUserId(req);

  // Verify user owns this output
  const output = await supabase
    .from('video_outputs')
    .select('*, video_jobs!inner(user_id, expires_at)')
    .eq('id', outputId)
    .eq('video_jobs.user_id', userId)
    .single();

  if (!output.data) {
    return res.status(404).json({ error: 'Output not found' });
  }

  // Check if expired
  if (new Date(output.data.video_jobs.expires_at) < new Date()) {
    return res.status(410).json({ error: 'File has expired' });
  }

  // Generate presigned download URL (1 hour expiry)
  const presignedUrl = await s3Client.getSignedUrl(GetObjectCommand, {
    Bucket: process.env.S3_BUCKET,
    Key: output.data.s3_output_key,
    ResponseContentDisposition: `attachment; filename="${output.data.filename}"`
  }, { expiresIn: 3600 });

  res.json({ downloadUrl: presignedUrl });
}

// POST /api/download/bulk - Generate bulk download ZIP
```

#### Worker Communication APIs
```typescript
// POST /api/worker/job-update - Update job status from worker
export async function POST(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  const expectedToken = process.env.WORKER_SECRET_TOKEN;

  if (authHeader !== `Bearer ${expectedToken}`) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { jobId, status, progress, error } = await request.json();

  await supabase
    .from('video_jobs')
    .update({ status, progress, error_message: error })
    .eq('id', jobId);

  return NextResponse.json({ success: true });
}

// POST /api/worker/job-complete - Mark job as complete
```

### API Response Formats

#### Standard Response Wrapper
```typescript
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

#### Job Status Response
```typescript
interface JobStatusResponse {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number; // 0-100
  current_step: string;
  outputs: {
    platform_id: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    file_size?: number;
    download_url?: string;
    error_message?: string;
  }[];
  estimated_completion?: string;
  error_message?: string;
}
```

### Rate Limiting & Security
- **Authentication**: All protected routes require valid Clerk session
- **Rate Limiting**: 100 requests per minute per user
- **File Validation**: Type, size, and content validation
- **CORS**: Configured for frontend domain only
- **Webhook Security**: Signature verification for all webhooks

## Worker Service Architecture

### BullMQ Job Processing Flow

#### Job Queue Configuration
```typescript
const videoProcessingQueue = new Queue('video-processing', {
  connection: redis,
  defaultJobOptions: {
    removeOnComplete: 10,
    removeOnFail: 5,
    delay: 1000, // 1 second delay before processing
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 5000,
    },
  },
});
```

#### Worker Implementation
```typescript
const worker = new Worker(
  'video-processing',
  async (job) => {
    const { jobId, inputS3Key, selectedPlatforms, platformRules } = job.data;

    console.log(`🎬 Processing job ${jobId} with ${selectedPlatforms.length} platforms`);

    try {
      // Update job status to processing
      await updateJobStatus(jobId, 'processing', 0);

      // Download input video from S3
      const inputPath = await downloadFromS3(inputS3Key);
      job.updateProgress(10);

      // Extract video metadata
      const metadata = await getVideoMetadata(inputPath);
      job.updateProgress(15);

      // Process video for each target platform
      const outputs = [];
      const totalPlatforms = selectedPlatforms.length;

      for (let i = 0; i < totalPlatforms; i++) {
        const platform = selectedPlatforms[i];
        const baseProgress = 15 + (i * 70 / totalPlatforms);

        // Get platform processing options
        const processingOptions = PlatformRulesService.getProcessingOptions(platform);

        // Generate output filename and path
        const outputFilename = `${jobId}_${platform}.mp4`;
        const outputPath = path.join(tempDir, outputFilename);

        // Process video with FFmpeg
        await processVideo(inputPath, outputPath, processingOptions, (progress) => {
          const totalProgress = baseProgress + (progress * 70 / totalPlatforms / 100);
          job.updateProgress(Math.round(totalProgress));
        });

        // Upload processed video to S3
        const outputS3Key = await uploadToS3(outputPath, `outputs/${jobId}/${outputFilename}`);

        // Update database with output details
        await createVideoOutput({
          jobId,
          platformId: platform,
          s3OutputKey: outputS3Key,
          filename: outputFilename,
          fileSizeBytes: getFileSize(outputPath),
          status: 'completed'
        });

        outputs.push({ platform, s3Key: outputS3Key });
      }

      // Mark job as completed
      await updateJobStatus(jobId, 'completed', 100);

      // Schedule cleanup job for 3 days later
      await scheduleCleanupJob(jobId, new Date(Date.now() + 3 * 24 * 60 * 60 * 1000));

    } catch (error) {
      console.error(`❌ Job ${jobId} failed:`, error);
      await updateJobStatus(jobId, 'failed', 0, error.message);
      throw error;
    } finally {
      // Cleanup temporary files
      await cleanupTempFiles(inputPath);
    }
  },
  {
    connection: redis,
    concurrency: 3, // Process 3 jobs simultaneously
    removeOnComplete: 10,
    removeOnFail: 50,
  }
);
```

### FFmpeg Video Processing Pipeline

#### Video Processing Service
```typescript
export class VideoProcessor {
  async processVideo(
    inputPath: string,
    outputPath: string,
    options: ProcessingOptions,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    console.log(`🎬 Processing video for ${options.platform}`);

    // Ensure output directory exists
    await fs.ensureDir(path.dirname(outputPath));

    return new Promise((resolve, reject) => {
      let command = ffmpeg(inputPath);

      // Video codec and quality settings
      command = command
        .videoCodec(options.videoCodec)
        .audioCodec(options.audioCodec)
        .videoBitrate(options.videoBitrate)
        .audioBitrate(options.audioBitrate)
        .fps(options.framerate);

      // Resolution and aspect ratio
      const { width, height } = options.resolution;
      command = command.size(`${width}x${height}`);

      // Aspect ratio handling
      if (options.aspectRatio === '9:16') {
        // Vertical video - crop or pad as needed
        command = command.aspect('9:16');
      } else if (options.aspectRatio === '16:9') {
        // Horizontal video
        command = command.aspect('16:9');
      } else if (options.aspectRatio === '1:1') {
        // Square video
        command = command.aspect('1:1');
      }

      // Progress tracking
      command.on('progress', (progress) => {
        if (onProgress && progress.percent) {
          onProgress(Math.min(90, progress.percent)); // Max 90% during processing
        }
      });

      // Error handling
      command.on('error', (err) => {
        console.error(`❌ FFmpeg error for ${options.platform}:`, err);
        reject(err);
      });

      // Completion handling
      command.on('end', () => {
        console.log(`✅ Video processing completed for ${options.platform}`);
        if (onProgress) onProgress(100);
        resolve();
      });

      // Start processing
      command.save(outputPath);
    });
  }

  async getVideoMetadata(inputPath: string): Promise<VideoMetadata> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }

        const videoStream = metadata.streams.find(s => s.codec_type === 'video');
        const audioStream = metadata.streams.find(s => s.codec_type === 'audio');

        resolve({
          duration: metadata.format.duration,
          width: videoStream.width,
          height: videoStream.height,
          aspectRatio: videoStream.width / videoStream.height,
          videoCodec: videoStream.codec_name,
          audioCodec: audioStream?.codec_name,
          frameRate: this.parseFrameRate(videoStream.r_frame_rate),
          bitrate: parseInt(metadata.format.bit_rate)
        });
      });
    });
  }
}
```

#### Platform-Specific FFmpeg Commands
```bash
# TikTok (9:16, 1080x1920, H.264/AAC)
ffmpeg -i input.mp4 \
  -vf "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:black" \
  -c:v libx264 \
  -preset medium \
  -crf 23 \
  -b:v 2500k \
  -maxrate 2500k \
  -bufsize 5000k \
  -c:a aac \
  -b:a 128k \
  -ar 44100 \
  -r 30 \
  -movflags +faststart \
  output_tiktok_high.mp4

# Instagram Reel (9:16, 1080x1920, H.264/AAC)
ffmpeg -i input.mp4 \
  -vf "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:black" \
  -c:v libx264 \
  -preset medium \
  -crf 21 \
  -b:v 3500k \
  -maxrate 3500k \
  -bufsize 7000k \
  -c:a aac \
  -b:a 192k \
  -ar 44100 \
  -r 30 \
  -movflags +faststart \
  output_instagram_reel.mp4

# YouTube Shorts (9:16, 1080x1920, H.264/AAC)
ffmpeg -i input.mp4 \
  -vf "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:black" \
  -c:v libx264 \
  -preset medium \
  -crf 20 \
  -b:v 4000k \
  -maxrate 4000k \
  -bufsize 8000k \
  -c:a aac \
  -b:a 192k \
  -ar 44100 \
  -r 30 \
  -movflags +faststart \
  output_youtube_shorts.mp4
```

### S3 Service Integration
```typescript
export class S3Service {
  async downloadFile(s3Key: string, localPath: string): Promise<void> {
    console.log(`📥 Downloading ${s3Key} to ${localPath}`);

    const command = new GetObjectCommand({
      Bucket: config.aws.bucketName,
      Key: s3Key,
    });

    const response = await this.s3Client.send(command);

    // Convert stream to buffer and write to file
    const chunks = [];
    for await (const chunk of response.Body as any) {
      chunks.push(chunk);
    }
    const buffer = Buffer.concat(chunks);

    await fs.ensureDir(path.dirname(localPath));
    await fs.writeFile(localPath, buffer);

    console.log(`✅ Successfully downloaded ${s3Key}`);
  }

  async uploadFile(localPath: string, s3Key: string, contentType = 'video/mp4'): Promise<string> {
    console.log(`📤 Uploading ${localPath} to S3 as ${s3Key}`);

    const fileStream = fs.createReadStream(localPath);
    const stats = await fs.stat(localPath);

    const command = new PutObjectCommand({
      Bucket: config.aws.bucketName,
      Key: s3Key,
      Body: fileStream,
      ContentType: contentType,
      ContentLength: stats.size,
    });

    await this.s3Client.send(command);

    const s3Url = `https://${config.aws.bucketName}.s3.${config.aws.region}.amazonaws.com/${s3Key}`;
    console.log(`✅ Successfully uploaded to ${s3Url}`);

    return s3Url;
  }
}
```

### Error Handling & Retry Logic
```typescript
// Job retry configuration
const retryConfig = {
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 5000, // Start with 5 second delay
  },
};

// Error categorization
enum ErrorType {
  NETWORK_ERROR = 'network_error',
  FILE_ERROR = 'file_error',
  PROCESSING_ERROR = 'processing_error',
  TIMEOUT_ERROR = 'timeout_error',
}

// Retry logic based on error type
function shouldRetry(error: Error, attemptNumber: number): boolean {
  if (attemptNumber >= 3) return false;

  if (error.message.includes('ECONNRESET') || error.message.includes('timeout')) {
    return true; // Retry network/timeout errors
  }

  if (error.message.includes('Invalid video format')) {
    return false; // Don't retry format errors
  }

  return true; // Retry other errors
}
```

## Platform Rules Engine

### Video Classification Logic
The system automatically classifies videos into two form types based on duration and aspect ratio:

```typescript
function classifyVideoForm(duration_seconds: number, height: number, width: number): FormType {
  const aspectRatio = height / width;

  // Short-form criteria: ≤120 seconds AND vertical aspect ratio ≥1.6
  if (duration_seconds <= 120 && aspectRatio >= 1.6) {
    return 'short_form';
  }

  // Everything else is long-form
  return 'long_form';
}
```

### Platform Rules Structure
The `video_platforms_rules_updated.json` file defines comprehensive platform specifications:

```json
{
  "metadata": {
    "version": "2.0",
    "form_type_support": true,
    "repurposing_mode": "same_form_only"
  },
  "form_classification": {
    "logic": {
      "short_form": {
        "duration_seconds": "≤ 120",
        "aspect_ratio_height_width": "≥ 1.6"
      },
      "long_form": {
        "criteria": "All content that doesn't meet short_form criteria"
      }
    }
  },
  "platforms": {
    "tiktok": {
      "form_types": {
        "short_form": {
          "technical_specifications": {
            "aspect_ratios": { "recommended": "9:16" },
            "resolution": { "recommended": "1080x1920" },
            "duration": { "maximum": "120 seconds" },
            "video_codec": "H.264",
            "audio_codec": "AAC"
          },
          "conversion_presets": {
            "quality_high": {
              "resolution": "1080x1920",
              "bitrate": "2500 kbps",
              "frame_rate": "30",
              "audio_bitrate": "128 kbps"
            }
          }
        }
      }
    }
  }
}
```

### Platform Compatibility Matrix
```typescript
const FORM_COMPATIBILITY_MATRIX = {
  short_form_platforms: [
    'tiktok',
    'instagram_reels',
    'youtube_shorts',
    'facebook_reels',
    'snapchat_story',
    'twitter_short'
  ],
  long_form_platforms: [
    'youtube',
    'instagram_feed',
    'linkedin',
    'facebook_feed',
    'twitter_video'
  ]
};

// Same-form enforcement
function validatePlatformCompatibility(formType: FormType, selectedPlatforms: string[]): ValidationResult {
  const compatiblePlatforms = FORM_COMPATIBILITY_MATRIX[`${formType}_platforms`];

  const validPlatforms = selectedPlatforms.filter(platform =>
    compatiblePlatforms.includes(platform)
  );

  const invalidPlatforms = selectedPlatforms.filter(platform =>
    !compatiblePlatforms.includes(platform)
  );

  return {
    valid: invalidPlatforms.length === 0,
    validPlatforms,
    invalidPlatforms,
    errors: invalidPlatforms.map(p => `${p} not compatible with ${formType}`)
  };
}
```

### Supported Platforms & Specifications

#### Short-Form Platforms
- **TikTok**: 9:16, 1080x1920, max 120s, H.264/AAC, 2500 kbps
- **Instagram Reels**: 9:16, 1080x1920, max 90s, H.264/AAC, 3500 kbps
- **YouTube Shorts**: 9:16, 1080x1920, max 60s, H.264/AAC, 4000 kbps
- **Facebook Reels**: 9:16, 1080x1920, max 120s, H.264/AAC, 2500 kbps
- **Snapchat Story**: 9:16, 1080x1920, max 180s, H.264/AAC, 2000 kbps
- **Twitter Short**: 9:16/1:1, 1080x1920, max 140s, H.264/AAC, 2500 kbps

#### Long-Form Platforms
- **YouTube**: 16:9, 1920x1080, up to 12 hours, H.264/AAC, 5000 kbps
- **Instagram Feed**: 1:1/4:5, 1080x1080, up to 60 minutes, H.264/AAC, 3500 kbps
- **LinkedIn**: 16:9/1:1, 1920x1080, max 10 minutes, H.264/AAC, 4000 kbps
- **Facebook Feed**: 1:1/4:5, 1080x1080, max 240 minutes, H.264/AAC, 3000 kbps

## Frontend Components

### Main Dashboard Structure
```typescript
// app/dashboard/page.tsx
export default function DashboardPage() {
  const { user } = useUser();
  const { jobs, loading, error, refreshJobs, addJob } = useRealtimeJobs();

  return (
    <div className="min-h-screen bg-background">
      <nav className="border-b bg-background/95 backdrop-blur">
        {/* Navigation with user info, stats, and logout */}
      </nav>

      <main className="container mx-auto p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload">Upload Video</TabsTrigger>
            <TabsTrigger value="jobs">Job Status</TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="upload">
            <VideoUploadComponent onUploadSuccess={(job) => {
              addJob(job);
              setActiveTab("jobs");
            }} />
          </TabsContent>

          <TabsContent value="jobs">
            <JobStatusList jobs={jobs} loading={loading} />
          </TabsContent>

          <TabsContent value="profile">
            <UserProfileComponent />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
```

### Video Upload Component
```typescript
// components/video-upload.tsx
export function VideoUploadComponent({ onUploadSuccess }: { onUploadSuccess: (job: any) => void }) {
  const [file, setFile] = useState<File | null>(null);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileSelection = (selectedFile: File) => {
    // Validate file type
    const validTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/mkv', 'video/webm'];
    if (!validTypes.includes(selectedFile.type)) {
      toast.error("Please select a valid video file");
      return;
    }

    // Validate file size (5GB limit)
    const maxSize = 5 * 1024 * 1024 * 1024;
    if (selectedFile.size > maxSize) {
      toast.error("File size must be less than 5GB");
      return;
    }

    setFile(selectedFile);
  };

  const handleUpload = async () => {
    if (!file || selectedPlatforms.length === 0) return;

    setIsUploading(true);

    try {
      // Get presigned URL
      const presignedResponse = await fetch('/api/upload/presigned', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
        }),
      });

      const { uploadUrl, key, jobId } = await presignedResponse.json();

      // Upload to S3 with progress tracking
      const xhr = new XMLHttpRequest();

      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 80; // Reserve 20% for processing
          setUploadProgress(progress);
        }
      };

      await new Promise((resolve, reject) => {
        xhr.onload = () => resolve(xhr);
        xhr.onerror = () => reject(new Error('Upload failed'));
        xhr.open('PUT', uploadUrl);
        xhr.setRequestHeader('Content-Type', file.type);
        xhr.send(file);
      });

      // Create processing job
      const jobResponse = await fetch('/api/jobs/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobId,
          fileName: file.name,
          s3Key: key,
          platforms: selectedPlatforms,
        }),
      });

      const job = await jobResponse.json();

      setUploadProgress(100);
      toast.success("Video uploaded successfully!");
      onUploadSuccess(job);

      // Reset form
      setFile(null);
      setSelectedPlatforms([]);
      setUploadProgress(0);

    } catch (error) {
      console.error('Upload error:', error);
      toast.error("Upload failed. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Upload Video</CardTitle>
        <CardDescription>
          Select a video file to repurpose for multiple social media platforms. Maximum file size: 5GB.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Drag & drop file upload area */}
        <div className="border-2 border-dashed rounded-lg p-8 text-center">
          {file ? (
            <div className="space-y-4">
              <FileVideo className="h-12 w-12 text-primary mx-auto" />
              <div>
                <p className="font-medium">{file.name}</p>
                <p className="text-sm text-gray-500">
                  {(file.size / (1024 * 1024)).toFixed(2)} MB
                </p>
              </div>
              <Button variant="outline" onClick={() => setFile(null)}>
                Remove File
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <Video className="h-12 w-12 text-gray-400 mx-auto" />
              <div>
                <p className="text-lg font-medium">Drop your video here</p>
                <p className="text-gray-500">or click to browse files</p>
              </div>
              <Button variant="outline" onClick={() => fileInputRef.current?.click()}>
                Choose File
              </Button>
            </div>
          )}
        </div>

        {/* Platform selection */}
        {file && (
          <div className="mt-6">
            <h3 className="text-lg font-medium mb-4">Select Target Platforms</h3>
            <PlatformSelector
              selectedPlatforms={selectedPlatforms}
              onPlatformChange={setSelectedPlatforms}
            />
          </div>
        )}

        {/* Upload progress */}
        {isUploading && (
          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Uploading...</span>
              <span className="text-sm text-gray-500">{uploadProgress.toFixed(0)}%</span>
            </div>
            <Progress value={uploadProgress} className="w-full" />
          </div>
        )}

        {/* Upload button */}
        <div className="mt-6">
          <Button
            onClick={handleUpload}
            disabled={!file || selectedPlatforms.length === 0 || isUploading}
            className="w-full"
          >
            {isUploading ? 'Uploading...' : 'Upload and Process Video'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
```

### Job Status Monitoring Component
```typescript
// components/job-status-list.tsx
export function JobStatusList({ jobs, loading }: { jobs: VideoJob[], loading: boolean }) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
      case 'queued':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
      case 'queued':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (jobs.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <FileVideo className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No videos uploaded yet</h3>
          <p className="text-gray-500">Upload your first video to get started with repurposing.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {jobs.map((job) => (
        <Card key={job.id} className="overflow-hidden">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <FileVideo className="h-5 w-5 text-primary" />
                <div>
                  <CardTitle className="text-lg">{job.originalFileName}</CardTitle>
                  <CardDescription className="flex items-center space-x-4 mt-1">
                    <span className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(job.createdAt)}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Smartphone className="h-3 w-3" />
                      <span>{job.selectedPlatforms.length} platforms</span>
                    </span>
                  </CardDescription>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {getStatusIcon(job.status)}
                <Badge className={getStatusColor(job.status)}>
                  {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                </Badge>
              </div>
            </div>
          </CardHeader>

          <CardContent>
            {/* Progress bar for processing jobs */}
            {job.status === 'processing' && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Processing...</span>
                  <span className="text-sm text-gray-500">{job.progress}%</span>
                </div>
                <Progress value={job.progress} className="w-full" />
              </div>
            )}

            {/* Error message for failed jobs */}
            {job.status === 'failed' && job.errorMessage && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-800">{job.errorMessage}</p>
              </div>
            )}

            {/* Platform outputs for completed jobs */}
            {job.status === 'completed' && job.outputs && job.outputs.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">Download Processed Videos</h4>
                <div className="grid gap-2 sm:grid-cols-2 lg:grid-cols-3">
                  {job.outputs.map((output) => (
                    <div key={output.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Download className="h-4 w-4 text-gray-500" />
                        <span className="text-sm font-medium">{output.platformId}</span>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => downloadFile(output.id)}
                      >
                        Download
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* File expiration warning */}
            {job.status === 'completed' && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm text-yellow-800">
                    Files will be automatically deleted on {formatDate(job.expiresAt)}
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
```

### Real-time Job Updates Hook
```typescript
// hooks/useRealtimeJobs.ts
export function useRealtimeJobs() {
  const [jobs, setJobs] = useState<VideoJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchJobs = useCallback(async () => {
    try {
      const response = await fetch('/api/jobs');
      if (!response.ok) throw new Error('Failed to fetch jobs');

      const data = await response.json();
      setJobs(data.jobs || []);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, []);

  // Set up Server-Sent Events for real-time updates
  useEffect(() => {
    const eventSource = new EventSource('/api/jobs/stream');

    eventSource.onmessage = (event) => {
      const updatedJob = JSON.parse(event.data);

      setJobs(prevJobs =>
        prevJobs.map(job =>
          job.id === updatedJob.id ? { ...job, ...updatedJob } : job
        )
      );
    };

    eventSource.onerror = () => {
      setError('Connection to real-time updates lost');
    };

    return () => {
      eventSource.close();
    };
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchJobs();
  }, [fetchJobs]);

  // Polling fallback for real-time updates
  useEffect(() => {
    const interval = setInterval(fetchJobs, 10000); // Poll every 10 seconds
    return () => clearInterval(interval);
  }, [fetchJobs]);

  const addJob = useCallback((newJob: VideoJob) => {
    setJobs(prevJobs => [newJob, ...prevJobs]);
  }, []);

  const refreshJobs = useCallback(() => {
    setLoading(true);
    fetchJobs();
  }, [fetchJobs]);

  return {
    jobs,
    loading,
    error,
    refreshJobs,
    addJob,
  };
}
```

## Payment Integration

### Dual Payment Provider System
The application supports both Stripe (global) and Razorpay (India) for comprehensive payment coverage.

#### Payment Provider Selection Component
```typescript
// components/payment/PaymentProviderSelector.tsx
export default function PaymentProviderSelector({
  onProviderSelect,
  selectedProvider,
  loading = false
}: PaymentProviderSelectorProps) {
  const paymentProviders = [
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Secure international payments',
      currency: 'USD',
      amount: '$30/month',
      features: ['Credit/Debit Cards', 'International', 'Apple Pay', 'Google Pay'],
      region: 'Global'
    },
    {
      id: 'razorpay',
      name: 'Razorpay',
      description: 'Comprehensive India payments',
      currency: 'INR',
      amount: '₹2,500/month',
      features: ['UPI', 'Net Banking', 'Cards', 'Wallets', 'EMI Options'],
      region: 'India'
    }
  ];

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold mb-2">Choose Payment Method</h3>
        <p className="text-muted-foreground">
          Select your preferred payment provider to continue with subscription
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {paymentProviders.map((provider) => (
          <Card
            key={provider.id}
            className={`cursor-pointer transition-all hover:shadow-md ${
              selectedProvider === provider.id
                ? 'ring-2 ring-primary border-primary'
                : 'hover:border-primary/50'
            }`}
            onClick={() => onProviderSelect(provider.id)}
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{provider.name}</CardTitle>
                <Badge variant="secondary">{provider.region}</Badge>
              </div>
              <CardDescription>{provider.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-2xl font-bold text-primary">
                  {provider.amount}
                </div>
                <div className="space-y-1">
                  {provider.features.map((feature) => (
                    <div key={feature} className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
```

### Stripe Integration Flow
```typescript
// Stripe checkout session creation
export async function createStripeCheckout(userId: string) {
  const session = await stripe.checkout.sessions.create({
    customer_email: await getUserEmail(userId),
    line_items: [
      {
        price: process.env.STRIPE_PRICE_ID, // $30/month price
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?payment=success`,
    cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/pricing?payment=cancelled`,
    metadata: { userId },
    subscription_data: { metadata: { userId } },
    allow_promotion_codes: true,
    billing_address_collection: 'required',
  });

  return session.url;
}

// Stripe webhook handler
export async function handleStripeWebhook(event: Stripe.Event) {
  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object as Stripe.Checkout.Session;
      const userId = session.metadata?.userId;

      if (userId) {
        const subscription = await stripe.subscriptions.retrieve(session.subscription as string);

        await supabase
          .from('users')
          .update({
            subscription_status: 'active',
            subscription_id: subscription.id,
            current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            stripe_customer_id: subscription.customer as string,
          })
          .eq('clerk_user_id', userId);
      }
      break;

    case 'invoice.payment_succeeded':
      // Handle successful recurring payments
      break;

    case 'customer.subscription.deleted':
      // Handle subscription cancellation
      break;
  }
}
```

### Razorpay Integration Flow
```typescript
// Razorpay order creation
export async function createRazorpayOrder(userId: string) {
  const options = {
    amount: 250000, // ₹2,500 in paise
    currency: 'INR',
    receipt: `order_${userId}_${Date.now()}`,
    notes: {
      user_id: userId,
      subscription_type: 'monthly',
      plan: 'autorepurpose_monthly'
    }
  };

  const order = await razorpay.orders.create(options);
  return order;
}

// Razorpay payment verification
export async function verifyRazorpayPayment(
  razorpay_order_id: string,
  razorpay_payment_id: string,
  razorpay_signature: string
) {
  const hmac = crypto.createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!);
  hmac.update(`${razorpay_order_id}|${razorpay_payment_id}`);
  const generatedSignature = hmac.digest('hex');

  if (generatedSignature !== razorpay_signature) {
    throw new Error('Invalid payment signature');
  }

  return true;
}

// Razorpay webhook handler
export async function handleRazorpayWebhook(event: any) {
  switch (event.event) {
    case 'payment.captured':
      // Handle successful payment
      const payment = event.payload.payment.entity;
      const userId = payment.notes.user_id;

      if (userId) {
        await supabase
          .from('users')
          .update({
            subscription_status: 'active',
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            razorpay_customer_id: payment.customer_id,
          })
          .eq('clerk_user_id', userId);
      }
      break;

    case 'subscription.cancelled':
      // Handle subscription cancellation
      break;
  }
}
```

### Usage Tracking & Limits
```typescript
// Usage management service
export class UsageManager {
  static async checkUsageLimit(userId: string): Promise<UsageLimitResult> {
    const user = await supabase
      .from('users')
      .select('subscription_status, free_repurposings_used, current_period_end')
      .eq('clerk_user_id', userId)
      .single();

    if (!user.data) {
      throw new Error('User not found');
    }

    const { subscription_status, free_repurposings_used, current_period_end } = user.data;

    // Check subscription status
    if (subscription_status === 'trial') {
      const hasFreesLeft = free_repurposings_used < 1;
      return {
        allowed: hasFreesLeft,
        reason: hasFreesLeft ? undefined : 'Free trial limit reached',
        remainingFree: Math.max(0, 1 - free_repurposings_used),
        requiresSubscription: !hasFreesLeft
      };
    }

    if (subscription_status === 'active') {
      // Check if subscription is still valid
      const isActive = current_period_end && new Date(current_period_end) > new Date();
      return {
        allowed: isActive,
        reason: isActive ? undefined : 'Subscription expired',
        isSubscriber: true,
        subscriptionExpired: !isActive
      };
    }

    return {
      allowed: false,
      reason: 'No valid subscription or trial',
      requiresSubscription: true
    };
  }

  static async consumeFreeRepurposing(userId: string): Promise<void> {
    await supabase.rpc('increment_free_usage', { user_id: userId });
  }
}
```

## File Management & Storage

### S3 Storage Architecture
```
auto-repurpose/
├── uploads/                     # Original uploaded videos
│   └── user-{userId}/
│       └── {jobId}/
│           └── original.{ext}   # Original file with metadata
├── outputs/                     # Processed videos
│   └── user-{userId}/
│       └── {jobId}/
│           ├── tiktok_high.mp4
│           ├── instagram_reel.mp4
│           └── youtube_shorts.mp4
├── temp/                        # Temporary processing files
│   └── worker-{workerId}/
│       └── {jobId}/
└── archive/                     # Backup before deletion (optional)
```

### File Lifecycle Management
```typescript
// File retention service
export class FileRetentionService {
  static async scheduleCleanup(jobId: string, expirationDate: Date) {
    // Schedule cleanup job in BullMQ
    await cleanupQueue.add(
      'cleanup-job-files',
      { jobId },
      { delay: expirationDate.getTime() - Date.now() }
    );
  }

  static async cleanupExpiredFiles() {
    const expiredJobs = await supabase
      .from('video_jobs')
      .select(`
        id,
        s3_input_key,
        video_outputs (s3_output_key)
      `)
      .lt('expires_at', new Date().toISOString());

    for (const job of expiredJobs.data || []) {
      try {
        // Delete original file
        await s3Client.deleteObject({
          Bucket: process.env.S3_BUCKET_NAME,
          Key: job.s3_input_key
        });

        // Delete all output files
        for (const output of job.video_outputs) {
          await s3Client.deleteObject({
            Bucket: process.env.S3_BUCKET_NAME,
            Key: output.s3_output_key
          });
        }

        // Update job status
        await supabase
          .from('video_jobs')
          .update({ status: 'expired' })
          .eq('id', job.id);

        console.log(`✅ Cleaned up files for job ${job.id}`);

      } catch (error) {
        console.error(`❌ Failed to cleanup job ${job.id}:`, error);
      }
    }
  }
}

// Automated cleanup cron job
cron.schedule('0 2 * * *', async () => { // Daily at 2 AM
  console.log('🧹 Running daily file cleanup...');
  await FileRetentionService.cleanupExpiredFiles();
});
```

### Secure File Access
```typescript
// Download service with authentication
export class DownloadService {
  static async generateDownloadUrl(outputId: string, userId: string): Promise<string> {
    // Verify user owns this output
    const output = await supabase
      .from('video_outputs')
      .select(`
        *,
        video_jobs!inner (
          user_id,
          expires_at
        )
      `)
      .eq('id', outputId)
      .eq('video_jobs.user_id', userId)
      .single();

    if (!output.data) {
      throw new Error('Output not found or access denied');
    }

    // Check if expired
    if (new Date(output.data.video_jobs.expires_at) < new Date()) {
      throw new Error('File has expired');
    }

    // Generate presigned download URL (1 hour expiry)
    const presignedUrl = await getSignedUrl(
      s3Client,
      new GetObjectCommand({
        Bucket: process.env.S3_BUCKET_NAME,
        Key: output.data.s3_output_key,
        ResponseContentDisposition: `attachment; filename="${output.data.filename}"`
      }),
      { expiresIn: 3600 }
    );

    // Log download activity
    await supabase.from('usage_logs').insert({
      user_id: userId,
      action: 'download',
      details: { outputId, filename: output.data.filename }
    });

    return presignedUrl;
  }
}
```

## Authentication & Security

### Clerk Authentication Integration
```typescript
// Middleware for protected routes
export default clerkMiddleware(async (auth, req) => {
  // Check if Clerk is properly configured
  const isClerkConfigured = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY &&
    !process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY.includes('placeholder');

  // In development mode without proper Clerk config, allow all routes
  if (!isClerkConfigured) {
    return NextResponse.next();
  }

  // Allow public routes to pass through
  if (isPublicRoute(req)) {
    return NextResponse.next();
  }

  try {
    const { userId } = await auth();

    // For protected pages, redirect to sign-in if not authenticated
    if (!userId) {
      const signInUrl = new URL('/sign-in', req.url);
      signInUrl.searchParams.set('redirect_url', req.url);
      return NextResponse.redirect(signInUrl);
    }
  } catch (error) {
    // If auth fails, redirect to sign-in
    const signInUrl = new URL('/sign-in', req.url);
    signInUrl.searchParams.set('redirect_url', req.url);
    return NextResponse.redirect(signInUrl);
  }

  return NextResponse.next();
});
```

### User Synchronization
```typescript
// Clerk webhook handler for user sync
export async function POST(request: NextRequest) {
  const webhook = new Webhook(process.env.CLERK_WEBHOOK_SECRET);

  try {
    const evt = webhook.verify(req.body, req.headers);

    switch (evt.type) {
      case 'user.created':
        await createUserRecord(evt.data);
        break;

      case 'user.updated':
        await updateUserRecord(evt.data);
        break;

      case 'user.deleted':
        await deleteUserRecord(evt.data);
        break;
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: 'Webhook verification failed' }, { status: 400 });
  }
}

async function createUserRecord(clerkUser: any) {
  await supabase.from('users').insert({
    clerk_user_id: clerkUser.id,
    email: clerkUser.email_addresses[0]?.email_address,
    full_name: `${clerkUser.first_name} ${clerkUser.last_name}`.trim(),
    subscription_status: 'trial',
    free_repurposings_used: 0
  });
}
```

### Security Measures
- **Authentication**: All protected routes require valid Clerk session
- **Authorization**: Users can only access their own files and data
- **File Validation**: Type, size, and content validation on upload
- **Webhook Security**: Signature verification for all external webhooks
- **Rate Limiting**: API rate limiting to prevent abuse
- **CORS**: Configured for frontend domain only
- **Environment Variables**: Sensitive data stored securely
- **Presigned URLs**: Temporary, secure file access
- **Database Security**: Row-level security policies in Supabase

## Deployment & Infrastructure

### Production Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vercel        │    │   Railway       │    │   AWS S3        │
│   (Frontend)    │    │   (Worker)      │    │   (Storage)     │
│                 │    │                 │    │                 │
│ • Next.js App   │    │ • Video Worker  │    │ • Original      │
│ • API Routes    │    │ • FFmpeg        │    │ • Processed     │
│ • Static Assets │    │ • Job Queue     │    │ • Temp Files    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Supabase      │    │   Upstash       │    │   Clerk         │
│   (Database)    │    │   (Redis)       │    │   (Auth)        │
│                 │    │                 │    │                 │
│ • PostgreSQL    │    │ • Job Queue     │    │ • User Auth     │
│ • Real-time     │    │ • Session Cache │    │ • Webhooks      │
│ • Backups       │    │ • Rate Limiting │    │ • Social Login  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Environment Configuration

#### Production Environment Variables
```env
# Frontend (Vercel)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_...
CLERK_SECRET_KEY=sk_live_...
CLERK_WEBHOOK_SECRET=whsec_...

DATABASE_URL=postgresql://...
NEXT_PUBLIC_SUPABASE_URL=https://...supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiI...

AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=twF21iE+cHN8/P4lzbz3+fuFZLDKAvv2fIxQ8/19
S3_BUCKET_NAME=auto-repurpose

REDIS_URL=rediss://default:...@...upstash.io:6379

STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PRICE_ID=price_...

RAZORPAY_KEY_ID=rzp_live_...
RAZORPAY_KEY_SECRET=...
RAZORPAY_WEBHOOK_SECRET=...

NEXT_PUBLIC_APP_URL=https://autorepurpose.com

# Worker (Railway)
REDIS_URL=rediss://default:...@...upstash.io:6379
AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=twF21iE+cHN8/P4lzbz3+fuFZLDKAvv2fIxQ8/19
S3_BUCKET_NAME=auto-repurpose
WORKER_CONCURRENCY=3
NODE_ENV=production
```

### Deployment Process

#### Frontend Deployment (Vercel)
1. **GitHub Integration**: Auto-deploy on push to main branch
2. **Build Process**: `npm run build` with Next.js optimization
3. **Environment Variables**: Set in Vercel dashboard
4. **Domain Configuration**: Custom domain with SSL
5. **Edge Functions**: API routes deployed as serverless functions

#### Worker Deployment (Railway)
1. **GitHub Integration**: Auto-deploy on push to worker branch
2. **Docker Build**: Nixpacks auto-detection with FFmpeg installation
3. **Environment Variables**: Set in Railway dashboard
4. **Health Checks**: `/health` endpoint monitoring
5. **Auto-restart**: On failure with exponential backoff

### Monitoring & Logging
- **Application Monitoring**: Vercel Analytics and Railway metrics
- **Error Tracking**: Console logging with structured error handling
- **Performance Monitoring**: API response times and worker processing times
- **Usage Analytics**: User activity and conversion tracking
- **Health Checks**: Automated service health monitoring

### Backup & Recovery
- **Database Backups**: Automatic Supabase backups
- **File Backups**: S3 versioning and lifecycle policies
- **Configuration Backups**: Environment variables documented
- **Disaster Recovery**: Multi-region deployment capability

## Development Workflow

### Local Development Setup
```bash
# Clone repository
git clone <repository-url>
cd autorepurpose-saas

# Frontend setup
cd app
npm install
cp .env.example .env.local
# Fill in environment variables
npm run dev

# Worker setup (separate terminal)
cd ../worker
npm install
cp .env.example .env
# Fill in environment variables
npm run dev
```

### Development Environment Requirements
- **Node.js**: 18+ for both frontend and worker
- **FFmpeg**: Required for local video processing testing
- **Docker**: Optional for containerized development
- **Git**: Version control with environment variable management

### Testing Strategy
- **Unit Tests**: Component and utility function testing
- **Integration Tests**: API endpoint testing
- **End-to-End Tests**: Full user workflow testing
- **Performance Tests**: Video processing and upload testing
- **Security Tests**: Authentication and authorization testing

### Code Quality
- **TypeScript**: Strict type checking throughout
- **ESLint**: Code linting and formatting
- **Prettier**: Code formatting consistency
- **Husky**: Pre-commit hooks for quality checks

This comprehensive documentation provides a complete understanding of the AutoRepurpose SaaS platform, from high-level architecture to implementation details. The system is designed for scalability, reliability, and maintainability, with clear separation of concerns and robust error handling throughout.
