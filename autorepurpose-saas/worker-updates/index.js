
import { Worker } from 'bullmq';
import { createClient } from '@supabase/supabase-js';
import { S3Client, GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import Redis from 'ioredis';
import ffmpeg from 'fluent-ffmpeg';
import { promises as fs } from 'fs';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';
import { createServer } from 'http';

// Load environment variables
dotenv.config();

// Initialize services
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

const redis = new Redis(process.env.REDIS_URL, {
  maxRetriesPerRequest: null, // Required by BullMQ
});

// Configuration
const WORKER_CONCURRENCY = parseInt(process.env.WORKER_CONCURRENCY || '3');
const MAX_PROCESSING_TIME = parseInt(process.env.MAX_PROCESSING_TIME_MS || '60000');
const TEMP_DIR = '/tmp/autorepurpose';

// Ensure temp directory exists
await fs.mkdir(TEMP_DIR, { recursive: true });

console.log('🚀 AutoRepurpose Worker starting...');
console.log(`📊 Concurrency: ${WORKER_CONCURRENCY}`);
console.log(`⏱️  Max processing time: ${MAX_PROCESSING_TIME}ms`);

// ===== HTTP HEALTH CHECK SERVER (ADDED) =====
const PORT = process.env.PORT || 3001;

const healthServer = createServer((req, res) => {
  const { method, url } = req;
  
  // Health check endpoint
  if (method === 'GET' && url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'healthy',
      worker: 'autorepurpose-video-processor',
      timestamp: new Date().toISOString(),
      redis: redis.status,
      concurrency: WORKER_CONCURRENCY
    }));
    return;
  }

  // Default 404
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ error: 'Not Found' }));
});

healthServer.listen(PORT, () => {
  console.log(`🏥 Health check server running on port ${PORT}`);
  console.log(`🌐 Health endpoint: http://localhost:${PORT}/health`);
});

// ===== VIDEO PROCESSING FUNCTIONS =====

// Helper function to download from S3
async function downloadFromS3(s3Key, localPath) {
  console.log(`📥 Downloading ${s3Key} to ${localPath}`);
  
  const command = new GetObjectCommand({
    Bucket: process.env.S3_BUCKET_NAME,
    Key: s3Key,
  });

  const response = await s3Client.send(command);
  const chunks = [];
  
  for await (const chunk of response.Body) {
    chunks.push(chunk);
  }
  
  const buffer = Buffer.concat(chunks);
  await fs.writeFile(localPath, buffer);
  
  console.log(`✅ Downloaded ${s3Key} (${buffer.length} bytes)`);
}

// Helper function to upload to S3
async function uploadToS3(localPath, s3Key) {
  console.log(`📤 Uploading ${localPath} to ${s3Key}`);
  
  const fileBuffer = await fs.readFile(localPath);
  
  const command = new PutObjectCommand({
    Bucket: process.env.S3_BUCKET_NAME,
    Key: s3Key,
    Body: fileBuffer,
    ContentType: 'video/mp4',
  });

  await s3Client.send(command);
  console.log(`✅ Uploaded to ${s3Key} (${fileBuffer.length} bytes)`);
}

// Process single platform
async function processPlatform(jobId, inputPath, platformRule, platformId) {
  const outputFilename = `${jobId}_${platformId}.mp4`;
  const outputPath = join(TEMP_DIR, outputFilename);
  const outputS3Key = `processed/${new Date().toISOString().slice(0, 10)}/${jobId}/${outputFilename}`;

  return new Promise((resolve, reject) => {
    const { resolution, bitrate } = platformRule.output_preset;
    
    console.log(`🎬 Processing ${platformId}: ${resolution.width}x${resolution.height}`);
    
    ffmpeg(inputPath)
      .size(`${resolution.width}x${resolution.height}`)
      .videoBitrate(bitrate.video)
      .audioBitrate(bitrate.audio)
      .videoCodec('libx264')
      .audioCodec('aac')
      .on('progress', (progress) => {
        console.log(`⏳ ${platformId}: ${Math.round(progress.percent || 0)}%`);
      })
      .on('end', async () => {
        try {
          // Upload to S3
          await uploadToS3(outputPath, outputS3Key);
          
          // Create output record in database
          const { data: output, error } = await supabase
            .from('video_outputs')
            .insert({
              job_id: jobId,
              platform_id: platformId,
              preset_name: platformRule.name,
              s3_output_key: outputS3Key,
              filename: outputFilename,
              resolution: `${resolution.width}x${resolution.height}`,
              status: 'completed',
              file_size_bytes: (await fs.stat(outputPath)).size
            })
            .select()
            .single();

          if (error) throw error;

          // Clean up local file
          await fs.unlink(outputPath).catch(() => {});

          resolve({
            platformId,
            outputS3Key,
            filename: outputFilename,
            output
          });
        } catch (uploadError) {
          console.error(`❌ Upload failed for ${platformId}:`, uploadError);
          reject(uploadError);
        }
      })
      .on('error', (err) => {
        console.error(`❌ FFmpeg error for ${platformId}:`, err);
        reject(err);
      })
      .save(outputPath);
  });
}

// ===== BULLMQ WORKER =====

// Video processing worker
const worker = new Worker(
  'video-processing',
  async (job) => {
    const { jobId, inputS3Key, selectedPlatforms, platformRules } = job.data;
    
    console.log(`🎬 Processing job ${jobId} with ${selectedPlatforms.length} platforms`);
    
    try {
      // Update job status to processing
      await supabase
        .from('video_jobs')
        .update({
          status: 'processing',
          processing_started_at: new Date().toISOString(),
          queue_job_id: job.id
        })
        .eq('id', jobId);

      // Download input video from S3
      const inputPath = join(TEMP_DIR, `input_${jobId}.mp4`);
      await downloadFromS3(inputS3Key, inputPath);
      
      console.log(`📥 Downloaded input video: ${inputPath}`);

      // Process each platform
      const results = [];
      for (let i = 0; i < selectedPlatforms.length; i++) {
        const platformId = selectedPlatforms[i];
        const platformRule = platformRules[platformId];
        
        if (!platformRule) {
          console.warn(`⚠️  Platform rule not found for ${platformId}`);
          continue;
        }

        await job.updateProgress(Math.floor((i / selectedPlatforms.length) * 100));

        try {
          const result = await processPlatform(jobId, inputPath, platformRule, platformId);
          results.push(result);
          
          console.log(`✅ Processed ${platformId}: ${result.outputS3Key}`);
        } catch (error) {
          console.error(`❌ Failed to process ${platformId}:`, error);
          
          // Create failed output record
          await supabase
            .from('video_outputs')
            .insert({
              job_id: jobId,
              platform_id: platformId,
              preset_name: platformRule.name,
              s3_output_key: '',
              filename: '',
              resolution: `${platformRule.output_preset.resolution.width}x${platformRule.output_preset.resolution.height}`,
              status: 'failed',
              error_message: error.message
            });
        }
      }

      // Clean up input file
      await fs.unlink(inputPath).catch(() => {});

      // Update job status to completed
      await supabase
        .from('video_jobs')
        .update({
          status: 'completed',
          processing_completed_at: new Date().toISOString(),
          total_outputs: results.length
        })
        .eq('id', jobId);

      await job.updateProgress(100);
      console.log(`🎉 Job ${jobId} completed successfully! Generated ${results.length} outputs`);

    } catch (error) {
      console.error(`❌ Job ${jobId} failed:`, error);
      
      // Update job status to failed
      await supabase
        .from('video_jobs')
        .update({
          status: 'failed',
          processing_completed_at: new Date().toISOString(),
          error_message: error.message
        })
        .eq('id', jobId);

      throw error;
    }
  },
  {
    connection: redis,
    concurrency: WORKER_CONCURRENCY,
    removeOnComplete: { count: 100 },
    removeOnFail: { count: 50 }
  }
);

// ===== EVENT HANDLERS =====

worker.on('ready', () => {
  console.log('🚀 Worker is ready and waiting for jobs!');
});

worker.on('completed', (job) => {
  console.log(`✅ Job ${job.id} completed successfully`);
});

worker.on('failed', (job, err) => {
  console.error(`❌ Job ${job.id} failed:`, err.message);
});

worker.on('error', (err) => {
  console.error('🔥 Worker error:', err);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('📤 Shutting down worker...');
  await worker.close();
  await redis.quit();
  healthServer.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('📤 Shutting down worker...');
  await worker.close();
  await redis.quit();
  healthServer.close();
  process.exit(0);
});

console.log('✅ AutoRepurpose Worker is ready and waiting for jobs!');
