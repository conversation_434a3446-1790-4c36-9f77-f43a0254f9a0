
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-supabase-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# AWS S3 Configuration
AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
S3_BUCKET_NAME=auto-repurpose

# Redis Configuration (Upstash)
REDIS_URL=rediss://default:<EMAIL>:6379

# Worker Configuration
WORKER_CONCURRENCY=3
MAX_PROCESSING_TIME_MS=300000

# Railway Port (automatically set by Railway)
PORT=$PORT

# Node Environment
NODE_ENV=production
