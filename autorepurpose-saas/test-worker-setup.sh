
#!/bin/bash

echo "🧪 Testing AutoRepurpose Worker Setup"
echo "======================================"

# Navigate to worker directory
cd /home/<USER>/autorepurpose-saas/worker

# Make setup script executable
chmod +x setup.sh

# Run setup
echo "🔧 Running worker setup..."
./setup.sh

echo ""
echo "🧪 Testing configuration..."

# Check if .env exists
if [ -f ".env" ]; then
    echo "✅ Environment file exists"
else
    echo "❌ Environment file missing"
    exit 1
fi

# Check if built files exist
if [ -d "dist" ]; then
    echo "✅ TypeScript compiled successfully"
else
    echo "❌ Build directory not found"
    exit 1
fi

# Test FFmpeg
if command -v ffmpeg &> /dev/null; then
    echo "✅ FFmpeg available: $(ffmpeg -version | head -n 1)"
else
    echo "❌ FFmpeg not found"
    exit 1
fi

# Test temp directory
if [ -d "/tmp/autorepurpose" ]; then
    echo "✅ Temp directory created"
else
    echo "❌ Temp directory not found"
    exit 1
fi

echo ""
echo "✅ Worker setup test completed successfully!"
echo ""
echo "To start the worker:"
echo "  cd worker"
echo "  yarn dev"
echo ""
echo "To test the main app:"
echo "  cd app"
echo "  yarn dev"
