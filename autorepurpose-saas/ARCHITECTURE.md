
# AutoRepurpose SaaS - System Architecture & Development Plan

## Table of Contents
1. [Overview](#overview)
2. [Platform Rules Analysis](#platform-rules-analysis)
3. [System Architecture](#system-architecture)
4. [Database Schema](#database-schema)
5. [API Architecture](#api-architecture)
6. [Worker Architecture](#worker-architecture)
7. [File Flow Pipeline](#file-flow-pipeline)
8. [Authentication Flow](#authentication-flow)
9. [Payment Integration](#payment-integration)
10. [Development Roadmap](#development-roadmap)
11. [Testing Checklist](#testing-checklist)

## Overview

**Application Name:** AutoRepurpose  
**Purpose:** Video repurposing SaaS for content creators and agencies  
**Pricing:** $30/month subscription  
**Target Processing Time:** 2-5 minutes per video  
**File Retention:** 3 days for processed videos  
**Maximum Upload Size:** 5GB  

### Key Features
- User authentication with Clerk
- Video uploads to AWS S3 (up to 5GB)
- Automated video processing with FFmpeg
- Multiple platform format outputs
- Real-time job status updates
- Admin dashboard for managing platform rules
- Subscription management with Stripe/Razorpay
- File cleanup after 3 days

### Tech Stack
- **Frontend:** Next.js 14, TypeScript, Tailwind CSS
- **Backend:** Next.js API Routes, Node.js
- **Database:** Supabase (PostgreSQL)
- **Storage:** AWS S3
- **Authentication:** Clerk
- **Payment:** Stripe & Razorpay
- **Queue:** BullMQ with Upstash Redis
- **Video Processing:** FFmpeg
- **Deployment:** Vercel (frontend), Railway (worker)

## Platform Rules Analysis

The video platform rules are stored in `video_platforms_rules_updated.json` with the following structure:

### Rule Schema
```json
{
  "platform_name": {
    "formats": [
      {
        "name": "format_name",
        "aspect_ratio": "16:9",
        "resolution": "1920x1080",
        "max_duration": 60,
        "min_duration": 15,
        "video_codec": "h264",
        "audio_codec": "aac",
        "frame_rate": 30,
        "bitrate": "2000k",
        "audio_bitrate": "128k"
      }
    ]
  }
}
```

### Supported Platforms
- **YouTube:** Multiple formats (Shorts, Regular, Long-form)
- **Instagram:** Reels, Stories, IGTV, Posts
- **TikTok:** Vertical videos
- **Twitter/X:** Various aspect ratios
- **LinkedIn:** Professional content formats
- **Facebook:** Stories, Posts, Reels
- **Snapchat:** Vertical stories
- **Pinterest:** Pin videos
- **Discord:** Optimized for chat sharing

## System Architecture

```mermaid
graph TB
    A[User Upload] --> B[Next.js Frontend]
    B --> C[AWS S3 Storage]
    B --> D[Supabase Database]
    B --> E[BullMQ Queue]
    E --> F[Worker Service]
    F --> G[FFmpeg Processing]
    G --> H[Processed Videos]
    H --> C
    F --> D
    I[Clerk Auth] --> B
    J[Stripe/Razorpay] --> B
    K[Admin Dashboard] --> L[Platform Rules]
    L --> D
```

### Core Components

1. **Next.js Application**
   - User interface and dashboard
   - API routes for file uploads
   - Authentication integration
   - Payment processing
   - Job status monitoring

2. **Worker Service**
   - Video processing with FFmpeg
   - Job queue management
   - File cleanup scheduling
   - Error handling and retry logic

3. **Database Layer**
   - User management
   - Job tracking
   - Payment records
   - Platform rules storage

4. **Storage Layer**
   - Original video storage
   - Processed video storage
   - Temporary file management

## Database Schema

### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  clerk_id TEXT UNIQUE NOT NULL,
  email TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  subscription_status TEXT DEFAULT 'inactive',
  subscription_id TEXT,
  subscription_current_period_end TIMESTAMP,
  stripe_customer_id TEXT,
  razorpay_customer_id TEXT
);
```

### VideoJobs Table
```sql
CREATE TABLE video_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  original_filename TEXT NOT NULL,
  original_file_url TEXT NOT NULL,
  original_file_size BIGINT NOT NULL,
  status TEXT DEFAULT 'queued',
  platforms TEXT[] NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  error_message TEXT,
  total_outputs INTEGER DEFAULT 0,
  completed_outputs INTEGER DEFAULT 0
);
```

### VideoOutputs Table
```sql
CREATE TABLE video_outputs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  job_id UUID REFERENCES video_jobs(id),
  platform TEXT NOT NULL,
  format_name TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  processing_time INTEGER,
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP NOT NULL
);
```

### PaymentTransactions Table
```sql
CREATE TABLE payment_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  provider TEXT NOT NULL, -- 'stripe' or 'razorpay'
  provider_transaction_id TEXT NOT NULL,
  amount INTEGER NOT NULL,
  currency TEXT NOT NULL,
  status TEXT NOT NULL,
  subscription_id TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### PlatformRules Table
```sql
CREATE TABLE platform_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  platform_name TEXT NOT NULL,
  rules JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## API Architecture

### Public API Routes

#### Authentication
- `POST /api/auth/webhook` - Clerk webhook for user sync
- `GET /api/auth/session` - Get current user session

#### File Management
- `POST /api/upload/presigned-url` - Get S3 presigned upload URL
- `POST /api/jobs` - Create new video processing job
- `GET /api/jobs` - Get user's video jobs
- `GET /api/jobs/[id]` - Get specific job details
- `DELETE /api/jobs/[id]` - Cancel job

#### Payment Integration
- `POST /api/payments/stripe/create-session` - Create Stripe checkout session
- `POST /api/payments/stripe/webhook` - Stripe webhook handler
- `POST /api/payments/razorpay/create-order` - Create Razorpay order
- `POST /api/payments/razorpay/verify` - Verify Razorpay payment
- `POST /api/payments/razorpay/webhook` - Razorpay webhook handler
- `GET /api/payments/subscription` - Get subscription status
- `POST /api/payments/cancel` - Cancel subscription

#### File Downloads
- `GET /api/download/[...path]` - Serve processed videos

### Admin API Routes
- `GET /api/admin/platform-rules` - Get platform rules
- `PUT /api/admin/platform-rules` - Update platform rules
- `GET /api/admin/stats` - Get system statistics

### Worker Communication
- `POST /api/worker/job-update` - Update job status from worker
- `POST /api/worker/job-complete` - Mark job as complete

## Worker Architecture

### BullMQ Job Processing Flow

```mermaid
graph LR
    A[Job Created] --> B[Queue: video-processing]
    B --> C[Worker Picks Job]
    C --> D[Download Original]
    D --> E[Process with FFmpeg]
    E --> F[Upload Processed Files]
    F --> G[Update Database]
    G --> H[Schedule Cleanup]
```

### Job Types
1. **video-processing** - Main video conversion job
2. **file-cleanup** - Remove expired files
3. **job-retry** - Retry failed jobs

### Worker Configuration
```javascript
{
  concurrency: 3,
  removeOnComplete: 100,
  removeOnFail: 50,
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 5000
    }
  }
}
```

### FFmpeg Pipeline
1. **Input Validation** - Check file format and size
2. **Metadata Extraction** - Get video properties
3. **Format Processing** - Apply platform-specific rules
4. **Quality Optimization** - Maintain optimal quality/size ratio
5. **Output Generation** - Create multiple format variants

## File Flow Pipeline

### Upload Flow
1. User selects file and platforms
2. Frontend requests presigned S3 URL
3. File uploaded directly to S3
4. Job created in database
5. Job queued in BullMQ
6. User redirected to dashboard

### Processing Flow
1. Worker retrieves job from queue
2. Downloads original file from S3
3. Processes video according to platform rules
4. Uploads processed files to S3
5. Updates database with file URLs
6. Schedules cleanup job for 3 days later

### Download Flow
1. User requests processed video
2. API validates user ownership
3. Serves file from S3 through API proxy
4. Tracks download analytics

## Authentication Flow

### Clerk Integration
```mermaid
graph LR
    A[User Login] --> B[Clerk Authentication]
    B --> C[JWT Token Generated]
    C --> D[User Synced to Database]
    D --> E[Session Established]
    E --> F[Access Granted]
```

### User Sync Process
1. User signs up/logs in through Clerk
2. Clerk webhook triggers user sync
3. User record created/updated in Supabase
4. Session token validated on each request

## Payment Integration

### Stripe Integration

#### Subscription Flow
```mermaid
graph LR
    A[User Selects Plan] --> B[Create Checkout Session]
    B --> C[Redirect to Stripe]
    C --> D[Payment Processing]
    D --> E[Webhook Confirmation]
    E --> F[Update Subscription Status]
```

#### Implementation Details
- **Pricing:** $30/month recurring subscription
- **Product ID:** `prod_autorepurpose_monthly`
- **Webhooks:** Handle subscription events
- **Customer Portal:** For subscription management

#### Required Environment Variables
```env
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PRICE_ID=price_...
```

### Razorpay Integration

#### Subscription Flow
```mermaid
graph LR
    A[User Selects Plan] --> B[Create Razorpay Order]
    B --> C[Payment Gateway]
    C --> D[Payment Verification]
    D --> E[Webhook Confirmation]
    E --> F[Update Subscription Status]
```

#### Implementation Details
- **Pricing:** ₹2,500/month recurring subscription
- **Plan ID:** `plan_autorepurpose_monthly`
- **Webhooks:** Handle subscription and payment events
- **Dashboard:** Razorpay dashboard for management

#### Required Environment Variables
```env
RAZORPAY_KEY_ID=rzp_live_...
RAZORPAY_KEY_SECRET=...
RAZORPAY_WEBHOOK_SECRET=...
RAZORPAY_PLAN_ID=plan_...
```

#### Payment Methods Supported
- Credit/Debit Cards
- Net Banking
- UPI
- Digital Wallets (Paytm, PhonePe, etc.)
- EMI Options

### Payment Provider Selection
Both Stripe and Razorpay are implemented in parallel with a provider selection mechanism:
- Users can choose payment method during checkout
- Admin can configure preferred provider per region
- Fallback support if one provider fails
- Unified subscription management across providers

## Development Roadmap

### Day 1: Project Setup & Authentication ✅
- [x] Initialize Next.js project with TypeScript
- [x] Configure Tailwind CSS and UI components
- [x] Set up Supabase database and schema
- [x] Integrate Clerk authentication
- [x] Create basic project structure
- [x] Set up environment variables
- [x] Initialize worker service structure

### Day 2: Upload System & Job Management
- [ ] Implement AWS S3 integration
- [ ] Create file upload component with progress
- [ ] Build job creation and queueing system
- [ ] Design dashboard for job monitoring
- [ ] Implement real-time status updates
- [ ] Add file validation and size limits

### Day 3: Video Processing Worker
- [ ] Deploy worker service to Railway
- [ ] Implement FFmpeg processing pipeline
- [ ] Create platform-specific conversion logic
- [ ] Add job retry and error handling
- [ ] Implement progress tracking
- [ ] Test video processing with sample files

### Day 4: File Management & Downloads
- [ ] Create download API endpoints
- [ ] Implement file serving with authentication
- [ ] Build user file management interface
- [ ] Add file cleanup scheduling
- [ ] Implement download analytics
- [ ] Test file access controls

### Day 5: Payment Integration
- [ ] Integrate Stripe subscription system
- [ ] Integrate Razorpay payment system
- [ ] Create subscription management UI
- [ ] Implement webhook handlers
- [ ] Add payment provider selection
- [ ] Test payment flows end-to-end

### Day 6: Admin Dashboard & Rules Management
- [ ] Create admin authentication system
- [ ] Build platform rules management UI
- [ ] Implement rules validation system
- [ ] Add system monitoring dashboard
- [ ] Create user management interface
- [ ] Test admin functionality

### Day 7: Testing, Polish & Deployment
- [ ] Comprehensive integration testing
- [ ] Performance optimization
- [ ] Error handling improvements
- [ ] UI/UX refinements
- [ ] Production deployment setup
- [ ] Documentation completion

## Testing Checklist

### Day 1 Testing: Project Setup & Authentication ✅

#### Core Infrastructure
- [x] **Next.js Application**
  - [x] Application starts without errors (`npm run dev`)
  - [x] All pages load correctly (/, /dashboard, /auth)
  - [x] TypeScript compilation works without errors
  - [x] No console errors in browser

- [x] **Styling & UI**
  - [x] Tailwind CSS is properly configured
  - [x] Custom UI components render correctly
  - [x] Responsive design works on mobile and desktop
  - [x] Dark/light theme support (if implemented)

- [x] **Database Connection**
  - [x] Supabase client connection established
  - [x] Database tables created successfully
  - [x] Can query database without errors
  - [x] Environment variables properly loaded

- [x] **Authentication System**
  - [x] Clerk sign-up flow works completely
  - [x] Clerk sign-in flow works completely
  - [x] User session persists after page refresh
  - [x] Protected routes redirect unauthenticated users
  - [x] User data syncs to Supabase database
  - [x] Sign-out functionality works
  - [x] User profile information displays correctly

#### Critical Tests
- [x] Create new account and verify user appears in Supabase
- [x] Test sign-in with existing account
- [x] Verify protected pages require authentication
- [x] Test session persistence across browser tabs
- [x] Test responsive design on different screen sizes

### Day 2 Testing: Upload System & Job Management

#### File Upload System
- [ ] **S3 Integration**
  - [ ] Presigned URL generation works
  - [ ] Files upload successfully to S3 bucket
  - [ ] Upload progress indicator functions
  - [ ] Large files (up to 5GB) upload without issues
  - [ ] Upload cancellation works properly

- [ ] **File Validation**
  - [ ] Supported video formats accepted (MP4, MOV, AVI, etc.)
  - [ ] Unsupported formats properly rejected
  - [ ] File size validation works (max 5GB)
  - [ ] Malformed files handled gracefully

- [ ] **Job Management**
  - [ ] Jobs created successfully in database
  - [ ] Job status updates properly
  - [ ] Multiple platform selection works
  - [ ] Job queue integration functions
  - [ ] Job history displays correctly

#### Dashboard Functionality
- [ ] **User Interface**
  - [ ] Upload interface is intuitive and responsive
  - [ ] Platform selection works for all supported platforms
  - [ ] Job status displays with proper indicators
  - [ ] Real-time updates work (WebSocket/polling)
  - [ ] Error messages are clear and helpful

#### Critical Tests
- [ ] Upload a 1GB video file and verify S3 storage
- [ ] Create job with multiple platforms selected
- [ ] Test upload with unsupported file format
- [ ] Verify job appears in dashboard immediately
- [ ] Test upload cancellation during progress

### Day 3 Testing: Video Processing Worker

#### Worker Service
- [ ] **Railway Deployment**
  - [ ] Worker service deploys successfully to Railway
  - [ ] Environment variables properly configured
  - [ ] Worker connects to Redis queue
  - [ ] Worker can access AWS S3 for file operations

- [ ] **Video Processing**
  - [ ] FFmpeg correctly installed and accessible
  - [ ] Sample video processes without errors
  - [ ] All platform formats generate correctly
  - [ ] Processing time is reasonable (2-5 minutes)
  - [ ] Quality of output videos is acceptable

- [ ] **Job Queue Processing**
  - [ ] Jobs picked up from queue automatically
  - [ ] Job status updates reflect in database
  - [ ] Failed jobs retry with exponential backoff
  - [ ] Concurrent job processing works (3 jobs simultaneously)
  - [ ] Completed jobs marked correctly

#### Error Handling
- [ ] **Failure Scenarios**
  - [ ] Corrupted video files handled gracefully
  - [ ] Network interruptions during processing
  - [ ] Invalid platform configurations
  - [ ] Worker service restarts don't lose jobs
  - [ ] Database connection failures handled

#### Critical Tests
- [ ] Process a sample video for YouTube Shorts format
- [ ] Process same video for multiple platforms simultaneously
- [ ] Test with a corrupted video file
- [ ] Verify worker handles job queue restart
- [ ] Test concurrent processing with 5+ jobs

### Day 4 Testing: File Management & Downloads

#### File Serving
- [ ] **Download System**
  - [ ] Processed videos download successfully
  - [ ] Download links expire after 3 days
  - [ ] Authentication required for file access
  - [ ] Download analytics track properly
  - [ ] File streaming works for large videos

- [ ] **File Management**
  - [ ] User can view all their processed files
  - [ ] File organization by job and platform
  - [ ] File size and processing time displayed
  - [ ] Bulk download functionality works
  - [ ] File deletion works properly

- [ ] **Cleanup System**
  - [ ] Files automatically deleted after 3 days
  - [ ] Cleanup jobs scheduled properly
  - [ ] Database cleanup occurs with file cleanup
  - [ ] No orphaned files left in S3
  - [ ] Cleanup logs are maintained

#### Security & Access Control
- [ ] **File Access**
  - [ ] Users can only access their own files
  - [ ] Direct S3 URLs are not accessible
  - [ ] Expired links return proper error
  - [ ] Unauthorized access attempts blocked
  - [ ] Admin can access all files (if implemented)

#### Critical Tests
- [ ] Download a processed video and verify quality
- [ ] Test file access with different user accounts
- [ ] Verify expired links return 403/404 errors
- [ ] Test cleanup system with test files
- [ ] Verify file organization in dashboard

### Day 5 Testing: Payment Integration

#### Stripe Integration
- [ ] **Subscription Flow**
  - [ ] Checkout session creation works
  - [ ] Stripe payment page renders correctly
  - [ ] Successful payments update user subscription
  - [ ] Failed payments handled gracefully
  - [ ] Subscription status reflects in dashboard

- [ ] **Webhook Handling**
  - [ ] Webhook endpoint responds correctly
  - [ ] Subscription events update database
  - [ ] Payment confirmations processed
  - [ ] Cancellation events handled
  - [ ] Customer portal integration works

#### Razorpay Integration
- [ ] **Payment Flow**
  - [ ] Razorpay order creation works
  - [ ] Payment gateway loads correctly
  - [ ] Multiple payment methods available
  - [ ] Payment verification functions
  - [ ] Failed payments handled properly

- [ ] **Subscription Management**
  - [ ] Recurring payments set up correctly
  - [ ] Subscription status updates properly
  - [ ] Payment history accessible
  - [ ] Refund handling (if applicable)
  - [ ] Currency conversion works (USD to INR)

#### Provider Selection
- [ ] **Multi-Provider Support**
  - [ ] User can choose payment provider
  - [ ] Provider switching works seamlessly
  - [ ] Subscription data consistent across providers
  - [ ] Fallback to secondary provider
  - [ ] Admin can configure provider preferences

#### Critical Tests
- [ ] Complete full payment flow with Stripe
- [ ] Complete full payment flow with Razorpay
- [ ] Test failed payment scenarios
- [ ] Verify subscription cancellation works
- [ ] Test webhook delivery and processing

### Day 6 Testing: Admin Dashboard & Rules Management

#### Admin Authentication
- [ ] **Access Control**
  - [ ] Admin-only areas properly protected
  - [ ] Role-based access control works
  - [ ] Admin login flow functions
  - [ ] Regular users cannot access admin features
  - [ ] Admin session management works

#### Platform Rules Management
- [ ] **Rules Editor**
  - [ ] JSON rules display correctly
  - [ ] Rules editing interface works
  - [ ] Rules validation prevents invalid configs
  - [ ] Rules updates reflect in processing
  - [ ] Rules history/versioning (if implemented)

- [ ] **System Monitoring**
  - [ ] Dashboard shows system statistics
  - [ ] Real-time metrics update
  - [ ] Error logs accessible
  - [ ] Performance metrics display
  - [ ] User activity monitoring

#### User Management
- [ ] **Admin User Controls**
  - [ ] View all user accounts
  - [ ] User subscription status visible
  - [ ] Can cancel/modify user subscriptions
  - [ ] User activity logs accessible
  - [ ] Support ticket system (if implemented)

#### Critical Tests
- [ ] Update platform rules and verify in processing
- [ ] Test admin access control thoroughly
- [ ] Verify system metrics accuracy
- [ ] Test user management functions
- [ ] Ensure regular users cannot access admin features

### Day 7 Testing: Final Integration & Production Readiness

#### Comprehensive Integration Tests
- [ ] **End-to-End User Journey**
  - [ ] New user sign-up through video download
  - [ ] Payment subscription through first video processing
  - [ ] Multiple video processing jobs
  - [ ] File management and download
  - [ ] Account cancellation process

- [ ] **Cross-Browser Testing**
  - [ ] Chrome functionality complete
  - [ ] Firefox functionality complete
  - [ ] Safari functionality complete
  - [ ] Mobile browser testing
  - [ ] Edge/Internet Explorer compatibility

- [ ] **Performance Testing**
  - [ ] Page load times under 3 seconds
  - [ ] Upload progress smooth for large files
  - [ ] Dashboard responsive with many jobs
  - [ ] Database queries optimized
  - [ ] Worker processing times acceptable

#### Production Deployment
- [ ] **Environment Configuration**
  - [ ] Production environment variables set
  - [ ] SSL certificates configured
  - [ ] Domain name properly configured
  - [ ] CDN setup (if applicable)
  - [ ] Error monitoring configured

- [ ] **Security Testing**
  - [ ] Authentication security audit
  - [ ] File access security verified
  - [ ] Payment security confirmed
  - [ ] No sensitive data in client-side code
  - [ ] Rate limiting implemented

- [ ] **Monitoring & Logging**
  - [ ] Application monitoring setup
  - [ ] Error logging configured
  - [ ] Performance monitoring active
  - [ ] Payment monitoring setup
  - [ ] Worker service monitoring

#### Final Readiness Checklist
- [ ] **Documentation**
  - [ ] User documentation complete
  - [ ] API documentation updated
  - [ ] Deployment documentation ready
  - [ ] Troubleshooting guides prepared
  - [ ] Change log maintained

- [ ] **Backup & Recovery**
  - [ ] Database backup strategy implemented
  - [ ] File backup process established
  - [ ] Disaster recovery plan ready
  - [ ] Data migration scripts tested
  - [ ] Rollback procedures documented

#### Critical Pre-Launch Tests
- [ ] Process 10+ videos simultaneously without issues
- [ ] Complete payment flow with real payment methods
- [ ] Test system under load (stress testing)
- [ ] Verify all error scenarios handled gracefully
- [ ] Confirm all security measures are active
- [ ] Test complete user journey from sign-up to cancellation

---

This testing checklist ensures that each development milestone is thoroughly validated before moving to the next phase. Each test should be performed manually and documented, with any issues resolved before progressing. The final day's testing is particularly critical as it validates the entire system's readiness for production deployment.
