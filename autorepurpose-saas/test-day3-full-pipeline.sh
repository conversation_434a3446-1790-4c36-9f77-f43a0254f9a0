
#!/bin/bash

echo "🧪 Testing Day 3: Video Processing Worker Pipeline"
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

BASE_URL="http://localhost:3000"

echo -e "${BLUE}1. Testing Main App Health...${NC}"
curl -s "$BASE_URL/api/health" | jq '.' || echo "❌ Main app health check failed"

echo ""
echo -e "${BLUE}2. Testing Database Connection...${NC}"
curl -s "$BASE_URL/api/test/database" | jq '.' || echo "❌ Database test failed"

echo ""
echo -e "${BLUE}3. Testing Worker Connection...${NC}"
WORKER_TEST=$(curl -s "$BASE_URL/api/test/worker-connection")
echo "$WORKER_TEST" | jq '.'

if echo "$WORKER_TEST" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ Worker connection successful${NC}"
else
    echo -e "${RED}❌ Worker connection failed${NC}"
    echo "Please make sure:"
    echo "1. Railway worker is deployed and running"
    echo "2. You've generated a public domain in Railway"
    echo "3. WORKER_SERVICE_URL is updated in .env"
    exit 1
fi

echo ""
echo -e "${BLUE}4. Testing Queue Stats (before test job)...${NC}"
curl -s "$BASE_URL/api/test/queue-test-job" | jq '.queueStats'

echo ""
echo -e "${BLUE}5. Adding Test Job to Queue...${NC}"
TEST_JOB=$(curl -s -X POST "$BASE_URL/api/test/queue-test-job" -H "Content-Type: application/json" -d '{"testType": "simple"}')
echo "$TEST_JOB" | jq '.'

if echo "$TEST_JOB" | grep -q '"success":true'; then
    JOB_ID=$(echo "$TEST_JOB" | jq -r '.jobId')
    echo -e "${GREEN}✅ Test job queued: $JOB_ID${NC}"
    
    echo ""
    echo -e "${BLUE}6. Monitoring Queue (waiting 30 seconds)...${NC}"
    for i in {1..6}; do
        echo -e "${YELLOW}Check $i/6 (${i}0 seconds)...${NC}"
        curl -s "$BASE_URL/api/test/queue-test-job" | jq '.queueStats'
        
        if [ $i -lt 6 ]; then
            sleep 5
        fi
    done
    
else
    echo -e "${RED}❌ Failed to queue test job${NC}"
    echo "$TEST_JOB"
    exit 1
fi

echo ""
echo -e "${BLUE}7. Final Queue Stats...${NC}"
FINAL_STATS=$(curl -s "$BASE_URL/api/test/queue-test-job")
echo "$FINAL_STATS" | jq '.'

echo ""
echo -e "${BLUE}8. Testing Complete!${NC}"
echo ""
echo "Expected outcomes:"
echo "- ✅ Worker should be connected and healthy"
echo "- ✅ Test job should be processed (moved from waiting → active → completed/failed)"
echo "- ⚠️  Job may fail due to missing test video file (this is expected)"
echo "- ✅ Worker should log the processing attempt"
echo ""
echo "Next steps:"
echo "1. Check Railway worker logs for processing details"
echo "2. If successful, proceed with Day 4 implementation"
echo "3. If issues, debug worker configuration"
