{"name": "app", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "prisma": {"seed": "tsx --require dotenv/config scripts/seed.ts"}, "devDependencies": {"@next/swc-wasm-nodejs": "13.5.1", "@types/node": "20.6.2", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "@typescript-eslint/eslint-plugin": "7.0.0", "@typescript-eslint/parser": "7.0.0", "eslint": "9.24.0", "eslint-config-next": "15.3.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react-hooks": "4.6.0", "postcss": "8.4.30", "prisma": "6.7.0", "tailwind-merge": "2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "1.0.7", "ts-node": "10.9.2", "tsx": "4.20.3", "typescript": "5.2.2"}, "dependencies": {"@aws-sdk/client-s3": "^3.864.0", "@aws-sdk/s3-request-presigner": "^3.864.0", "@clerk/nextjs": "^6.30.2", "@floating-ui/react": "0.26.0", "@headlessui/react": "1.7.18", "@hookform/resolvers": "3.9.0", "@next-auth/prisma-adapter": "1.0.7", "@prisma/client": "6.7.0", "@radix-ui/react-accordion": "1.2.0", "@radix-ui/react-alert-dialog": "1.1.1", "@radix-ui/react-aspect-ratio": "1.1.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "1.1.1", "@radix-ui/react-collapsible": "1.1.0", "@radix-ui/react-context-menu": "2.2.1", "@radix-ui/react-dialog": "1.1.1", "@radix-ui/react-dropdown-menu": "2.1.1", "@radix-ui/react-hover-card": "1.1.1", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-menubar": "1.1.1", "@radix-ui/react-navigation-menu": "1.2.0", "@radix-ui/react-popover": "1.1.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "1.2.0", "@radix-ui/react-scroll-area": "1.1.0", "@radix-ui/react-select": "2.1.1", "@radix-ui/react-separator": "1.1.0", "@radix-ui/react-slider": "1.2.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "1.1.0", "@radix-ui/react-tabs": "1.1.0", "@radix-ui/react-toast": "1.2.1", "@radix-ui/react-toggle": "1.1.0", "@radix-ui/react-toggle-group": "1.1.0", "@radix-ui/react-tooltip": "1.1.2", "@stripe/stripe-js": "^7.8.0", "@supabase/supabase-js": "^2.55.0", "@tanstack/react-query": "5.0.0", "@types/bcryptjs": "2.4.6", "@types/ioredis": "^5.0.0", "@types/jsonwebtoken": "9.0.5", "@types/plotly.js": "2.35.5", "@types/react-plotly.js": "2.6.3", "autoprefixer": "10.4.15", "bcryptjs": "2.4.3", "bullmq": "^5.58.1", "chart.js": "4.4.9", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "cmdk": "1.0.0", "cookie": "1.0.2", "csv": "6.3.11", "date-fns": "3.6.0", "dayjs": "1.11.13", "dotenv": "16.5.0", "embla-carousel-react": "8.3.0", "formik": "2.4.5", "framer-motion": "10.18.0", "gray-matter": "4.0.3", "input-otp": "1.2.4", "ioredis": "^5.7.0", "jotai": "2.6.0", "jsonwebtoken": "9.0.2", "lodash": "4.17.21", "lucide-react": "0.446.0", "mapbox-gl": "1.13.3", "next": "14.2.28", "next-auth": "4.24.11", "next-themes": "^0.4.6", "plotly.js": "2.35.3", "razorpay": "^2.9.6", "react": "18.2.0", "react-chartjs-2": "5.3.0", "react-datepicker": "6.1.0", "react-day-picker": "8.10.1", "react-dom": "18.2.0", "react-hook-form": "7.53.0", "react-hot-toast": "2.4.1", "react-intersection-observer": "9.8.0", "react-is": "18.3.1", "react-plotly.js": "2.6.0", "react-resizable-panels": "2.1.3", "react-select": "5.8.0", "react-use": "17.6.0", "recharts": "2.15.3", "sonner": "^2.0.7", "stripe": "^18.4.0", "svix": "^1.71.0", "swr": "2.2.4", "tailwind-scrollbar-hide": "1.1.7", "vaul": "0.9.9", "webpack": "5.99.5", "yup": "1.3.0", "zod": "3.23.8", "zustand": "5.0.3"}, "browserslist": ["ie >= 11", "> 0.5%", "last 2 versions", "not dead"]}