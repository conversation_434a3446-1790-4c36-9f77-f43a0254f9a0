
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  Download, 
  HardDrive, 
  Clock, 
  FileVideo,
  TrendingUp,
  <PERSON>ert<PERSON>riangle,
  CheckCircle
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface FileStats {
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  processingFiles: number;
  totalDownloads: number;
  totalStorageUsed: number; // in bytes
  expiringIn24h: number;
  expiredFiles: number;
  avgProcessingTime: number; // in minutes
}

export default function FileStats() {
  const [stats, setStats] = useState<FileStats | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchStats = async () => {
    try {
      setLoading(true);
      
      // Get user files to calculate stats
      const response = await fetch('/api/files/user-files?limit=1000'); // Get all files for stats
      const data = await response.json();

      if (data.success && data.data.files) {
        const files = data.data.files;
        
        // Calculate stats
        const totalFiles = files.length;
        const completedFiles = files.filter((f: any) => f.status === 'completed').length;
        const failedFiles = files.filter((f: any) => f.status === 'failed').length;
        const processingFiles = files.filter((f: any) => f.status === 'processing').length;
        const totalDownloads = files.reduce((sum: number, f: any) => sum + (f.download_count || 0), 0);
        const totalStorageUsed = files
          .filter((f: any) => f.status === 'completed' && !f.isExpired)
          .reduce((sum: number, f: any) => sum + (f.file_size_bytes || 0), 0);
        const expiringIn24h = files.filter((f: any) => 
          !f.isExpired && f.hoursUntilExpiry <= 24 && f.status === 'completed'
        ).length;
        const expiredFiles = files.filter((f: any) => f.isExpired).length;

        // Calculate average processing time (mock for now - would need processing time tracking)
        const avgProcessingTime = 3.5; // minutes - would calculate from actual data

        const calculatedStats: FileStats = {
          totalFiles,
          completedFiles,
          failedFiles,
          processingFiles,
          totalDownloads,
          totalStorageUsed,
          expiringIn24h,
          expiredFiles,
          avgProcessingTime
        };

        setStats(calculatedStats);
      }
    } catch (error) {
      console.error('Error fetching file stats:', error);
      toast.error('Failed to load file statistics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  const formatFileSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 1) return `${Math.round(minutes * 60)}s`;
    if (minutes < 60) return `${minutes.toFixed(1)}m`;
    return `${Math.round(minutes / 60)}h ${Math.round(minutes % 60)}m`;
  };

  if (loading || !stats) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Loading...</CardTitle>
              <div className="h-4 w-4 bg-muted rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded mb-2" />
              <div className="h-3 bg-muted rounded w-2/3" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const successRate = stats.totalFiles > 0 ? (stats.completedFiles / stats.totalFiles) * 100 : 0;
  const storageUsagePercent = Math.min((stats.totalStorageUsed / (5 * 1024 * 1024 * 1024)) * 100, 100); // Assuming 5GB limit

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Files */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Files</CardTitle>
          <FileVideo className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalFiles}</div>
          <p className="text-xs text-muted-foreground">
            {stats.completedFiles} completed, {stats.processingFiles} processing
          </p>
          <div className="mt-2">
            <Progress value={successRate} className="h-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {successRate.toFixed(1)}% success rate
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Total Downloads */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Downloads</CardTitle>
          <Download className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalDownloads}</div>
          <p className="text-xs text-muted-foreground">
            Across {stats.completedFiles} files
          </p>
          {stats.completedFiles > 0 && (
            <div className="flex items-center mt-2">
              <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
              <p className="text-xs text-muted-foreground">
                {(stats.totalDownloads / stats.completedFiles).toFixed(1)} avg per file
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Storage Usage */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
          <HardDrive className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatFileSize(stats.totalStorageUsed)}</div>
          <p className="text-xs text-muted-foreground">
            {stats.completedFiles} active files
          </p>
          <div className="mt-2">
            <Progress value={storageUsagePercent} className="h-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {storageUsagePercent.toFixed(1)}% of 5GB limit
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Expiring Soon */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold flex items-center">
            {stats.expiringIn24h}
            {stats.expiringIn24h > 0 && (
              <AlertTriangle className="h-4 w-4 text-yellow-500 ml-2" />
            )}
          </div>
          <p className="text-xs text-muted-foreground">
            Files expire in 24h
          </p>
          {stats.expiredFiles > 0 && (
            <p className="text-xs text-red-500 mt-1">
              {stats.expiredFiles} already expired
            </p>
          )}
        </CardContent>
      </Card>

      {/* Processing Performance */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Processing Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-green-600">{stats.completedFiles}</div>
              <p className="text-xs text-muted-foreground">Completed</p>
            </div>
            <div>
              <div className="text-lg font-bold text-blue-600">{stats.processingFiles}</div>
              <p className="text-xs text-muted-foreground">Processing</p>
            </div>
            <div>
              <div className="text-lg font-bold text-red-600">{stats.failedFiles}</div>
              <p className="text-xs text-muted-foreground">Failed</p>
            </div>
          </div>
          <div className="mt-4 pt-4 border-t">
            <p className="text-sm text-center text-muted-foreground">
              Average processing time: <span className="font-medium">{formatDuration(stats.avgProcessingTime)}</span>
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">File Management</CardTitle>
          <CardDescription>Quick file management actions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-3">
            <div className="flex items-center justify-between p-3 rounded-lg border bg-card/50">
              <div>
                <p className="text-sm font-medium">Active Files</p>
                <p className="text-xs text-muted-foreground">Ready for download</p>
              </div>
              <div className="text-lg font-bold text-green-600">{stats.completedFiles}</div>
            </div>
            <div className="flex items-center justify-between p-3 rounded-lg border bg-card/50">
              <div>
                <p className="text-sm font-medium">Total Storage</p>
                <p className="text-xs text-muted-foreground">Space in use</p>
              </div>
              <div className="text-sm font-bold">{formatFileSize(stats.totalStorageUsed)}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
