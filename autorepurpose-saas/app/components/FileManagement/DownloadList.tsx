
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { 
  Download, 
  Clock, 
  CheckCircle, 
  XCircle, 
  FileVideo, 
  Calendar,
  TrendingDown,
  Filter,
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'react-hot-toast';

interface VideoOutput {
  id: string;
  job_id: string;
  platform_id: string;
  preset_name: string;
  s3_output_key: string | null;
  filename: string;
  resolution: string;
  file_size_bytes: number | null;
  status: 'completed' | 'failed' | 'processing';
  created_at: string;
  download_count: number;
  last_downloaded_at: string | null;
  error_message: string | null;
  downloadUrl: string | null;
  isExpired: boolean;
  hoursUntilExpiry: number;
  expirationDate: string;
  video_jobs: {
    id: string;
    original_filename: string;
    status: string;
    created_at: string;
  };
}

interface FilesByJob {
  [key: string]: {
    job: VideoOutput['video_jobs'];
    outputs: VideoOutput[];
  };
}

interface UserFilesResponse {
  success: boolean;
  data: {
    files: VideoOutput[];
    filesByJob: FilesByJob;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
    filters: {
      status: string;
      jobId: string | null;
    };
  };
}

export default function DownloadList() {
  const [filesData, setFilesData] = useState<UserFilesResponse['data'] | null>(null);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState<Set<string>>(new Set());
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<'list' | 'jobs'>('jobs');

  const fetchFiles = async (page = 1, status = statusFilter) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        status,
      });

      const response = await fetch(`/api/files/user-files?${params}`);
      const data: UserFilesResponse = await response.json();

      if (data.success) {
        setFilesData(data.data);
        setCurrentPage(page);
      } else {
        toast.error('Failed to load files');
      }
    } catch (error) {
      console.error('Error fetching files:', error);
      toast.error('Failed to load files');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFiles();
  }, []);

  const handleDownload = async (output: VideoOutput) => {
    if (!output.downloadUrl || output.isExpired) {
      toast.error('File is not available for download');
      return;
    }

    setDownloading(prev => new Set([...prev, output.id]));
    
    try {
      // Create a hidden link to trigger download
      const link = document.createElement('a');
      link.href = output.downloadUrl;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(`Downloading ${output.filename}`);
      
      // Refresh the file list to update download count
      setTimeout(() => fetchFiles(currentPage, statusFilter), 1000);
      
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Download failed');
    } finally {
      setDownloading(prev => {
        const newSet = new Set(prev);
        newSet.delete(output.id);
        return newSet;
      });
    }
  };

  const handleFilterChange = (status: string) => {
    setStatusFilter(status);
    fetchFiles(1, status);
  };

  const handlePageChange = (page: number) => {
    fetchFiles(page, statusFilter);
  };

  const formatFileSize = (bytes: number | null) => {
    if (!bytes) return 'Unknown';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatTimeRemaining = (hours: number) => {
    if (hours <= 0) return 'Expired';
    if (hours < 24) return `${hours}h remaining`;
    const days = Math.floor(hours / 24);
    return `${days}d ${hours % 24}h remaining`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'processing': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getPlatformIcon = (platformId: string) => {
    const platformIcons: Record<string, string> = {
      'instagram_story': '📸',
      'instagram_reel': '🎬',
      'tiktok': '🎵',
      'youtube_shorts': '📺',
      'twitter_video': '🐦',
      'linkedin_video': '💼'
    };
    return platformIcons[platformId] || '🎬';
  };

  if (loading && !filesData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileVideo className="h-5 w-5" />
            Your Downloads
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Loading your files...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const filesList = viewMode === 'list' ? filesData?.files || [] : [];
  const filesByJob = filesData?.filesByJob || {};
  const pagination = filesData?.pagination;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileVideo className="h-5 w-5" />
                Your Downloads
              </CardTitle>
              <CardDescription>
                Manage and download your processed videos
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Select value={statusFilter} onValueChange={handleFilterChange}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Files</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline" 
                size="sm"
                onClick={() => fetchFiles(currentPage, statusFilter)}
                disabled={loading}
              >
                <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={viewMode} onValueChange={(v) => setViewMode(v as 'list' | 'jobs')} className="space-y-4">
            <TabsList>
              <TabsTrigger value="jobs">By Project</TabsTrigger>
              <TabsTrigger value="list">All Files</TabsTrigger>
            </TabsList>

            <TabsContent value="jobs" className="space-y-4">
              {Object.keys(filesByJob).length === 0 ? (
                <div className="text-center py-8">
                  <FileVideo className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No files yet</h3>
                  <p className="text-muted-foreground">Upload and process your first video to see downloads here.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {Object.entries(filesByJob).map(([jobId, { job, outputs }]) => (
                    <Card key={jobId} className="border-l-4 border-l-blue-500">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <CardTitle className="text-base font-medium">
                              {job.original_filename}
                            </CardTitle>
                            <CardDescription className="flex items-center gap-4 mt-1">
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {new Date(job.created_at).toLocaleDateString()}
                              </span>
                              <Badge variant="outline" className={getStatusColor(job.status)}>
                                {job.status}
                              </Badge>
                            </CardDescription>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {outputs.length} {outputs.length === 1 ? 'format' : 'formats'}
                          </span>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
                          {outputs.map((output) => (
                            <div
                              key={output.id}
                              className="flex items-center justify-between p-3 rounded-lg border bg-card/50"
                            >
                              <div className="flex items-center gap-3 min-w-0 flex-1">
                                <span className="text-lg">{getPlatformIcon(output.platform_id)}</span>
                                <div className="min-w-0 flex-1">
                                  <p className="text-sm font-medium truncate">{output.preset_name}</p>
                                  <p className="text-xs text-muted-foreground">
                                    {output.resolution} • {formatFileSize(output.file_size_bytes)}
                                  </p>
                                  {output.isExpired ? (
                                    <div className="flex items-center gap-1 text-xs text-red-500 mt-1">
                                      <AlertTriangle className="h-3 w-3" />
                                      Expired
                                    </div>
                                  ) : (
                                    <p className="text-xs text-muted-foreground mt-1">
                                      {formatTimeRemaining(output.hoursUntilExpiry)}
                                    </p>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                {output.download_count > 0 && (
                                  <span className="text-xs text-muted-foreground flex items-center gap-1">
                                    <TrendingDown className="h-3 w-3" />
                                    {output.download_count}
                                  </span>
                                )}
                                <Button
                                  size="sm"
                                  variant={output.status === 'completed' && !output.isExpired ? 'default' : 'outline'}
                                  disabled={
                                    output.status !== 'completed' || 
                                    output.isExpired || 
                                    !output.downloadUrl ||
                                    downloading.has(output.id)
                                  }
                                  onClick={() => handleDownload(output)}
                                  className="shrink-0"
                                >
                                  {downloading.has(output.id) ? (
                                    <RefreshCw className="h-3 w-3 animate-spin" />
                                  ) : output.status === 'completed' && !output.isExpired ? (
                                    <Download className="h-3 w-3" />
                                  ) : output.status === 'failed' ? (
                                    <XCircle className="h-3 w-3" />
                                  ) : output.status === 'processing' ? (
                                    <Clock className="h-3 w-3" />
                                  ) : (
                                    <AlertTriangle className="h-3 w-3" />
                                  )}
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="list" className="space-y-4">
              {filesList.length === 0 ? (
                <div className="text-center py-8">
                  <FileVideo className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No files yet</h3>
                  <p className="text-muted-foreground">Upload and process your first video to see downloads here.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filesList.map((output) => (
                    <div
                      key={output.id}
                      className="flex items-center justify-between p-4 rounded-lg border bg-card"
                    >
                      <div className="flex items-center gap-4 min-w-0 flex-1">
                        <span className="text-xl">{getPlatformIcon(output.platform_id)}</span>
                        <div className="min-w-0 flex-1">
                          <p className="font-medium truncate">{output.filename}</p>
                          <div className="flex items-center gap-4 mt-1">
                            <span className="text-sm text-muted-foreground">
                              {output.resolution} • {formatFileSize(output.file_size_bytes)}
                            </span>
                            <Badge variant="outline" className={getStatusColor(output.status)}>
                              {output.status}
                            </Badge>
                          </div>
                          {output.isExpired ? (
                            <div className="flex items-center gap-1 text-sm text-red-500 mt-1">
                              <AlertTriangle className="h-4 w-4" />
                              File expired
                            </div>
                          ) : (
                            <p className="text-sm text-muted-foreground mt-1">
                              Expires: {formatTimeRemaining(output.hoursUntilExpiry)}
                            </p>
                          )}
                        </div>
                      </div>
                      <Button
                        variant={output.status === 'completed' && !output.isExpired ? 'default' : 'outline'}
                        disabled={
                          output.status !== 'completed' || 
                          output.isExpired || 
                          !output.downloadUrl ||
                          downloading.has(output.id)
                        }
                        onClick={() => handleDownload(output)}
                      >
                        {downloading.has(output.id) ? (
                          <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                        ) : output.status === 'completed' && !output.isExpired ? (
                          <Download className="h-4 w-4 mr-2" />
                        ) : output.status === 'failed' ? (
                          <XCircle className="h-4 w-4 mr-2" />
                        ) : output.status === 'processing' ? (
                          <Clock className="h-4 w-4 mr-2" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 mr-2" />
                        )}
                        {output.status === 'completed' && !output.isExpired ? 'Download' : 
                         output.status === 'failed' ? 'Failed' :
                         output.status === 'processing' ? 'Processing' : 'Expired'}
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t">
              <p className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} files
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.hasPrev || loading}
                  onClick={() => handlePageChange(pagination.page - 1)}
                >
                  Previous
                </Button>
                <span className="text-sm text-muted-foreground">
                  Page {pagination.page} of {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.hasNext || loading}
                  onClick={() => handlePageChange(pagination.page + 1)}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
