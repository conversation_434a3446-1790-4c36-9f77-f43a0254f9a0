
"use client";

import { useState, useRef, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Upload, Video, AlertCircle, CheckCircle, X } from "lucide-react";
import { toast } from "sonner";

interface Platform {
  platform: string;
  displayName: string;
  description: string;
  category: string;
  maxDuration: number;
  aspectRatio: string;
  maxFileSize: number;
}

interface VideoUploadProps {
  onUploadSuccess: (job: any) => void;
}

export function VideoUploadComponent({ onUploadSuccess }: VideoUploadProps) {
  const { user } = useUser();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [file, setFile] = useState<File | null>(null);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragActive, setDragActive] = useState(false);
  const [platforms, setPlatforms] = useState<Platform[]>([]);

  // Load platforms from default rules (fallback)
  useEffect(() => {
    const defaultPlatforms: Platform[] = [
      {
        platform: "instagram_story",
        displayName: "Instagram Story",
        description: "Vertical video format for Instagram Stories",
        category: "Social Media",
        maxDuration: 15,
        aspectRatio: "9:16",
        maxFileSize: 100,
      },
      {
        platform: "instagram_reel",
        displayName: "Instagram Reel",
        description: "Vertical video format for Instagram Reels",
        category: "Social Media",
        maxDuration: 90,
        aspectRatio: "9:16",
        maxFileSize: 500,
      },
      {
        platform: "youtube_shorts",
        displayName: "YouTube Shorts",
        description: "Vertical short-form content for YouTube",
        category: "Video Platform",
        maxDuration: 60,
        aspectRatio: "9:16",
        maxFileSize: 512,
      },
      {
        platform: "tiktok",
        displayName: "TikTok",
        description: "Vertical video format for TikTok",
        category: "Social Media",
        maxDuration: 180,
        aspectRatio: "9:16",
        maxFileSize: 500,
      },
      {
        platform: "facebook_feed",
        displayName: "Facebook Feed",
        description: "Landscape video for Facebook feed posts",
        category: "Social Media",
        maxDuration: 240,
        aspectRatio: "16:9",
        maxFileSize: 1024,
      },
      {
        platform: "twitter",
        displayName: "Twitter/X",
        description: "Video format for Twitter/X posts",
        category: "Social Media",
        maxDuration: 140,
        aspectRatio: "16:9",
        maxFileSize: 512,
      },
      {
        platform: "linkedin",
        displayName: "LinkedIn",
        description: "Professional video content for LinkedIn",
        category: "Professional",
        maxDuration: 300,
        aspectRatio: "16:9",
        maxFileSize: 1024,
      }
    ];
    setPlatforms(defaultPlatforms);
  }, []);

  // Group platforms by category
  const platformsByCategory = platforms.reduce((acc, platform) => {
    if (!acc[platform.category]) {
      acc[platform.category] = [];
    }
    acc[platform.category].push(platform);
    return acc;
  }, {} as Record<string, Platform[]>);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileSelection = (selectedFile: File) => {
    // Validate file type
    const validTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/mkv', 'video/webm'];
    if (!validTypes.includes(selectedFile.type)) {
      toast.error("Please select a valid video file (MP4, MOV, AVI, MKV, WebM)");
      return;
    }

    // Validate file size (5GB limit)
    const maxSize = 5 * 1024 * 1024 * 1024; // 5GB
    if (selectedFile.size > maxSize) {
      toast.error("File size must be less than 5GB");
      return;
    }

    setFile(selectedFile);
    toast.success("Video file selected successfully!");
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileSelection(files[0]);
    }
  };

  const handlePlatformToggle = (platformKey: string) => {
    setSelectedPlatforms(prev => 
      prev.includes(platformKey)
        ? prev.filter(p => p !== platformKey)
        : [...prev, platformKey]
    );
  };

  const [debugInfo, setDebugInfo] = useState<string>("");
  const [lastError, setLastError] = useState<string>("");

  const logDebug = (step: string, details?: any) => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${step}${details ? ': ' + JSON.stringify(details) : ''}`;
    console.log(logMessage);
    setDebugInfo(prev => prev + '\n' + logMessage);
  };

  const handleUpload = async () => {
    if (!file || !user || selectedPlatforms.length === 0) {
      toast.error("Please select a file and at least one platform");
      return;
    }

    setUploading(true);
    setUploadProgress(0);
    setDebugInfo("");
    setLastError("");

    try {
      logDebug("Starting upload process", { 
        fileName: file.name, 
        fileType: file.type, 
        fileSize: file.size,
        platforms: selectedPlatforms,
        userId: user.id 
      });

      // Get presigned URL for S3 upload
      logDebug("Requesting presigned URL from API");
      const presignedResponse = await fetch('/api/upload/presigned', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
        }),
      });

      logDebug("Presigned URL response received", { 
        status: presignedResponse.status, 
        statusText: presignedResponse.statusText 
      });

      if (!presignedResponse.ok) {
        const errorText = await presignedResponse.text();
        logDebug("Presigned URL request failed", { errorText });
        throw new Error(`Failed to get upload URL: ${presignedResponse.status} - ${errorText}`);
      }

      const presignedData = await presignedResponse.json();
      logDebug("Presigned URL data received", { 
        hasUploadUrl: !!presignedData.uploadUrl, 
        hasKey: !!presignedData.key, 
        hasJobId: !!presignedData.jobId 
      });

      const { uploadUrl, key, jobId } = presignedData;

      // Upload file to S3 with progress tracking
      logDebug("Starting S3 upload", { uploadUrlLength: uploadUrl?.length });
      const xhr = new XMLHttpRequest();
      
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 80; // Reserve 20% for processing
          setUploadProgress(progress);
          logDebug(`Upload progress: ${progress.toFixed(1)}%`);
        }
      };

      const uploadPromise = new Promise((resolve, reject) => {
        xhr.onload = async () => {
          try {
            logDebug("S3 upload completed", { 
              status: xhr.status, 
              statusText: xhr.statusText,
              responseHeaders: xhr.getAllResponseHeaders()
            });

            if (xhr.status === 200 || xhr.status === 204) {
              setUploadProgress(80);
              logDebug("S3 upload successful, creating job record");
              
              // Create job record
              const jobPayload = {
                jobId,
                fileName: file.name,
                s3Key: key,
                platforms: selectedPlatforms,
              };
              
              logDebug("Sending job creation request", jobPayload);
              const jobResponse = await fetch('/api/jobs/create', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(jobPayload),
              });

              logDebug("Job creation response", { 
                status: jobResponse.status, 
                statusText: jobResponse.statusText 
              });

              if (jobResponse.ok) {
                const response = await jobResponse.json();
                console.log("🔥 FULL RESPONSE:", response); // Debug log
                const job = response.job; // Extract the job from the response
                console.log("🔥 EXTRACTED JOB:", job); // Debug log
                logDebug("Job created successfully", { jobId: job.id });
                setUploadProgress(100);

                toast.success("Upload successful! Processing started.");
                onUploadSuccess(job); // Pass only the job data, not the full response

                // Reset form
                setFile(null);
                setSelectedPlatforms([]);
                if (fileInputRef.current) {
                  fileInputRef.current.value = '';
                }
                resolve(job);
              } else {
                const errorText = await jobResponse.text();
                logDebug("Job creation failed", { status: jobResponse.status, error: errorText });
                reject(new Error(`Failed to create job record: ${jobResponse.status} - ${errorText}`));
              }
            } else {
              logDebug("S3 upload failed", { 
                status: xhr.status, 
                statusText: xhr.statusText,
                response: xhr.responseText 
              });
              reject(new Error(`S3 upload failed with status: ${xhr.status} - ${xhr.statusText}`));
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error in upload completion';
            logDebug("Error in upload completion handler", { error: errorMessage });
            reject(new Error(errorMessage));
          }
        };

        xhr.onerror = (event) => {
          logDebug("XMLHttpRequest error event", { 
            status: xhr.status, 
            readyState: xhr.readyState,
            event
          });
          reject(new Error(`Upload failed: Network error. Status: ${xhr.status}, ReadyState: ${xhr.readyState}`));
        };

        xhr.ontimeout = () => {
          logDebug("XMLHttpRequest timeout");
          reject(new Error('Upload timed out after 5 minutes'));
        };

        xhr.onabort = () => {
          logDebug("XMLHttpRequest aborted");
          reject(new Error('Upload was aborted'));
        };

        logDebug("Configuring XMLHttpRequest", { 
          method: 'PUT', 
          timeout: 300000,
          contentType: file.type 
        });

        xhr.open('PUT', uploadUrl);
        xhr.setRequestHeader('Content-Type', file.type);
        xhr.timeout = 300000; // 5 minute timeout
        xhr.send(file);

        logDebug("XMLHttpRequest sent, waiting for response");
      });

      await uploadPromise;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const errorStack = error instanceof Error ? error.stack : undefined;
      logDebug("Upload process failed", { error: errorMessage, stack: errorStack });
      setLastError(errorMessage);
      
      // Show specific error based on the failure point
      if (errorMessage.includes('presigned URL')) {
        toast.error("Failed to prepare upload. Please check your connection and try again.");
      } else if (errorMessage.includes('S3 upload')) {
        toast.error("Failed to upload file to storage. Please try again with a smaller file or check your connection.");
      } else if (errorMessage.includes('job record')) {
        toast.error("File uploaded but failed to start processing. Please contact support.");
      } else if (errorMessage.includes('timeout')) {
        toast.error("Upload timed out. Please try again with a smaller file.");
      } else if (errorMessage.includes('Network error')) {
        toast.error("Network connection issue. Please check your internet and try again.");
      } else {
        toast.error(`Upload failed: ${errorMessage}`);
      }
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* File Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="h-5 w-5" />
            <span>Upload Video</span>
          </CardTitle>
          <CardDescription>
            Select a video file to repurpose for multiple social media platforms. Maximum file size: 5GB.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive 
                ? 'border-primary bg-primary/10' 
                : 'border-gray-300 dark:border-gray-700 hover:border-primary hover:bg-primary/5'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            {file ? (
              <div className="space-y-4">
                <div className="flex items-center justify-center space-x-2">
                  <Video className="h-8 w-8 text-primary" />
                  <div className="text-left">
                    <p className="font-medium">{file.name}</p>
                    <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setFile(null);
                      if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                      }
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                {uploading && (
                  <div className="space-y-2">
                    <Progress value={uploadProgress} className="w-full" />
                    <p className="text-sm text-gray-600">
                      {uploadProgress < 80 ? 'Uploading...' : 'Processing...'}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <Video className="h-12 w-12 text-gray-400 mx-auto" />
                <div>
                  <p className="text-lg font-medium">Drop your video here</p>
                  <p className="text-gray-500">or click to browse files</p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Choose File
                </Button>
              </div>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept="video/*"
            onChange={handleFileInput}
          />
        </CardContent>
      </Card>

      {/* Platform Selection */}
      {file && (
        <Card>
          <CardHeader>
            <CardTitle>Select Platforms</CardTitle>
            <CardDescription>
              Choose which social media platforms you want to optimize your video for.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {Object.entries(platformsByCategory).map(([category, categoryPlatforms]) => (
              <div key={category} className="space-y-3">
                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                  {category}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {categoryPlatforms.map((platform) => (
                    <div key={platform.platform} className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                      <Checkbox
                        id={platform.platform}
                        checked={selectedPlatforms.includes(platform.platform)}
                        onCheckedChange={() => handlePlatformToggle(platform.platform)}
                        className="mt-1"
                      />
                      <Label
                        htmlFor={platform.platform}
                        className="flex-1 cursor-pointer space-y-1"
                      >
                        <div className="flex items-center justify-between">
                          <p className="font-medium text-sm">{platform.displayName}</p>
                          <Badge variant="outline" className="text-xs">
                            {platform.aspectRatio}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-500 line-clamp-2">{platform.description}</p>
                        <div className="flex items-center gap-2 text-xs text-gray-400">
                          <span>Max: {platform.maxDuration}s</span>
                          <span>•</span>
                          <span>{platform.maxFileSize}MB</span>
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            ))}
            
            {selectedPlatforms.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <p className="text-sm font-medium mb-2">Selected platforms ({selectedPlatforms.length}):</p>
                <div className="flex flex-wrap gap-2">
                  {selectedPlatforms.map(platformKey => {
                    const platform = platforms.find(p => p.platform === platformKey);
                    return (
                      <Badge key={platformKey} variant="secondary" className="flex items-center gap-1">
                        {platform?.displayName}
                        <X 
                          className="h-3 w-3 cursor-pointer hover:text-red-500" 
                          onClick={(e) => {
                            e.preventDefault();
                            handlePlatformToggle(platformKey);
                          }}
                        />
                      </Badge>
                    );
                  })}
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Your video will be optimized for these platforms with their specific requirements.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Upload Button */}
      {file && selectedPlatforms.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <Button
              onClick={handleUpload}
              disabled={uploading}
              className="w-full"
              size="lg"
            >
              {uploading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Uploading...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Upload className="h-4 w-4" />
                  <span>Start Processing</span>
                </div>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Debug Information Panel */}
      {(debugInfo || lastError) && (
        <Card className="border-dashed">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center space-x-2 text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>Debug Information</span>
            </CardTitle>
            <CardDescription className="text-xs">
              This shows detailed logging to help identify upload issues. Check browser console for more details.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {lastError && (
              <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-red-800 dark:text-red-200">Last Error</p>
                    <p className="text-sm text-red-700 dark:text-red-300 break-words">{lastError}</p>
                  </div>
                </div>
              </div>
            )}
            
            {debugInfo && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">Process Log</p>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      navigator.clipboard.writeText(debugInfo);
                      toast.success("Debug info copied to clipboard");
                    }}
                    className="h-6 text-xs"
                  >
                    Copy Logs
                  </Button>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-900 border rounded-md max-h-48 overflow-y-auto">
                  <pre className="text-xs font-mono text-gray-700 dark:text-gray-300 whitespace-pre-wrap break-words">
                    {debugInfo.trim()}
                  </pre>
                </div>
              </div>
            )}
            
            <div className="text-xs text-gray-500 bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
              <strong>💡 Troubleshooting Tips:</strong><br/>
              • If "presigned URL" fails → Check API configuration and AWS credentials<br/>
              • If "S3 upload" fails → Check file size (&lt;5GB) and internet connection<br/>
              • If "job creation" fails → Check database connectivity<br/>
              • If upload keeps failing → Try a smaller test file first
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
