
"use client";

import { useState, useEffect } from 'react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, CheckCircle, AlertCircle, Clock } from 'lucide-react';

interface UploadProgressProps {
  progress: number;
  status: 'idle' | 'uploading' | 'processing' | 'completed' | 'error';
  fileName?: string;
  errorMessage?: string;
  estimatedTimeRemaining?: number;
}

export function EnhancedUploadProgress({ 
  progress, 
  status, 
  fileName, 
  errorMessage, 
  estimatedTimeRemaining 
}: UploadProgressProps) {
  const [displayProgress, setDisplayProgress] = useState(progress);
  
  // Smooth progress animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setDisplayProgress(progress);
    }, 100);
    return () => clearTimeout(timer);
  }, [progress]);

  const getStatusInfo = () => {
    switch (status) {
      case 'uploading':
        return {
          icon: <Upload className="h-4 w-4 animate-bounce" />,
          color: 'bg-blue-500',
          text: 'Uploading to cloud storage...',
          badgeVariant: 'default' as const
        };
      case 'processing':
        return {
          icon: <Clock className="h-4 w-4 animate-spin" />,
          color: 'bg-yellow-500',
          text: 'Creating job record...',
          badgeVariant: 'secondary' as const
        };
      case 'completed':
        return {
          icon: <CheckCircle className="h-4 w-4" />,
          color: 'bg-green-500',
          text: 'Upload completed successfully!',
          badgeVariant: 'default' as const
        };
      case 'error':
        return {
          icon: <AlertCircle className="h-4 w-4" />,
          color: 'bg-red-500',
          text: errorMessage || 'Upload failed',
          badgeVariant: 'destructive' as const
        };
      default:
        return {
          icon: <Upload className="h-4 w-4" />,
          color: 'bg-gray-500',
          text: 'Ready to upload',
          badgeVariant: 'outline' as const
        };
    }
  };

  const statusInfo = getStatusInfo();

  if (status === 'idle') return null;

  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {statusInfo.icon}
              <span className="font-medium">{fileName || 'Processing...'}</span>
            </div>
            <Badge variant={statusInfo.badgeVariant}>
              {status === 'uploading' && `${Math.round(displayProgress)}%`}
              {status === 'processing' && 'Processing'}
              {status === 'completed' && 'Done'}
              {status === 'error' && 'Error'}
            </Badge>
          </div>

          {/* Progress Bar */}
          {(status === 'uploading' || status === 'processing') && (
            <div className="space-y-2">
              <Progress value={displayProgress} className="w-full" />
              <div className="flex justify-between text-sm text-gray-600">
                <span>{statusInfo.text}</span>
                {estimatedTimeRemaining && status === 'uploading' && (
                  <span>~{formatTime(estimatedTimeRemaining)} remaining</span>
                )}
              </div>
            </div>
          )}

          {/* Status Message */}
          <div className={`text-sm p-3 rounded-lg ${
            status === 'completed' ? 'bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-200' :
            status === 'error' ? 'bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-200' :
            'bg-blue-50 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200'
          }`}>
            {statusInfo.text}
          </div>

          {/* Upload Speed & File Size Info */}
          {status === 'uploading' && (
            <div className="grid grid-cols-2 gap-4 text-sm text-gray-500">
              <div>Progress: {Math.round(displayProgress)}%</div>
              {estimatedTimeRemaining && (
                <div>ETA: {formatTime(estimatedTimeRemaining)}</div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
