
"use client";

import { useUser } from "@clerk/nextjs";
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { 
  User, 
  Mail, 
  Calendar, 
  Crown, 
  CreditCard,
  Settings,
  BarChart3,
  Video,
  Clock,
  CheckCircle
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";

interface UserStats {
  totalJobs: number;
  completedJobs: number;
  processingJobs: number;
  totalProcessingTime: number;
  storageUsed: number;
  storageLimit: number;
}

interface SubscriptionInfo {
  status: 'free_trial' | 'active' | 'canceled' | 'expired';
  planName: string;
  currentPeriodEnd?: string;
  jobsRemaining?: number;
  jobsLimit?: number;
}

export function UserProfile() {
  const { user } = useUser();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [subscription, setSubscription] = useState<SubscriptionInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!user) return;

      try {
        const [statsResponse, subscriptionResponse] = await Promise.all([
          fetch('/api/user/stats'),
          fetch('/api/payments/subscription')
        ]);

        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStats(statsData);
        }

        if (subscriptionResponse.ok) {
          const subscriptionData = await subscriptionResponse.json();
          setSubscription(subscriptionData);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [user]);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getSubscriptionBadgeColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'free_trial':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'expired':
      case 'canceled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <p>Please sign in to view your profile.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Profile Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center">
              {user.imageUrl ? (
                <img 
                  src={user.imageUrl} 
                  alt="Profile"
                  className="w-16 h-16 rounded-full"
                />
              ) : (
                <User className="h-8 w-8 text-primary" />
              )}
            </div>
            <div>
              <h3 className="text-xl font-semibold">
                {user.firstName} {user.lastName}
              </h3>
              <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                <Mail className="h-4 w-4" />
                <span>{user.primaryEmailAddress?.emailAddress}</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 mt-1">
                <Calendar className="h-4 w-4" />
                <span>
                  Member since {formatDistanceToNow(new Date(user.createdAt || Date.now()), { addSuffix: true })}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subscription Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Crown className="h-5 w-5" />
            <span>Subscription</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <span className="ml-2">Loading subscription info...</span>
            </div>
          ) : subscription ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">{subscription.planName}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {subscription.status === 'free_trial' ? 'Free Trial' : 'Pro Plan'}
                  </p>
                </div>
                <Badge className={getSubscriptionBadgeColor(subscription.status)}>
                  {subscription.status.replace('_', ' ').charAt(0).toUpperCase() + subscription.status.slice(1)}
                </Badge>
              </div>

              {subscription.currentPeriodEnd && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {subscription.status === 'active' ? 'Renews' : 'Expires'} on{' '}
                  {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                </p>
              )}

              {subscription.jobsLimit && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Jobs this month</span>
                    <span>
                      {(subscription.jobsLimit - (subscription.jobsRemaining || 0))} / {subscription.jobsLimit}
                    </span>
                  </div>
                  <Progress 
                    value={((subscription.jobsLimit - (subscription.jobsRemaining || 0)) / subscription.jobsLimit) * 100} 
                    className="w-full"
                  />
                </div>
              )}

              <Separator />

              <div className="flex space-x-2">
                {subscription.status === 'free_trial' && (
                  <Button asChild>
                    <Link href="/pricing">
                      <CreditCard className="h-4 w-4 mr-2" />
                      Upgrade to Pro
                    </Link>
                  </Button>
                )}
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Manage Subscription
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                No subscription found
              </p>
              <Button asChild>
                <Link href="/pricing">
                  <Crown className="h-4 w-4 mr-2" />
                  Get Started
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Usage Statistics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <span className="ml-2">Loading statistics...</span>
            </div>
          ) : stats ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <Video className="h-6 w-6 text-primary mx-auto mb-2" />
                <p className="text-2xl font-bold">{stats.totalJobs}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Jobs</p>
              </div>

              <div className="text-center p-4 border rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600 mx-auto mb-2" />
                <p className="text-2xl font-bold">{stats.completedJobs}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
              </div>

              <div className="text-center p-4 border rounded-lg">
                <Clock className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                <p className="text-2xl font-bold">{stats.processingJobs}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Processing</p>
              </div>

              <div className="text-center p-4 border rounded-lg">
                <BarChart3 className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                <p className="text-2xl font-bold">
                  {stats.totalProcessingTime ? formatDuration(stats.totalProcessingTime) : '0m'}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Processing Time</p>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-600 dark:text-gray-400">
                No usage statistics available yet
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Storage Usage */}
      {stats && (
        <Card>
          <CardHeader>
            <CardTitle>Storage Usage</CardTitle>
            <CardDescription>
              Monitor your storage usage and remaining capacity
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Used Storage</span>
                <span>
                  {formatBytes(stats.storageUsed)} / {formatBytes(stats.storageLimit)}
                </span>
              </div>
              <Progress 
                value={(stats.storageUsed / stats.storageLimit) * 100} 
                className="w-full"
              />
            </div>
            
            {stats.storageUsed / stats.storageLimit > 0.8 && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-3">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  ⚠️ You're running low on storage space. Consider upgrading your plan or clearing old files.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
