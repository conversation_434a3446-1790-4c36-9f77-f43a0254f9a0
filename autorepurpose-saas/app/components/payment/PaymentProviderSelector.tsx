
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, CreditCard, Smartphone } from 'lucide-react';

interface PaymentProvider {
  id: 'stripe' | 'razorpay';
  name: string;
  description: string;
  currency: string;
  amount: string;
  features: string[];
  icon: React.ReactNode;
  region: string;
}

const paymentProviders: PaymentProvider[] = [
  {
    id: 'stripe',
    name: 'Stripe',
    description: 'Secure international payments',
    currency: 'USD',
    amount: '$30/month',
    features: ['Credit/Debit Cards', 'International', 'Apple Pay', 'Google Pay'],
    icon: <CreditCard className="h-6 w-6" />,
    region: 'Global'
  },
  {
    id: 'razorpay',
    name: 'Razorpay',
    description: 'Comprehensive India payments',
    currency: 'INR',
    amount: '₹2,500/month',
    features: ['UPI', 'Net Banking', 'Cards', 'Wallets', 'EMI Options'],
    icon: <Smartphone className="h-6 w-6" />,
    region: 'India'
  }
];

interface PaymentProviderSelectorProps {
  onProviderSelect: (provider: 'stripe' | 'razorpay') => void;
  selectedProvider?: 'stripe' | 'razorpay';
  loading?: boolean;
}

export default function PaymentProviderSelector({
  onProviderSelect,
  selectedProvider,
  loading = false
}: PaymentProviderSelectorProps) {
  const [selected, setSelected] = useState<'stripe' | 'razorpay' | null>(
    selectedProvider || null
  );

  const handleSelect = (providerId: 'stripe' | 'razorpay') => {
    setSelected(providerId);
    onProviderSelect(providerId);
  };

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold mb-2">Choose Payment Method</h3>
        <p className="text-muted-foreground">
          Select your preferred payment provider to continue with subscription
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {paymentProviders.map((provider) => (
          <Card
            key={provider.id}
            className={`cursor-pointer transition-all hover:shadow-md ${
              selected === provider.id
                ? 'ring-2 ring-primary border-primary'
                : 'hover:border-primary/50'
            }`}
            onClick={() => handleSelect(provider.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {provider.icon}
                  <div>
                    <CardTitle className="text-lg">{provider.name}</CardTitle>
                    <Badge variant="outline" className="text-xs">
                      {provider.region}
                    </Badge>
                  </div>
                </div>
                {selected === provider.id && (
                  <CheckCircle className="h-5 w-5 text-primary" />
                )}
              </div>
              <CardDescription>{provider.description}</CardDescription>
            </CardHeader>

            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="font-medium">Price:</span>
                <span className="font-bold text-lg">{provider.amount}</span>
              </div>

              <div>
                <span className="text-sm font-medium mb-2 block">Supported Methods:</span>
                <div className="flex flex-wrap gap-1">
                  {provider.features.map((feature, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {selected && (
        <div className="flex justify-center mt-6">
          <Button
            size="lg"
            onClick={() => onProviderSelect(selected)}
            disabled={loading}
            className="min-w-[200px]"
          >
            {loading ? 'Processing...' : `Continue with ${paymentProviders.find(p => p.id === selected)?.name}`}
          </Button>
        </div>
      )}
    </div>
  );
}
