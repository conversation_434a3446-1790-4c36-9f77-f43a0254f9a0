
"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { 
  Video, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Download, 
  RefreshCw,
  Calendar,
  FileVideo,
  Smartphone
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface VideoOutput {
  id: string;
  platform: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'queued';
  downloadUrl?: string;
  fileSize?: number;
  duration?: number;
}

interface VideoJob {
  id: string;
  originalFileName: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'queued';
  platformsRequested: string[];
  createdAt: string;
  updatedAt?: string;
  progress?: number;
  errorMessage?: string;
  outputs?: VideoOutput[];
}

interface JobStatusListProps {
  jobs: VideoJob[];
  loading: boolean;
}

export function JobStatusList({ jobs, loading }: JobStatusListProps) {
  const [refreshing, setRefreshing] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
      case 'queued':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
      case 'queued':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const handleDownload = async (downloadUrl: string, fileName: string) => {
    try {
      const response = await fetch(downloadUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (!bytes) return 'Unknown size';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    if (!seconds) return 'Unknown duration';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const safeFormatDate = (dateString: string | undefined) => {
    if (!dateString) {
      console.warn('Invalid date string:', dateString);
      return 'Recently';
    }
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.warn('Invalid date string:', dateString);
        return 'Recently';
      }
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      console.warn('Date parsing error:', error, 'for value:', dateString);
      return 'Recently';
    }
  };

  const safeArrayLength = (arr: any) => {
    if (!arr || !Array.isArray(arr)) {
      return 0;
    }
    return arr.length;
  };

  const handleDeleteJob = async (jobId: string) => {
    if (!confirm('Are you sure you want to delete this job? This will also delete the uploaded file from S3.')) {
      return;
    }

    try {
      const response = await fetch('/api/jobs/cleanup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ jobId }),
      });

      if (response.ok) {
        // Trigger parent component refresh
        window.location.reload();
      } else {
        const error = await response.json();
        alert(`Failed to delete job: ${error.error}`);
      }
    } catch (error) {
      console.error('Error deleting job:', error);
      alert('Failed to delete job. Please try again.');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading jobs...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (jobs.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Video className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No video jobs yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Upload your first video to get started with repurposing!
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Your Video Jobs</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            setRefreshing(true);
            // Trigger parent component to refresh
            setTimeout(() => setRefreshing(false), 1000);
          }}
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {jobs.map((job) => (
        <Card key={job.id} className="overflow-hidden">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <FileVideo className="h-5 w-5 text-primary" />
                <div>
                  <CardTitle className="text-lg">{job.originalFileName}</CardTitle>
                  <CardDescription className="flex items-center space-x-4 mt-1">
                    <span className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3" />
                      <span>{safeFormatDate(job.createdAt)}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Smartphone className="h-3 w-3" />
                      <span>{safeArrayLength(job.platformsRequested)} platforms</span>
                    </span>
                  </CardDescription>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {getStatusIcon(job.status)}
                <Badge className={getStatusColor(job.status)}>
                  {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                </Badge>
                {(job.status === 'failed' || (job.status === 'queued' && new Date().getTime() - new Date(job.createdAt).getTime() > 10 * 60 * 1000)) && (
                  <Button
                    size="sm"
                    variant="outline"
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={() => handleDeleteJob(job.id)}
                  >
                    <XCircle className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Progress Bar for Processing Jobs */}
            {job.status === 'processing' && job.progress !== undefined && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Processing progress</span>
                  <span>{job.progress}%</span>
                </div>
                <Progress value={job.progress} className="w-full" />
              </div>
            )}

            {/* Error Message */}
            {job.status === 'failed' && job.errorMessage && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
                <div className="flex items-center space-x-2">
                  <XCircle className="h-4 w-4 text-red-600" />
                  <p className="text-sm text-red-800 dark:text-red-200">
                    {job.errorMessage}
                  </p>
                </div>
              </div>
            )}

            {/* Platform Outputs */}
            {job.outputs && safeArrayLength(job.outputs) > 0 && (
              <div className="space-y-3">
                <Separator />
                <h4 className="font-medium">Platform Outputs</h4>
                <div className="grid gap-3">
                  {(job.outputs || []).map((output, index) => (
                    <div
                      key={output?.id || `output-${index}`}
                      className="flex items-center justify-between p-3 border rounded-lg bg-gray-50 dark:bg-gray-800/50"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(output.status)}
                          <span className="font-medium capitalize">
                            {output.platform}
                          </span>
                        </div>
                        
                        {output.fileSize && output.duration && (
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {formatFileSize(output.fileSize)} • {formatDuration(output.duration)}
                          </div>
                        )}
                      </div>

                      {output.status === 'completed' && output.downloadUrl && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDownload(
                            output.downloadUrl!, 
                            `${job.originalFileName.split('.')[0]}_${output.platform}.mp4`
                          )}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      )}

                      {output.status === 'processing' && (
                        <Badge variant="secondary">
                          Processing...
                        </Badge>
                      )}

                      {output.status === 'failed' && (
                        <Badge variant="destructive">
                          Failed
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Requested Platforms (when no outputs yet) */}
            {(!job.outputs || safeArrayLength(job.outputs) === 0) && (
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Requested Platforms:</h4>
                <div className="flex flex-wrap gap-2">
                  {(job.platformsRequested || []).map((platform, index) => (
                    <Badge key={`${platform}-${index}`} variant="outline">
                      {platform ? platform.charAt(0).toUpperCase() + platform.slice(1) : 'Unknown'}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
