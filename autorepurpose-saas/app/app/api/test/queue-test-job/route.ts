
import { NextResponse } from 'next/server';
import { Queue } from 'bullmq';
import IORedis from 'ioredis';

export async function POST(request: Request) {
  try {
    const { testType = 'simple' } = await request.json();

    const redisUrl = process.env.REDIS_URL;
    if (!redisUrl) {
      return NextResponse.json({
        success: false,
        error: 'Redis URL not configured'
      }, { status: 500 });
    }

    // Create Redis connection
    const redis = new IORedis(redisUrl, {
      maxRetriesPerRequest: 3,
    });

    // Create queue
    const videoQueue = new Queue('video-processing', {
      connection: redis,
    });

    // Test job data
    const testJobData = {
      id: `test_job_${Date.now()}`,
      user_id: 'test_user',
      original_filename: 'test_video.mp4',
      s3_input_key: 'test/sample_video.mp4',
      file_size_bytes: 1024000, // 1MB
      duration_seconds: 30,
      target_platforms: testType === 'simple' ? ['instagram_story'] : ['instagram_story', 'youtube_shorts', 'tiktok'],
      status: 'queued',
      created_at: new Date().toISOString(),
    };

    console.log(`Adding test job to queue: ${testJobData.id}`);

    // Add job to queue
    const job = await videoQueue.add('process-video', testJobData, {
      attempts: 1, // Only try once for testing
      removeOnComplete: 10,
      removeOnFail: 10,
    });

    // Clean up Redis connection
    await redis.disconnect();

    return NextResponse.json({
      success: true,
      message: 'Test job queued successfully',
      jobId: job.id,
      testJobData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Queue test job error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const redisUrl = process.env.REDIS_URL;
    if (!redisUrl) {
      return NextResponse.json({
        success: false,
        error: 'Redis URL not configured'
      }, { status: 500 });
    }

    // Create Redis connection
    const redis = new IORedis(redisUrl, {
      maxRetriesPerRequest: 3,
    });

    // Create queue
    const videoQueue = new Queue('video-processing', {
      connection: redis,
    });

    // Get queue stats
    const waiting = await videoQueue.getWaiting();
    const active = await videoQueue.getActive();
    const completed = await videoQueue.getCompleted();
    const failed = await videoQueue.getFailed();

    await redis.disconnect();

    return NextResponse.json({
      success: true,
      queueStats: {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
      },
      recentJobs: {
        waiting: waiting.slice(0, 3).map(job => ({
          id: job.id,
          data: job.data,
          timestamp: job.timestamp
        })),
        active: active.slice(0, 3).map(job => ({
          id: job.id,
          data: job.data,
          timestamp: job.timestamp
        })),
        completed: completed.slice(0, 3).map(job => ({
          id: job.id,
          timestamp: job.timestamp
        })),
        failed: failed.slice(0, 3).map(job => ({
          id: job.id,
          failedReason: job.failedReason,
          timestamp: job.timestamp
        }))
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Queue stats error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
