
import { NextResponse } from 'next/server';
import path from 'path';
import { readFileSync } from 'fs';

// Test endpoint to verify platform rules are loaded
export async function GET() {
  try {
    const rulesPath = path.join(process.cwd(), 'lib', 'platform-rules.json');
    const rules = JSON.parse(readFileSync(rulesPath, 'utf8'));
    
    const platforms = Object.keys(rules.platforms || {});
    
    return NextResponse.json({
      success: true,
      message: 'Platform rules loaded successfully',
      version: rules.metadata?.version || 'unknown',
      platforms_count: platforms.length,
      platforms: platforms.slice(0, 5), // Show first 5 platforms
      form_types_supported: rules.metadata?.form_type_support || false
    });
  } catch (error: any) {
    console.error('Platform rules error:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}
