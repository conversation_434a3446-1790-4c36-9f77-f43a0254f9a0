

import { NextRequest, NextResponse } from 'next/server';
import { S3Client, HeadBucketCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import Redis from 'ioredis';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  const results: any = {
    timestamp: new Date().toISOString(),
    tests: {}
  };

  // Test AWS S3 Connection
  try {
    const s3Client = new S3Client({
      region: process.env.AWS_REGION!,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      },
    });

    // Test bucket access
    const headBucketCommand = new HeadBucketCommand({ 
      Bucket: process.env.S3_BUCKET_NAME! 
    });
    
    await s3Client.send(headBucketCommand);
    
    results.tests.s3 = {
      status: 'success',
      message: 'S3 bucket accessible',
      bucket: process.env.S3_BUCKET_NAME,
      region: process.env.AWS_REGION
    };

  } catch (error: any) {
    results.tests.s3 = {
      status: 'error',
      message: error.message || 'S3 connection failed',
      bucket: process.env.S3_BUCKET_NAME,
      region: process.env.AWS_REGION
    };
  }

  // Test Redis/Upstash Connection
  try {
    const redis = new Redis(process.env.REDIS_URL!, {
      maxRetriesPerRequest: null,
      enableReadyCheck: false,
      lazyConnect: true,
    });

    // Test Redis connection
    await redis.ping();
    
    results.tests.redis = {
      status: 'success',
      message: 'Redis connection successful',
      url: process.env.REDIS_URL?.split('@')[1] || 'Redis URL configured'
    };

    await redis.quit();

  } catch (error: any) {
    results.tests.redis = {
      status: 'error',
      message: error.message || 'Redis connection failed',
      url: process.env.REDIS_URL?.split('@')[1] || 'Redis URL not configured'
    };
  }

  // Test Environment Variables
  const requiredEnvVars = [
    'AWS_REGION',
    'AWS_ACCESS_KEY_ID', 
    'AWS_SECRET_ACCESS_KEY',
    'S3_BUCKET_NAME',
    'REDIS_URL',
    'DATABASE_URL'
  ];

  const envStatus: any = {};
  requiredEnvVars.forEach(key => {
    envStatus[key] = {
      present: !!process.env[key],
      value: process.env[key] ? `${process.env[key]?.substring(0, 10)}...` : 'NOT SET'
    };
  });

  results.tests.environment = {
    status: requiredEnvVars.every(key => !!process.env[key]) ? 'success' : 'error',
    variables: envStatus
  };

  // Overall status
  const allTestsPassed = Object.values(results.tests).every((test: any) => test.status === 'success');
  results.overall = {
    status: allTestsPassed ? 'success' : 'error',
    message: allTestsPassed ? 'All connections successful' : 'Some connections failed'
  };

  return NextResponse.json(results, { status: allTestsPassed ? 200 : 500 });
}
