

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { DatabaseService } from '@/lib/db-extensions';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Checking user in database for Clerk ID:', userId);

    // Check if user exists in database
    const user = await DatabaseService.getUserByClerkId(userId);

    if (user) {
      return NextResponse.json({
        success: true,
        message: 'User found in database',
        user: {
          id: user.id,
          clerkUserId: user.clerk_user_id,
          email: user.email,
          fullName: user.full_name,
          subscriptionStatus: user.subscription_status,
          createdAt: user.created_at
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'User not found in database',
        clerkUserId: userId,
        suggestion: 'User will be auto-created on next job creation'
      });
    }

  } catch (error: any) {
    console.error('Error checking user:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to check user',
      details: error.message
    }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Force creating user for Clerk ID:', userId);

    // Force create/update user
    const user = await DatabaseService.upsertUserFromClerk({
      id: userId,
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User'
    });

    return NextResponse.json({
      success: true,
      message: 'User created/updated successfully',
      user: {
        id: user.id,
        clerkUserId: user.clerk_user_id,
        email: user.email,
        fullName: user.full_name
      }
    });

  } catch (error: any) {
    console.error('Error creating user:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create user',
      details: error.message
    }, { status: 500 });
  }
}

