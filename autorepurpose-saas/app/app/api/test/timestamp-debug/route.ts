
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Manually create Supabase client to test timestamps
    const supabaseClient = {
      request: async (endpoint: string, options: RequestInit = {}) => {
        const url = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/${endpoint}`;
        
        const response = await fetch(url, {
          ...options,
          headers: {
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY!,
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY!}`,
            'Content-Type': 'application/json',
            'Prefer': 'return=representation',
            ...options.headers,
          },
        });

        if (!response.ok) {
          throw new Error(`Supabase API error: ${response.status} ${response.statusText}`);
        }

        return await response.json();
      }
    };
    
    // Test direct database query to see what timestamps look like
    console.log('Testing direct database query for existing jobs...');
    
    const jobs = await supabaseClient.request('video_jobs?limit=5&select=*&order=created_at.desc');
    console.log('Raw jobs from database:', JSON.stringify(jobs, null, 2));

    if (jobs && jobs.length > 0) {
      const firstJob = jobs[0];
      console.log('First job timestamps:', {
        created_at: firstJob.created_at,
        created_at_type: typeof firstJob.created_at,
        updated_at: firstJob.updated_at,
        updated_at_type: typeof firstJob.updated_at
      });

      // Test date conversion with actual data
      const testDates = [firstJob.created_at, firstJob.updated_at];
      
      for (const dateValue of testDates) {
        console.log(`Testing date value: ${dateValue} (${typeof dateValue})`);
        
        try {
          const date = new Date(dateValue);
          console.log(`  Date object: ${date}`);
          console.log(`  getTime(): ${date.getTime()}`);
          console.log(`  isNaN(getTime()): ${isNaN(date.getTime())}`);
          console.log(`  toISOString(): ${date.toISOString()}`);
        } catch (error) {
          console.log(`  Error: ${error}`);
        }
      }
    }

    // Test the Jobs API transformation
    console.log('Testing Jobs API transformation...');
    
    if (jobs && jobs.length > 0) {
      const job = jobs[0];
      
      // Simulate the safeToISOString function from jobs API
      const safeToISOString = (dateValue: any) => {
        console.log(`safeToISOString input: ${dateValue} (${typeof dateValue})`);
        
        if (!dateValue) return new Date().toISOString();
        
        if (typeof dateValue === 'string') {
          try {
            const date = new Date(dateValue);
            if (isNaN(date.getTime())) {
              console.log('Invalid date string detected!');
              return new Date().toISOString();
            }
            return date.toISOString();
          } catch {
            return new Date().toISOString();
          }
        }
        
        if (dateValue instanceof Date) {
          if (isNaN(dateValue.getTime())) {
            return new Date().toISOString();
          }
          return dateValue.toISOString();
        }
        
        try {
          const date = new Date(dateValue);
          if (isNaN(date.getTime())) {
            return new Date().toISOString();
          }
          return date.toISOString();
        } catch {
          return new Date().toISOString();
        }
      };

      const transformedJob = {
        id: job.id,
        originalFileName: job.original_filename || 'Unknown',
        status: job.status || 'pending',
        platformsRequested: job.selected_platforms || [],
        createdAt: safeToISOString(job.created_at),
        updatedAt: safeToISOString(job.updated_at),
        progress: job.progress || 0,
        errorMessage: job.error_message || null,
      };

      console.log('Transformed job:', JSON.stringify(transformedJob, null, 2));
    }

    return NextResponse.json({
      success: true,
      debug: {
        rawJobs: jobs,
        jobCount: jobs?.length || 0,
        hasJobs: jobs && jobs.length > 0
      }
    });

  } catch (error: any) {
    console.error('Test error:', error);
    return NextResponse.json({
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
