

import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  const results: any = {
    timestamp: new Date().toISOString(),
    tests: {}
  };

  // Test Supabase REST API connection
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/users?select=count`,
      {
        method: 'HEAD',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY!,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'count=exact'
        }
      }
    );

    if (response.ok) {
      const count = response.headers.get('content-range')?.split('/')[1] || '0';
      results.tests.supabase_rest_api = {
        status: 'success',
        message: `REST API connection successful. User count: ${count}`,
        userCount: parseInt(count)
      };
    } else {
      results.tests.supabase_rest_api = {
        status: 'error',
        message: `REST API failed: ${response.status} ${response.statusText}`,
        status_code: response.status
      };
    }
  } catch (error: any) {
    results.tests.supabase_rest_api = {
      status: 'error',
      message: error.message
    };
  }

  // Test environment variables
  results.tests.environment = {
    status: 'success',
    supabase_url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing',
    service_key: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'configured' : 'missing',
    database_url: process.env.DATABASE_URL ? 'configured' : 'missing'
  };

  // Overall status
  const allTestsPassed = Object.values(results.tests).every((test: any) => test.status === 'success');
  results.overall = {
    status: allTestsPassed ? 'success' : 'error',
    message: allTestsPassed ? 'All Supabase tests passed' : 'Some Supabase tests failed'
  };

  return NextResponse.json(results);
}
