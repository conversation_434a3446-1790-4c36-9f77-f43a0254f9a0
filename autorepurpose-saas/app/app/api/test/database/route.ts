

import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/db-extensions';
import { supabaseClient } from '@/lib/supabase-client';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  const results: any = {
    timestamp: new Date().toISOString(),
    tests: {}
  };

  // Test 1: Supabase REST API Connection
  try {
    const connectionTest = await supabaseClient.testConnection();
    if (connectionTest.success) {
      results.tests.supabase_connection = {
        status: 'success',
        message: 'Supabase REST API connection successful'
      };
    } else {
      results.tests.supabase_connection = {
        status: 'error',
        message: connectionTest.error
      };
    }
  } catch (error: any) {
    results.tests.supabase_connection = {
      status: 'error',
      message: error.message,
      code: error.code
    };
  }

  // Test 2: Database Query Test (User Count)
  try {
    const userCount = await supabaseClient.getUserCount();
    results.tests.database_query = {
      status: 'success',
      message: `Database query successful via REST API. User count: ${userCount}`,
      userCount
    };
  } catch (error: any) {
    results.tests.database_query = {
      status: 'error',
      message: error.message,
      code: error.code
    };
  }

  // Test 3: List all users via REST API
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/users?select=id,clerk_user_id,email,created_at&limit=10`,
      {
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY!,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.ok) {
      const users = await response.json();
      results.tests.user_list = {
        status: 'success',
        message: `Found ${users.length} users via REST API`,
        users: users.slice(0, 5) // Show only first 5 for brevity
      };
    } else {
      throw new Error(`REST API failed: ${response.status} ${response.statusText}`);
    }
  } catch (error: any) {
    results.tests.user_list = {
      status: 'error',
      message: error.message,
      code: error.code
    };
  }

  // Test 4: Create a test user if requested
  if (req.nextUrl.searchParams.get('create') === 'test') {
    try {
      const testUser = await DatabaseService.upsertUserFromClerk({
        id: 'test_user_123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User'
      });
      
      results.tests.create_test_user = {
        status: 'success',
        message: 'Test user created successfully via REST API',
        user: testUser
      };
    } catch (error: any) {
      results.tests.create_test_user = {
        status: 'error',
        message: error.message,
        code: error.code
      };
    }
  }

  // Test 5: Environment Variables
  results.tests.environment = {
    status: 'success',
    supabase_url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing',
    service_key: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'configured' : 'missing',
    anon_key: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'configured' : 'missing'
  };

  // Overall status
  const allTestsPassed = Object.values(results.tests).every((test: any) => test.status === 'success');
  results.overall = {
    status: allTestsPassed ? 'success' : 'error',
    message: allTestsPassed ? 'All database tests passed using Supabase REST API' : 'Some database tests failed'
  };

  return NextResponse.json(results, { status: allTestsPassed ? 200 : 500 });
}
