
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const workerUrl = process.env.WORKER_SERVICE_URL;
    
    if (!workerUrl || workerUrl === 'REPLACE_WITH_YOUR_RAILWAY_URL') {
      return NextResponse.json({
        success: false,
        error: 'Worker URL not configured. Please update WORKER_SERVICE_URL in .env'
      }, { status: 500 });
    }

    console.log(`Testing worker connection to: ${workerUrl}`);

    // Test worker health endpoint
    const response = await fetch(`${workerUrl}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return NextResponse.json({
        success: false,
        error: `Worker health check failed: ${response.status} ${response.statusText}`,
        workerUrl,
      }, { status: 500 });
    }

    const healthData = await response.json();

    return NextResponse.json({
      success: true,
      message: 'Worker connection successful',
      workerUrl,
      workerHealth: healthData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Worker connection test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      workerUrl: process.env.WORKER_SERVICE_URL
    }, { status: 500 });
  }
}
