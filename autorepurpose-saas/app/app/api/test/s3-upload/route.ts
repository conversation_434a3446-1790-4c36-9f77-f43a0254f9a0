

import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const s3Client = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export async function POST(req: NextRequest) {
  try {
    const testContent = `S3 Upload Test - ${new Date().toISOString()}`;
    const testKey = `test-uploads/test-${Date.now()}.txt`;

    // Test direct upload to S3 (bypasses CORS)
    const command = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET_NAME!,
      Key: testKey,
      Body: testContent,
      ContentType: 'text/plain',
      Metadata: {
        'test-upload': 'true',
        'timestamp': Date.now().toString(),
      },
    });

    const result = await s3Client.send(command);

    return NextResponse.json({
      success: true,
      message: 'S3 upload test successful',
      details: {
        bucket: process.env.S3_BUCKET_NAME,
        region: process.env.AWS_REGION,
        key: testKey,
        etag: result.ETag,
        versionId: result.VersionId,
      }
    });

  } catch (error: any) {
    console.error('S3 upload test failed:', {
      error: error.message,
      code: error.code,
      region: process.env.AWS_REGION,
      bucket: process.env.S3_BUCKET_NAME,
    });

    return NextResponse.json({
      success: false,
      error: 'S3 upload test failed',
      details: {
        message: error.message,
        code: error.code,
        bucket: process.env.S3_BUCKET_NAME,
        region: process.env.AWS_REGION,
      }
    }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
  return NextResponse.json({
    message: 'S3 Upload Test Endpoint',
    instructions: 'Send a POST request to test S3 connectivity',
    config: {
      region: process.env.AWS_REGION,
      bucket: process.env.S3_BUCKET_NAME,
      hasAccessKey: !!process.env.AWS_ACCESS_KEY_ID,
      hasSecretKey: !!process.env.AWS_SECRET_ACCESS_KEY,
    }
  });
}

