
import { NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/db-extensions';

// Test endpoint to simulate webhook user creation
export async function POST(req: Request) {
  try {
    const { action = 'create-test-user' } = await req.json();
    
    if (action === 'create-test-user') {
      // Test creating a user like the webhook would
      const testUser = {
        id: `user_test_${Date.now()}`,
        email: `test-webhook-${Date.now()}@example.com`,
        firstName: 'Test',
        lastName: 'User'
      };

      const result = await DatabaseService.upsertUserFromClerk(testUser);
      
      return NextResponse.json({
        success: true,
        message: 'Test user created via webhook simulation',
        user: result
      });
    }
    
    if (action === 'test-user-lookup') {
      const { clerk_user_id } = await req.json();
      const user = await DatabaseService.getUserByClerkId(clerk_user_id);
      
      return NextResponse.json({
        success: true,
        user: user,
        found: !!user
      });
    }

    return NextResponse.json({ success: false, error: 'Invalid action' });
  } catch (error: any) {
    console.error('Webhook test error:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Clerk webhook test endpoint is working',
    status: 'active'
  });
}
