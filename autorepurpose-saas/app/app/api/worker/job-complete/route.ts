
import { NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase-client';

export async function POST(request: Request) {
  try {
    const { jobId, outputs, completed_at } = await request.json();

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Missing required field: jobId' },
        { status: 400 }
      );
    }

    console.log(`Completing job: ${jobId} with ${outputs?.length || 0} outputs`);

    // Update job status to completed
    const updateData = {
      status: 'completed',
      progress: 100,
      updated_at: completed_at || new Date().toISOString(),
    };

    await supabaseClient.updateJobStatus(jobId, 'completed', updateData);

    // If outputs are provided, create/update platform output records
    if (outputs && Array.isArray(outputs)) {
      for (const output of outputs) {
        try {
          await supabaseClient.createPlatformOutput({
            ...output,
            created_at: output.created_at || new Date().toISOString(),
          });
        } catch (outputError) {
          console.error(`Failed to create output for ${output.platform}:`, outputError);
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: `Job ${jobId} completed successfully`,
      outputs_created: outputs?.length || 0
    });

  } catch (error) {
    console.error('Worker job completion error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
