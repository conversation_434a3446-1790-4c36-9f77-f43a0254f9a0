
import { NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase-client';

export async function POST(request: Request) {
  try {
    const { jobId, status, progress, error_message, updated_at } = await request.json();

    if (!jobId || !status) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: jobId, status' },
        { status: 400 }
      );
    }

    console.log(`Worker update: Job ${jobId} -> ${status} (${progress}%)`);

    // Prepare update data
    const updateData: any = {
      status,
      updated_at: updated_at || new Date().toISOString(),
    };

    if (progress !== undefined) {
      updateData.progress = progress;
    }

    if (error_message) {
      updateData.error_message = error_message;
    }

    // Update job in database
    const result = await supabaseClient.updateJobStatus(jobId, status, updateData);

    return NextResponse.json({
      success: true,
      message: `Job ${jobId} status updated to ${status}`,
      data: result
    });

  } catch (error) {
    console.error('Worker job update error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
