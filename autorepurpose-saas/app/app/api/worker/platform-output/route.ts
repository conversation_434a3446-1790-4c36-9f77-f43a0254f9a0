
import { NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase-client';

export async function POST(request: Request) {
  try {
    const outputData = await request.json();

    const {
      id,
      job_id,
      platform,
      s3_output_key,
      file_size_bytes,
      duration_seconds,
      resolution,
      format,
      status,
      created_at
    } = outputData;

    if (!id || !job_id || !platform) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: id, job_id, platform' },
        { status: 400 }
      );
    }

    console.log(`Creating platform output: Job ${job_id} -> ${platform} (${status})`);

    // Create platform output record
    const output = {
      id,
      job_id,
      platform,
      s3_output_key: s3_output_key || '',
      file_size_bytes: file_size_bytes || 0,
      duration_seconds: duration_seconds || 0,
      resolution: resolution || '0x0',
      format: format || 'mp4',
      status: status || 'pending',
      created_at: created_at || new Date().toISOString(),
    };

    const result = await supabaseClient.createPlatformOutput(output);

    return NextResponse.json({
      success: true,
      message: `Platform output created for ${platform}`,
      data: result
    });

  } catch (error) {
    console.error('Worker platform output creation error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
