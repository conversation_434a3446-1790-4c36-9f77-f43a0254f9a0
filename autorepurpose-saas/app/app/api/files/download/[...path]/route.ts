
import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { supabaseClient } from '@/lib/supabase-client';

const s3Client = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Get authenticated user
    const user = await currentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const filePath = params.path.join('/');
    console.log(`📥 Download request for: ${filePath} by user: ${user.id}`);

    // Verify file ownership using direct query
    const outputs = await supabaseClient.request(
      `video_outputs?s3_output_key=eq.${encodeURIComponent(filePath)}&status=eq.completed&select=*,video_jobs!inner(user_id,id,original_filename)`
    );

    const output = outputs?.find((o: any) => o.video_jobs?.user_id === user.id);

    if (!output) {
      console.log(`❌ File not found or access denied: ${filePath}`);
      return NextResponse.json({ error: 'File not found or access denied' }, { status: 404 });
    }

    // Check if file hasn't expired (3-day retention)
    const fileCreatedAt = new Date(output.created_at);
    const expirationDate = new Date(fileCreatedAt.getTime() + 3 * 24 * 60 * 60 * 1000); // 3 days
    const now = new Date();

    if (now > expirationDate) {
      console.log(`⏰ File expired: ${filePath}`);
      return NextResponse.json({ error: 'File has expired' }, { status: 410 });
    }

    // Generate signed URL for S3 download
    const command = new GetObjectCommand({
      Bucket: process.env.S3_BUCKET_NAME!,
      Key: filePath,
      ResponseContentDisposition: `attachment; filename="${output.filename}"`,
    });

    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 300 }); // 5 minutes

    // Track download - create simple log entry
    try {
      await supabaseClient.request('download_logs', {
        method: 'POST',
        body: JSON.stringify({
          user_id: user.id,
          video_output_id: output.id,
          job_id: output.job_id,
          downloaded_at: new Date().toISOString(),
          ip_address: request.headers.get('x-forwarded-for') || 'unknown',
          user_agent: request.headers.get('user-agent') || 'unknown'
        }),
      });
    } catch (trackingError) {
      console.warn('⚠️ Failed to track download:', trackingError);
      // Don't fail the download for tracking errors
    }

    // Update download count (simplified)
    try {
      await supabaseClient.request(`video_outputs?id=eq.${output.id}`, {
        method: 'PATCH',
        body: JSON.stringify({
          download_count: (output.download_count || 0) + 1,
          last_downloaded_at: new Date().toISOString()
        }),
      });
    } catch (updateError) {
      console.warn('⚠️ Failed to update download count:', updateError);
    }

    console.log(`✅ Download authorized for: ${output.filename}`);

    // Redirect to signed URL for download
    return NextResponse.redirect(signedUrl);

  } catch (error) {
    console.error('❌ Download error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Download failed' 
    }, { status: 500 });
  }
}

export async function HEAD(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  // Handle HEAD requests for file info without downloading
  try {
    const user = await currentUser();
    if (!user) {
      return new NextResponse(null, { status: 401 });
    }

    const filePath = params.path.join('/');

    const outputs = await supabaseClient.request(
      `video_outputs?s3_output_key=eq.${encodeURIComponent(filePath)}&status=eq.completed&select=filename,file_size_bytes,created_at,video_jobs!inner(user_id)`
    );

    const output = outputs?.find((o: any) => o.video_jobs?.user_id === user.id);

    if (!output) {
      return new NextResponse(null, { status: 404 });
    }

    return new NextResponse(null, {
      status: 200,
      headers: {
        'Content-Length': output.file_size_bytes?.toString() || '0',
        'Content-Type': 'video/mp4',
        'Last-Modified': new Date(output.created_at).toUTCString(),
      },
    });

  } catch (error) {
    return new NextResponse(null, { status: 500 });
  }
}
