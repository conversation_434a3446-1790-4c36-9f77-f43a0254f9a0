
import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';
import { supabaseClient } from '@/lib/supabase-client';

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await currentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50); // Max 50 items
    const status = searchParams.get('status') || 'all'; // all, completed, failed, processing
    const jobId = searchParams.get('jobId');

    console.log(`📂 Fetching files for user: ${user.id}, page: ${page}, status: ${status}`);

    // First get the user's database ID
    const userRecord = await supabaseClient.findUserByClerkId(user.id);
    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Build query for video outputs with joins
    let queryUrl = `video_outputs?video_jobs.user_id=eq.${userRecord.id}&select=*,video_jobs!inner(id,user_id,original_filename,status,created_at,processing_started_at,processing_completed_at)&order=created_at.desc`;

    // Filter by status if specified
    if (status !== 'all') {
      queryUrl += `&status=eq.${status}`;
    }

    // Filter by job ID if specified  
    if (jobId) {
      queryUrl += `&job_id=eq.${jobId}`;
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    queryUrl += `&limit=${limit}&offset=${offset}`;

    const outputs = await supabaseClient.request(queryUrl);

    if (!outputs) {
      return NextResponse.json({ error: 'Failed to fetch files' }, { status: 500 });
    }

    // Calculate file expiry and add download URLs
    const filesWithUrls = outputs.map((output: any) => {
      const fileCreatedAt = new Date(output.created_at);
      const expirationDate = new Date(fileCreatedAt.getTime() + 3 * 24 * 60 * 60 * 1000); // 3 days
      const isExpired = new Date() > expirationDate;
      const hoursUntilExpiry = Math.max(0, Math.floor((expirationDate.getTime() - Date.now()) / (1000 * 60 * 60)));

      return {
        ...output,
        downloadUrl: output.status === 'completed' && !isExpired && output.s3_output_key 
          ? `/api/files/download/${output.s3_output_key}` 
          : null,
        isExpired,
        hoursUntilExpiry,
        expirationDate: expirationDate.toISOString(),
      };
    });

    // Group by job for better organization
    const filesByJob = filesWithUrls.reduce((acc: any, file: any) => {
      const jobId = file.job_id;
      if (!acc[jobId]) {
        acc[jobId] = {
          job: file.video_jobs,
          outputs: []
        };
      }
      acc[jobId].outputs.push(file);
      return acc;
    }, {});

    // Get total count (simplified)
    let countQueryUrl = `video_jobs?user_id=eq.${userRecord.id}&select=id`;
    const totalJobs = await supabaseClient.request(countQueryUrl);
    const count = totalJobs?.length || 0;

    const totalPages = count > 0 ? Math.ceil(count / limit) : 1;

    console.log(`✅ Found ${outputs.length} files for user ${user.id}`);

    return NextResponse.json({
      success: true,
      data: {
        files: filesWithUrls,
        filesByJob,
        pagination: {
          page,
          limit,
          total: count,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        },
        filters: {
          status,
          jobId
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ User files API error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to fetch user files'
    }, { status: 500 });
  }
}
