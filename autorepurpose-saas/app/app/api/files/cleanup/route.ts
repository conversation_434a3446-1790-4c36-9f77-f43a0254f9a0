
import { NextRequest, NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase-client';
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';

const s3Client = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export async function POST(request: NextRequest) {
  try {
    // Simple authentication - you might want to add a secret token
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CLEANUP_SECRET_TOKEN || 'cleanup-secret-123';
    
    if (authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🧹 Starting file cleanup job...');

    // Find files older than 3 days
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

    const expiredOutputs = await supabaseClient.request(
      `video_outputs?created_at=lt.${threeDaysAgo.toISOString()}&status=eq.completed&select=id,s3_output_key,filename,created_at,status`
    );

    if (!expiredOutputs || expiredOutputs.length === 0) {
      console.log('✅ No expired files to clean up');
      return NextResponse.json({
        success: true,
        message: 'No expired files to clean up',
        deletedCount: 0,
        timestamp: new Date().toISOString()
      });
    }

    console.log(`🗑️ Found ${expiredOutputs.length} expired files to delete`);

    let deletedCount = 0;
    let failedCount = 0;
    const deletedFiles = [];
    const failedFiles = [];

    // Delete files from S3 and update database
    for (const output of expiredOutputs) {
      try {
        if (output.s3_output_key) {
          // Delete from S3
          const deleteCommand = new DeleteObjectCommand({
            Bucket: process.env.S3_BUCKET_NAME!,
            Key: output.s3_output_key,
          });

          await s3Client.send(deleteCommand);
          console.log(`🗑️ Deleted from S3: ${output.s3_output_key}`);
        }

        // Update database record
        await supabaseClient.request(`video_outputs?id=eq.${output.id}`, {
          method: 'PATCH',
          body: JSON.stringify({
            status: 'expired_deleted',
            s3_output_key: null,
            deleted_at: new Date().toISOString()
          }),
        });

        deletedFiles.push({
          id: output.id,
          filename: output.filename,
          s3Key: output.s3_output_key,
          createdAt: output.created_at
        });
        deletedCount++;

      } catch (error) {
        console.error(`❌ Failed to delete ${output.filename}:`, error);
        failedFiles.push({
          id: output.id,
          filename: output.filename,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        failedCount++;
      }
    }

    console.log(`✅ Cleanup completed: ${deletedCount} deleted, ${failedCount} failed`);

    // Log cleanup activity (simplified)
    try {
      await supabaseClient.request('cleanup_logs', {
        method: 'POST',
        body: JSON.stringify({
          cleanup_type: 'expired_files',
          files_found: expiredOutputs.length,
          files_deleted: deletedCount,
          files_failed: failedCount,
          cleanup_date: new Date().toISOString(),
          details: JSON.stringify({
            deletedFiles,
            failedFiles
          })
        }),
      });
    } catch (logError) {
      console.warn('⚠️ Failed to log cleanup activity:', logError);
    }

    return NextResponse.json({
      success: true,
      message: `Cleanup completed: ${deletedCount} files deleted`,
      summary: {
        filesFound: expiredOutputs.length,
        filesDeleted: deletedCount,
        filesFailed: failedCount,
        deletedFiles,
        failedFiles
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Cleanup job error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Cleanup job failed'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Simple status endpoint to check cleanup service
    const { searchParams } = new URL(request.url);
    const showLogs = searchParams.get('logs') === 'true';

    let response: any = {
      service: 'file-cleanup',
      status: 'running',
      retention: '3 days',
      timestamp: new Date().toISOString()
    };

    if (showLogs) {
      // Get recent cleanup logs
      const logs = await supabaseClient.request(
        'cleanup_logs?select=*&order=cleanup_date.desc&limit=10'
      );

      if (logs) {
        response.recentCleanups = logs;
      }
    }

    return NextResponse.json(response);

  } catch (error) {
    return NextResponse.json({
      service: 'file-cleanup',
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
