
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// Force dynamic rendering
export const dynamic = 'force-dynamic';
import { randomUUID } from 'crypto';

const s3Client = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { fileName, fileType, fileSize } = await req.json();

    // Validate inputs
    if (!fileName || !fileType || !fileSize) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate file size (5GB limit)
    const maxSize = 5 * 1024 * 1024 * 1024; // 5GB
    if (fileSize > maxSize) {
      return NextResponse.json({ error: 'File size exceeds 5GB limit' }, { status: 400 });
    }

    // Validate file type
    const allowedTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/mkv', 'video/webm'];
    if (!allowedTypes.includes(fileType)) {
      return NextResponse.json({ error: 'Unsupported file type' }, { status: 400 });
    }

    // Generate unique file key
    const fileExtension = fileName.split('.').pop();
    const jobId = randomUUID();
    const key = `uploads/${userId}/${jobId}/${fileName}`;

    // Create presigned URL for upload
    const command = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET_NAME!,
      Key: key,
      ContentType: fileType,
      ContentLength: fileSize,
      Metadata: {
        'original-name': fileName,
        'user-id': userId,
        'job-id': jobId,
      },
    });

    const uploadUrl = await getSignedUrl(s3Client, command, {
      expiresIn: parseInt(process.env.S3_UPLOAD_EXPIRES_IN || '3600'), // 1 hour default
    });

    return NextResponse.json({
      uploadUrl,
      key,
      jobId,
      expiresIn: parseInt(process.env.S3_UPLOAD_EXPIRES_IN || '3600'),
    });

  } catch (error: any) {
    console.error('Error generating presigned URL:', {
      error: error.message,
      code: error.code,
      region: process.env.AWS_REGION,
      bucket: process.env.S3_BUCKET_NAME,
      stack: error.stack
    });
    return NextResponse.json(
      { 
        error: 'Failed to generate upload URL',
        details: error.message,
        code: error.code 
      },
      { status: 500 }
    );
  }
}
