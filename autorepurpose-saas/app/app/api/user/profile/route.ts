
import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService, db } from '@/lib/db';

export const dynamic = "force-dynamic";

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, error: { code: 'AUTH_REQUIRED', message: 'Authentication required' } },
        { status: 401 }
      );
    }

    const userWithUsage = await DatabaseService.getUserWithUsage(userId);

    if (!userWithUsage) {
      return NextResponse.json(
        { success: false, error: { code: 'USER_NOT_FOUND', message: 'User not found' } },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: userWithUsage.id,
          email: userWithUsage.email,
          fullName: userWithUsage.fullName,
          subscriptionStatus: userWithUsage.subscriptionStatus,
          subscriptionId: userWithUsage.subscriptionId,
          currentPeriodEnd: userWithUsage.currentPeriodEnd,
          freeRepurposingsUsed: userWithUsage.freeRepurposingsUsed,
          createdAt: userWithUsage.createdAt,
          updatedAt: userWithUsage.updatedAt,
        },
        usage: userWithUsage.usage
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json(
      { success: false, error: { code: 'SERVER_ERROR', message: 'Internal server error' } },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, error: { code: 'AUTH_REQUIRED', message: 'Authentication required' } },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { fullName } = body;

    // Validate input
    if (fullName && typeof fullName !== 'string') {
      return NextResponse.json(
        { success: false, error: { code: 'INVALID_INPUT', message: 'Full name must be a string' } },
        { status: 400 }
      );
    }

    const user = await DatabaseService.getUserWithUsage(userId);
    if (!user) {
      return NextResponse.json(
        { success: false, error: { code: 'USER_NOT_FOUND', message: 'User not found' } },
        { status: 404 }
      );
    }

    // Update user profile
    const updatedUser = await db.user.update({
      where: { clerkUserId: userId },
      data: {
        ...(fullName !== undefined && { fullName }),
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          fullName: updatedUser.fullName,
          subscriptionStatus: updatedUser.subscriptionStatus,
          subscriptionId: updatedUser.subscriptionId,
          currentPeriodEnd: updatedUser.currentPeriodEnd,
          freeRepurposingsUsed: updatedUser.freeRepurposingsUsed,
          createdAt: updatedUser.createdAt,
          updatedAt: updatedUser.updatedAt,
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    return NextResponse.json(
      { success: false, error: { code: 'SERVER_ERROR', message: 'Internal server error' } },
      { status: 500 }
    );
  }
}
