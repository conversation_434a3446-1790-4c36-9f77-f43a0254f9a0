
import { NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/db-extensions';

export async function POST() {
  try {
    console.log('Checking for stale jobs...');
    
    // Mark jobs that have been queued for more than 10 minutes as failed
    const result = await DatabaseService.markStaleJobsAsFailed();
    
    console.log('Marked stale jobs as failed:', result);
    
    return NextResponse.json({
      success: true,
      message: 'Stale jobs marked as failed',
      result
    });

  } catch (error: any) {
    console.error('Error marking stale jobs as failed:', error);
    return NextResponse.json(
      { error: 'Failed to mark stale jobs as failed', details: error.message },
      { status: 500 }
    );
  }
}
