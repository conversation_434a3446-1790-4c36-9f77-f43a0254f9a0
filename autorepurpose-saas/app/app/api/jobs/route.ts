
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { DatabaseService } from '@/lib/db-extensions';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');

    try {
      // Fetch user's jobs from database
      const { jobs } = await DatabaseService.getUserJobs(
        userId,
        Math.floor(offset / limit) + 1,
        limit,
        status || undefined
      );

      // Transform jobs to include output information
      const transformedJobs = await Promise.all(
        jobs.map(async (job: any) => {
          // Fetch outputs for this job
          const outputs = await DatabaseService.getVideoOutputs(job.id);
          
          // Safe date handling - ensure we get valid ISO strings
          const safeToISOString = (dateValue: any) => {
            if (!dateValue) return new Date().toISOString();
            
            // If it's already a string, validate it's a proper ISO string
            if (typeof dateValue === 'string') {
              try {
                const date = new Date(dateValue);
                if (isNaN(date.getTime())) {
                  console.warn('Invalid date string:', dateValue);
                  return new Date().toISOString();
                }
                return date.toISOString();
              } catch {
                return new Date().toISOString();
              }
            }
            
            if (dateValue instanceof Date) {
              if (isNaN(dateValue.getTime())) {
                return new Date().toISOString();
              }
              return dateValue.toISOString();
            }
            
            try {
              const date = new Date(dateValue);
              if (isNaN(date.getTime())) {
                return new Date().toISOString();
              }
              return date.toISOString();
            } catch {
              return new Date().toISOString();
            }
          };
          
          return {
            id: job.id,
            originalFileName: job.original_filename || job.originalFileName || 'Unknown',
            status: job.status || 'pending',
            platformsRequested: job.selected_platforms || job.target_platforms || job.platformsRequested || [],
            createdAt: safeToISOString(job.created_at || job.createdAt),
            updatedAt: safeToISOString(job.updated_at || job.updatedAt),
            progress: job.progress || 0,
            errorMessage: job.error_message || job.errorMessage || null,
            outputs: outputs.map((output: any) => ({
              id: output.id,
              platform: output.platform,
              status: output.status,
              downloadUrl: output.downloadUrl,
              fileSize: output.fileSize,
              duration: output.duration,
            })),
          };
        })
      );

      return NextResponse.json({
        success: true,
        jobs: transformedJobs,
        pagination: {
          limit,
          offset,
          hasMore: transformedJobs.length === limit,
        },
      });

    } catch (dbError) {
      console.error('Database error:', dbError);
      return NextResponse.json(
        { error: 'Failed to fetch jobs' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error fetching jobs:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
