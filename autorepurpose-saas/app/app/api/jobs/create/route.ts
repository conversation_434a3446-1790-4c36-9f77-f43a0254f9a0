
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { DatabaseService } from '@/lib/db-extensions';
import { JobQueueService } from '@/lib/queue';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { jobId, fileName, s3Key, platforms } = await req.json();

    // Validate inputs
    if (!jobId || !fileName || !s3Key || !platforms || platforms.length === 0) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    try {
      console.log('Creating job for user:', userId, 'with platforms:', platforms);
      
      // Create job record in database using the REST API method
      const job = await DatabaseService.createVideoJob({
        clerkUserId: userId,
        originalFilename: fileName,
        s3InputKey: s3Key,
        fileSizeBytes: 1024000, // Default 1MB, will be updated by worker
        durationSeconds: 0, // Will be updated by worker
        targetPlatforms: platforms,
        jobId: jobId,
      });

      console.log('Job created successfully in database:', job.id);

      // Add job to processing queue
      console.log('Adding job to processing queue...');
      await JobQueueService.addVideoProcessingJob({
        jobId: job.id,
        userId,
        s3Key,
        platforms,
        originalFileName: fileName,
      });

      console.log('Job added to processing queue successfully');

      // Safe date handling with validation
      const safeToISOString = (dateValue: any) => {
        if (!dateValue) return new Date().toISOString();
        
        if (typeof dateValue === 'string') {
          try {
            const date = new Date(dateValue);
            if (isNaN(date.getTime())) {
              console.warn('Invalid date string in job creation:', dateValue);
              return new Date().toISOString();
            }
            return date.toISOString();
          } catch {
            return new Date().toISOString();
          }
        }
        
        if (dateValue instanceof Date) {
          if (isNaN(dateValue.getTime())) {
            return new Date().toISOString();
          }
          return dateValue.toISOString();
        }
        
        try {
          const date = new Date(dateValue);
          if (isNaN(date.getTime())) {
            return new Date().toISOString();
          }
          return date.toISOString();
        } catch {
          return new Date().toISOString();
        }
      };

      const currentTime = new Date().toISOString();

      return NextResponse.json({
        success: true,
        job: {
          id: job.id || jobId,
          originalFileName: job.original_filename || fileName,
          status: job.status || 'queued',
          platformsRequested: job.selected_platforms || platforms,
          createdAt: job.created_at ? safeToISOString(job.created_at) : currentTime,
          updatedAt: job.updated_at ? safeToISOString(job.updated_at) : currentTime,
          progress: job.progress || 0,
        },
      });

    } catch (dbError: any) {
      console.error('Database/Queue error:', {
        error: dbError.message,
        stack: dbError.stack,
        userId,
        fileName,
        s3Key,
        platforms
      });
      
      // Provide more specific error messages
      if (dbError.message?.includes('User not found')) {
        return NextResponse.json(
          { 
            error: 'User account not found in database',
            details: 'Please try refreshing the page or signing out and back in'
          },
          { status: 400 }
        );
      } else if (dbError.message?.includes('video_jobs')) {
        return NextResponse.json(
          { 
            error: 'Database error when creating job',
            details: dbError.message 
          },
          { status: 500 }
        );
      } else if (dbError.message?.includes('queue')) {
        return NextResponse.json(
          { 
            error: 'Failed to start processing',
            details: 'Job was created but could not be queued for processing'
          },
          { status: 500 }
        );
      } else {
        return NextResponse.json(
          { 
            error: 'Failed to create job record',
            details: dbError.message 
          },
          { status: 500 }
        );
      }
    }

  } catch (error) {
    console.error('Error creating job:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
