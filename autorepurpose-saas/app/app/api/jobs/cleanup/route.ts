
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { DatabaseService } from '@/lib/db-extensions';
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';

const s3Client = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { jobId } = await req.json();

    if (!jobId) {
      return NextResponse.json({ error: 'Job ID is required' }, { status: 400 });
    }

    // Get job details
    const { jobs } = await DatabaseService.getUserJobs(userId, 1, 1000);
    const job = jobs.find((j: any) => j.id === jobId);

    if (!job) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    // Delete S3 input file if it exists
    if (job.s3_input_key || job.s3InputKey) {
      try {
        await s3Client.send(new DeleteObjectCommand({
          Bucket: process.env.S3_BUCKET_NAME!,
          Key: job.s3_input_key || job.s3InputKey,
        }));
        console.log('Deleted S3 input file:', job.s3_input_key || job.s3InputKey);
      } catch (error) {
        console.warn('Failed to delete S3 input file:', error);
      }
    }

    // Delete job from database
    await DatabaseService.deleteVideoJob(jobId);

    return NextResponse.json({
      success: true,
      message: 'Job and associated files deleted successfully',
    });

  } catch (error: any) {
    console.error('Error cleaning up job:', error);
    return NextResponse.json(
      { error: 'Failed to cleanup job', details: error.message },
      { status: 500 }
    );
  }
}
