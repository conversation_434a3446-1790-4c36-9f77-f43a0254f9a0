
import { NextRequest, NextResponse } from 'next/server';

// Simple cron endpoint that can be called by external schedulers
// In production, use services like Vercel Cron, GitHub Actions, or external cron services

export async function POST(request: NextRequest) {
  try {
    // Verify this is a legitimate cron call
    const cronSecret = request.headers.get('x-cron-secret');
    const expectedSecret = process.env.CRON_SECRET || 'default-cron-secret-change-in-production';

    if (cronSecret !== expectedSecret) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🕒 Cron job triggered: Starting cleanup process...');

    // Call the cleanup endpoint
    const cleanupUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/files/cleanup`;
    const cleanupToken = process.env.CLEANUP_SECRET_TOKEN || 'cleanup-secret-123';

    const cleanupResponse = await fetch(cleanupUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${cleanupToken}`,
        'Content-Type': 'application/json',
      },
    });

    const cleanupResult = await cleanupResponse.json();

    if (!cleanupResponse.ok) {
      console.error('❌ Cleanup failed:', cleanupResult);
      return NextResponse.json({
        success: false,
        error: 'Cleanup process failed',
        details: cleanupResult
      }, { status: 500 });
    }

    console.log('✅ Cleanup completed successfully:', cleanupResult.summary);

    return NextResponse.json({
      success: true,
      message: 'Cleanup cron job completed successfully',
      cleanupResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Cron job error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Cron job failed'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  // Status endpoint for cron job
  return NextResponse.json({
    service: 'cleanup-cron',
    status: 'active',
    description: 'Automated file cleanup scheduler',
    schedule: 'Daily at 2:00 AM UTC',
    lastRun: 'Use POST method to trigger cleanup',
    setup: {
      cronSecret: 'Set X-Cron-Secret header',
      schedule: 'Call POST /api/cleanup/cron daily'
    },
    timestamp: new Date().toISOString()
  });
}
