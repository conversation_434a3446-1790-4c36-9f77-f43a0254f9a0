
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const supabase = createClient();
    
    // Get user subscription details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        subscription_status,
        subscription_id,
        current_period_end,
        preferred_payment_provider,
        stripe_customer_id,
        razorpay_customer_id
      `)
      .eq('clerk_user_id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get recent payment transactions
    const { data: transactions, error: transactionsError } = await supabase
      .from('payment_transactions')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(5);

    if (transactionsError) {
      console.error('Error fetching payment transactions:', transactionsError);
    }

    const subscriptionStatus = {
      isActive: user.subscription_status === 'active',
      status: user.subscription_status,
      provider: user.preferred_payment_provider,
      subscriptionId: user.subscription_id,
      expiresAt: user.current_period_end,
      customerIds: {
        stripe: user.stripe_customer_id,
        razorpay: user.razorpay_customer_id
      },
      recentTransactions: transactions || []
    };

    return NextResponse.json(subscriptionStatus);

  } catch (error) {
    console.error('Subscription status error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription status' },
      { status: 500 }
    );
  }
}
