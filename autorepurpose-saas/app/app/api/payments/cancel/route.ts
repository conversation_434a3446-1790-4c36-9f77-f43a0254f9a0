
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createClient } from '@/lib/supabase/server';
import Stripe from 'stripe';
import razorpay from '@/lib/razorpay';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { reason } = body; // Optional cancellation reason

    const supabase = createClient();
    
    // Get user subscription details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('clerk_user_id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (user.subscription_status !== 'active') {
      return NextResponse.json(
        { error: 'No active subscription to cancel' },
        { status: 400 }
      );
    }

    const provider = user.preferred_payment_provider;
    const subscriptionId = user.subscription_id;

    if (!provider || !subscriptionId) {
      return NextResponse.json(
        { error: 'Invalid subscription data' },
        { status: 400 }
      );
    }

    try {
      // Cancel subscription based on provider
      if (provider === 'stripe') {
        if (user.stripe_customer_id && subscriptionId.startsWith('sub_')) {
          // Cancel Stripe subscription
          await stripe.subscriptions.update(subscriptionId, {
            cancel_at_period_end: true,
            metadata: {
              cancellation_reason: reason || 'user_request',
              cancelled_by: 'user'
            }
          });
        }
      } else if (provider === 'razorpay') {
        if (user.razorpay_customer_id && subscriptionId.startsWith('sub_')) {
          // Cancel Razorpay subscription
          await razorpay.subscriptions.cancel(subscriptionId, true);
        }
      }

      // Update user subscription status in database
      const { error: updateError } = await supabase
        .from('users')
        .update({
          subscription_status: 'cancelled'
        })
        .eq('id', user.id);

      if (updateError) {
        console.error('Error updating user subscription status:', updateError);
        return NextResponse.json(
          { error: 'Failed to update subscription status' },
          { status: 500 }
        );
      }

      // Log the cancellation
      await supabase.from('payment_transactions').insert({
        user_id: user.id,
        provider: provider,
        amount_cents: 0,
        currency: provider === 'stripe' ? 'usd' : 'inr',
        status: 'cancelled',
        transaction_type: 'subscription_cancellation',
        metadata: {
          reason: reason || 'user_request',
          cancelled_at: new Date().toISOString(),
          subscription_id: subscriptionId
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Subscription cancelled successfully',
        provider,
        willCancelAt: user.current_period_end
      });

    } catch (providerError) {
      console.error(`${provider} cancellation error:`, providerError);
      
      // Still update the database even if provider API fails
      await supabase
        .from('users')
        .update({
          subscription_status: 'cancelled'
        })
        .eq('id', user.id);

      return NextResponse.json({
        success: true,
        message: 'Subscription marked as cancelled',
        warning: 'Provider API call failed, please contact support if charges continue'
      });
    }

  } catch (error) {
    console.error('Subscription cancellation error:', error);
    return NextResponse.json(
      { error: 'Failed to cancel subscription' },
      { status: 500 }
    );
  }
}
