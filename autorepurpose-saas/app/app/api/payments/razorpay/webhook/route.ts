
import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('x-razorpay-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing signature' },
        { status: 400 }
      );
    }

    // Verify webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET!)
      .update(body)
      .digest('hex');

    if (signature !== expectedSignature) {
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    const event = JSON.parse(body);
    const supabase = createClient();

    console.log('Razorpay webhook event:', event.event);

    switch (event.event) {
      case 'payment.captured':
        {
          const payment = event.payload.payment.entity;
          
          // Update payment transaction
          await supabase
            .from('payment_transactions')
            .update({
              status: 'completed',
              razorpay_payment_id: payment.id,
              provider_transaction_id: payment.id
            })
            .eq('razorpay_order_id', payment.order_id);
        }
        break;

      case 'payment.failed':
        {
          const payment = event.payload.payment.entity;
          
          // Update payment transaction
          await supabase
            .from('payment_transactions')
            .update({
              status: 'failed',
              razorpay_payment_id: payment.id,
              provider_transaction_id: payment.id
            })
            .eq('razorpay_order_id', payment.order_id);
        }
        break;

      case 'subscription.charged':
        {
          const subscription = event.payload.subscription.entity;
          const payment = event.payload.payment.entity;

          // Find user by razorpay customer ID or subscription ID
          const { data: user } = await supabase
            .from('users')
            .select('*')
            .eq('subscription_id', subscription.id)
            .single();

          if (user) {
            // Create payment transaction record
            await supabase.from('payment_transactions').insert({
              user_id: user.id,
              provider: 'razorpay',
              razorpay_payment_id: payment.id,
              razorpay_subscription_id: subscription.id,
              amount_cents: payment.amount,
              currency: payment.currency.toLowerCase(),
              status: 'completed',
              transaction_type: 'subscription_renewal',
              provider_transaction_id: payment.id
            });

            // Update subscription period
            const nextPeriodEnd = new Date(subscription.current_end * 1000);
            await supabase
              .from('users')
              .update({
                subscription_status: 'active',
                current_period_end: nextPeriodEnd.toISOString()
              })
              .eq('id', user.id);
          }
        }
        break;

      case 'subscription.cancelled':
        {
          const subscription = event.payload.subscription.entity;

          // Update user subscription status
          await supabase
            .from('users')
            .update({
              subscription_status: 'cancelled'
            })
            .eq('subscription_id', subscription.id);
        }
        break;

      default:
        console.log(`Unhandled Razorpay event: ${event.event}`);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Razorpay webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}
