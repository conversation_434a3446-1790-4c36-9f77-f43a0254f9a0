
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import Ra<PERSON>pay from 'razorpay';
import { createClient } from '@/lib/supabase/server';

const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID!,
  key_secret: process.env.RAZORPAY_KEY_SECRET!,
});

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const supabase = createClient();
    
    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('clerk_user_id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Create Razorpay order
    const options = {
      amount: 250000, // ₹2,500 in paise
      currency: 'INR',
      receipt: `order_${user.id}_${Date.now()}`,
      notes: {
        user_id: user.id,
        subscription_type: 'monthly',
        plan: 'autorepurpose_monthly'
      }
    };

    const order = await razorpay.orders.create(options);

    // Store order in database
    await supabase.from('payment_transactions').insert({
      user_id: user.id,
      provider: 'razorpay',
      razorpay_order_id: order.id,
      amount_cents: options.amount,
      currency: options.currency.toLowerCase(),
      status: 'created',
      transaction_type: 'subscription',
      provider_transaction_id: order.id,
      metadata: {
        receipt: order.receipt,
        notes: order.notes
      }
    });

    return NextResponse.json({
      orderId: order.id,
      amount: order.amount,
      currency: order.currency,
      key: process.env.RAZORPAY_KEY_ID,
      user: {
        name: user.full_name || user.email,
        email: user.email
      }
    });

  } catch (error) {
    console.error('Razorpay order creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create payment order' },
      { status: 500 }
    );
  }
}
