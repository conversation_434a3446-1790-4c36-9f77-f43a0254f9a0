
"use client";

import { useUser, SignOutButton } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Upload, Video, Clock, CheckCircle, XCircle, Download, Settings, User, LogOut, RefreshCw } from "lucide-react";
import Link from "next/link";
import { VideoUploadComponent } from "@/components/video-upload";
import { JobStatusList } from "@/components/job-status-list";
import { UserProfile } from "@/components/user-profile";
import { useRealtimeJobs } from "@/hooks/use-realtime-jobs";

interface VideoJob {
  id: string;
  originalFileName: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'queued';
  platformsRequested: string[];
  createdAt: string;
  progress?: number;
  outputs?: Array<{
    id: string;
    platform: string;
    downloadUrl?: string;
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'queued';
    fileSize?: number;
    duration?: number;
  }>;
}

export default function DashboardPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("upload");
  
  // Use enhanced realtime jobs hook
  const { jobs, loading, error, refreshJobs, addJob } = useRealtimeJobs();

  // Redirect to home if not authenticated
  useEffect(() => {
    if (isLoaded && !user) {
      router.push("/");
    }
  }, [isLoaded, user, router]);

  if (!isLoaded || !user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  const getJobStats = () => {
    const completed = jobs.filter(job => job.status === 'completed').length;
    const processing = jobs.filter(job => job.status === 'processing' || job.status === 'pending' || job.status === 'queued').length;
    const failed = jobs.filter(job => job.status === 'failed').length;
    
    return { completed, processing, failed, total: jobs.length };
  };

  const stats = getJobStats();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 w-full border-b bg-background/80 backdrop-blur-sm">
        <div className="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-2">
              <Link href="/dashboard" className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded bg-primary">
                  <Settings className="h-5 w-5 text-primary-foreground" />
                </div>
                <span className="text-xl font-bold">AutoRepurpose</span>
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <Badge variant="secondary">
                {stats.total} jobs processed
              </Badge>
              {error && (
                <Badge variant="destructive">
                  Connection issue
                </Badge>
              )}
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={refreshJobs}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button size="sm" variant="ghost" onClick={() => setActiveTab("profile")}>
                <User className="h-4 w-4 mr-2" />
                Profile
              </Button>
              <SignOutButton>
                <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700 hover:bg-red-50">
                  <LogOut className="h-4 w-4 mr-2" />
                  Logout
                </Button>
              </SignOutButton>
            </div>
          </div>
        </div>
      </nav>

      <div className="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Welcome back, {user.firstName || 'there'}! 👋
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Upload your videos and repurpose them for multiple social media platforms.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
              <Video className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Processing</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{stats.processing}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload" className="flex items-center space-x-2">
              <Upload className="h-4 w-4" />
              <span>Upload Video</span>
            </TabsTrigger>
            <TabsTrigger value="jobs" className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>Job Status</span>
            </TabsTrigger>
            <TabsTrigger value="profile" className="flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span>Profile</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-6">
            <VideoUploadComponent onUploadSuccess={(job) => {
              addJob(job);
              setActiveTab("jobs");
            }} />
          </TabsContent>

          <TabsContent value="jobs" className="space-y-6">
            <JobStatusList jobs={jobs} loading={loading} />
          </TabsContent>

          <TabsContent value="profile" className="space-y-6">
            <UserProfile />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
