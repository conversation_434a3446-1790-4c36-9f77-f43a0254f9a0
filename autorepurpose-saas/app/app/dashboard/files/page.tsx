
import { Metadata } from 'next';
import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import FileStats from '@/components/FileManagement/FileStats';
import DownloadList from '@/components/FileManagement/DownloadList';

export const metadata: Metadata = {
  title: 'File Management - AutoRepurpose',
  description: 'Manage and download your processed videos',
};

export default async function FilesPage() {
  const user = await currentUser();

  if (!user) {
    redirect('/sign-in');
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">File Management</h1>
        <p className="text-muted-foreground">
          View, download, and manage your processed videos. Files are automatically deleted after 3 days.
        </p>
      </div>

      {/* File Statistics */}
      <FileStats />

      {/* Downloads List */}
      <DownloadList />
    </div>
  );
}
