generator client {
    provider = "prisma-client-js"
    binaryTargets = ["native", "linux-musl-arm64-openssl-3.0.x"]
    output = "/home/<USER>/autorepurpose-saas/app/node_modules/.prisma/client"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

// Users table (synced with <PERSON>)
model User {
  id                    String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  clerkUserId          String    @unique @map("clerk_user_id") @db.VarChar(255)
  email                String    @db.VarChar(255)
  fullName             String?   @map("full_name") @db.VarChar(255)
  subscriptionStatus   String    @default("trial") @map("subscription_status") @db.VarChar(50)
  subscriptionId       String?   @map("subscription_id") @db.VarChar(255)
  currentPeriodEnd     DateTime? @map("current_period_end") @db.Timestamptz
  freeRepurposingsUsed Int       @default(0) @map("free_repurposings_used")
  
  // Payment provider customer IDs
  stripeCustomerId     String?   @map("stripe_customer_id") @db.VarChar(255)
  razorpayCustomerId   String?   @map("razorpay_customer_id") @db.VarChar(255)
  preferredPaymentProvider String? @default("stripe") @map("preferred_payment_provider") @db.VarChar(20)
  
  createdAt            DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt            DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  videoJobs            VideoJob[]
  paymentTransactions  PaymentTransaction[]
  usageLogs            UsageLog[]

  @@index([clerkUserId], name: "idx_users_clerk_id")
  @@index([subscriptionStatus], name: "idx_users_subscription")
  @@map("users")
}

// Video jobs table
model VideoJob {
  id                      String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId                  String    @map("user_id") @db.Uuid
  originalFilename        String    @map("original_filename") @db.VarChar(500)
  s3InputKey              String    @map("s3_input_key") @db.VarChar(1000)
  fileSizeBytes           BigInt    @map("file_size_bytes")
  durationSeconds         Decimal   @map("duration_seconds") @db.Decimal(10, 2)
  
  // Video metadata
  width                   Int
  height                  Int
  aspectRatio             Decimal   @map("aspect_ratio") @db.Decimal(10, 6)
  formType                String    @map("form_type") @db.VarChar(20)
  
  // Processing status
  status                  String    @default("pending") @db.VarChar(50)
  progress                Int       @default(0) // Progress percentage (0-100)
  errorMessage            String?   @map("error_message") @db.Text
  
  // Platform selection
  selectedPlatforms       Json      @map("selected_platforms")
  
  // Processing metadata
  queueJobId              String?   @map("queue_job_id") @db.VarChar(255)
  processingStartedAt     DateTime? @map("processing_started_at") @db.Timestamptz
  processingCompletedAt   DateTime? @map("processing_completed_at") @db.Timestamptz
  
  // Retention
  expiresAt               DateTime  @default(dbgenerated("(NOW() + INTERVAL '3 days')")) @map("expires_at") @db.Timestamptz
  
  createdAt               DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt               DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  user                    User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  outputs                 VideoOutput[]
  usageLogs               UsageLog[]

  @@index([userId], name: "idx_video_jobs_user_id")
  @@index([status], name: "idx_video_jobs_status")
  @@index([expiresAt], name: "idx_video_jobs_expires_at")
  @@map("video_jobs")
}

// Video outputs table (one per platform conversion)
model VideoOutput {
  id                      String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  jobId                   String    @map("job_id") @db.Uuid
  platformId              String    @map("platform_id") @db.VarChar(50)
  presetName              String    @map("preset_name") @db.VarChar(100)
  
  // Output file details
  s3OutputKey             String    @map("s3_output_key") @db.VarChar(1000)
  downloadUrl             String?   @map("download_url") @db.Text
  filename                String    @db.VarChar(500)
  fileSizeBytes           BigInt?   @map("file_size_bytes")
  duration                Decimal?  @db.Decimal(10, 2) // Duration in seconds
  
  // Technical specs applied
  resolution              String    @db.VarChar(20)
  bitrate                 String?   @db.VarChar(20)
  videoCodec              String?   @map("video_codec") @db.VarChar(20)
  audioCodec              String?   @map("audio_codec") @db.VarChar(20)
  
  // Processing status
  status                  String    @default("pending") @db.VarChar(50)
  errorMessage            String?   @map("error_message") @db.Text
  processingTimeSeconds   Int?      @map("processing_time_seconds")
  
  createdAt               DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt               DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  job                     VideoJob  @relation(fields: [jobId], references: [id], onDelete: Cascade)

  @@index([jobId], name: "idx_video_outputs_job_id")
  @@index([status], name: "idx_video_outputs_status")
  @@map("video_outputs")
}

// Platform rules cache table (for performance)
model PlatformRulesCache {
  id                      String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  rulesVersion            String    @map("rules_version") @db.VarChar(50)
  rulesData               Json      @map("rules_data")
  checksum                String    @db.VarChar(64)
  isActive                Boolean   @default(true) @map("is_active")
  createdAt               DateTime  @default(now()) @map("created_at") @db.Timestamptz

  @@map("platform_rules_cache")
}

// Payment transactions table (supports both Stripe and Razorpay)
model PaymentTransaction {
  id                      String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId                  String    @map("user_id") @db.Uuid
  
  // Payment provider identification
  provider                String    @db.VarChar(20) // 'stripe' or 'razorpay'
  
  // Stripe-specific fields
  stripePaymentIntentId   String?   @map("stripe_payment_intent_id") @db.VarChar(255)
  stripeSubscriptionId    String?   @map("stripe_subscription_id") @db.VarChar(255)
  stripeCustomerId        String?   @map("stripe_customer_id") @db.VarChar(255)
  
  // Razorpay-specific fields
  razorpayPaymentId       String?   @map("razorpay_payment_id") @db.VarChar(255)
  razorpayOrderId         String?   @map("razorpay_order_id") @db.VarChar(255)
  razorpaySubscriptionId  String?   @map("razorpay_subscription_id") @db.VarChar(255)
  razorpayCustomerId      String?   @map("razorpay_customer_id") @db.VarChar(255)
  
  // Common fields
  amountCents             Int       @map("amount_cents")
  currency                String    @default("usd") @db.VarChar(3)
  status                  String    @db.VarChar(50)
  transactionType         String    @map("transaction_type") @db.VarChar(50)
  providerTransactionId   String?   @map("provider_transaction_id") @db.VarChar(255)
  metadata                Json?     
  
  createdAt               DateTime  @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  user                    User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], name: "idx_payment_transactions_user")
  @@index([provider], name: "idx_payment_transactions_provider")
  @@map("payment_transactions")
}

// Usage tracking table
model UsageLog {
  id                      String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId                  String    @map("user_id") @db.Uuid
  jobId                   String?   @map("job_id") @db.Uuid
  action                  String    @db.VarChar(100)
  details                 Json?
  ipAddress               String?   @map("ip_address") @db.Inet
  userAgent               String?   @map("user_agent") @db.Text
  createdAt               DateTime  @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  user                    User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  job                     VideoJob? @relation(fields: [jobId], references: [id], onDelete: SetNull)

  @@map("usage_logs")
}
