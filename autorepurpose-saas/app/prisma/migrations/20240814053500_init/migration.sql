
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- CreateTable
CREATE TABLE "users" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "clerk_user_id" VARCHAR(255) NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "full_name" VARCHAR(255),
    "subscription_status" VARCHAR(50) NOT NULL DEFAULT 'trial',
    "subscription_id" VARCHAR(255),
    "current_period_end" TIMESTAMPTZ,
    "free_repurposings_used" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "video_jobs" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "original_filename" VARCHAR(500) NOT NULL,
    "s3_input_key" VARCHAR(1000) NOT NULL,
    "file_size_bytes" BIGINT NOT NULL,
    "duration_seconds" DECIMAL(10,2) NOT NULL,
    "width" INTEGER NOT NULL,
    "height" INTEGER NOT NULL,
    "aspect_ratio" DECIMAL(10,6) NOT NULL,
    "form_type" VARCHAR(20) NOT NULL,
    "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
    "error_message" TEXT,
    "selected_platforms" JSONB NOT NULL,
    "queue_job_id" VARCHAR(255),
    "processing_started_at" TIMESTAMPTZ,
    "processing_completed_at" TIMESTAMPTZ,
    "expires_at" TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '3 days'),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "video_jobs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "video_outputs" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "job_id" UUID NOT NULL,
    "platform_id" VARCHAR(50) NOT NULL,
    "preset_name" VARCHAR(100) NOT NULL,
    "s3_output_key" VARCHAR(1000) NOT NULL,
    "filename" VARCHAR(500) NOT NULL,
    "file_size_bytes" BIGINT,
    "resolution" VARCHAR(20) NOT NULL,
    "bitrate" VARCHAR(20),
    "video_codec" VARCHAR(20),
    "audio_codec" VARCHAR(20),
    "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
    "error_message" TEXT,
    "processing_time_seconds" INTEGER,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "video_outputs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "platform_rules_cache" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "rules_version" VARCHAR(50) NOT NULL,
    "rules_data" JSONB NOT NULL,
    "checksum" VARCHAR(64) NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "platform_rules_cache_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "payment_transactions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "stripe_payment_intent_id" VARCHAR(255),
    "stripe_subscription_id" VARCHAR(255),
    "amount_cents" INTEGER NOT NULL,
    "currency" VARCHAR(3) NOT NULL DEFAULT 'usd',
    "status" VARCHAR(50) NOT NULL,
    "transaction_type" VARCHAR(50) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "payment_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "usage_logs" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "job_id" UUID,
    "action" VARCHAR(100) NOT NULL,
    "details" JSONB,
    "ip_address" INET,
    "user_agent" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "usage_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_clerk_user_id_key" ON "users"("clerk_user_id");

-- CreateIndex
CREATE INDEX "idx_users_clerk_id" ON "users"("clerk_user_id");

-- CreateIndex
CREATE INDEX "idx_users_subscription" ON "users"("subscription_status");

-- CreateIndex
CREATE INDEX "idx_video_jobs_user_id" ON "video_jobs"("user_id");

-- CreateIndex
CREATE INDEX "idx_video_jobs_status" ON "video_jobs"("status");

-- CreateIndex
CREATE INDEX "idx_video_jobs_expires_at" ON "video_jobs"("expires_at");

-- CreateIndex
CREATE INDEX "idx_video_outputs_job_id" ON "video_outputs"("job_id");

-- CreateIndex
CREATE INDEX "idx_video_outputs_status" ON "video_outputs"("status");

-- CreateIndex
CREATE INDEX "idx_payment_transactions_user" ON "payment_transactions"("user_id");

-- AddForeignKey
ALTER TABLE "video_jobs" ADD CONSTRAINT "video_jobs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "video_outputs" ADD CONSTRAINT "video_outputs_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "video_jobs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment_transactions" ADD CONSTRAINT "payment_transactions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "usage_logs" ADD CONSTRAINT "usage_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "usage_logs" ADD CONSTRAINT "usage_logs_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "video_jobs"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_jobs_updated_at BEFORE UPDATE ON video_jobs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_outputs_updated_at BEFORE UPDATE ON video_outputs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
