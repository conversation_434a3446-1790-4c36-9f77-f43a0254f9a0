
"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { useUser } from '@clerk/nextjs';

interface VideoJob {
  id: string;
  originalFileName: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'queued';
  platformsRequested: string[];
  createdAt: string;
  updatedAt?: string;
  progress?: number;
  errorMessage?: string;
  outputs?: Array<{
    id: string;
    platform: string;
    downloadUrl?: string;
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'queued';
    fileSize?: number;
    duration?: number;
  }>;
}

interface UseRealtimeJobsReturn {
  jobs: VideoJob[];
  loading: boolean;
  error: string | null;
  refreshJobs: () => Promise<void>;
  addJob: (job: VideoJob) => void;
}

export function useRealtimeJobs(): UseRealtimeJobsReturn {
  const { user } = useUser();
  const [jobs, setJobs] = useState<VideoJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastFetchRef = useRef<number>(0);

  const fetchJobs = useCallback(async () => {
    if (!user) return;

    try {
      // First, mark stale jobs as failed
      try {
        await fetch('/api/jobs/mark-stale', { method: 'POST' });
      } catch (error) {
        console.warn('Failed to mark stale jobs:', error);
      }

      const response = await fetch('/api/jobs', {
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Ensure jobs is always an array with safe data
      const safeJobs = Array.isArray(data.jobs) ? data.jobs.map((job: any) => ({
        ...job,
        platformsRequested: Array.isArray(job.platformsRequested) ? job.platformsRequested : [],
        outputs: Array.isArray(job.outputs) ? job.outputs : [],
        createdAt: job.createdAt || job.created_at || new Date().toISOString(),
        updatedAt: job.updatedAt || job.updated_at || new Date().toISOString(),
      })) : [];
      
      setJobs(safeJobs);
      setError(null);
      lastFetchRef.current = Date.now();
    } catch (err) {
      console.error('Error fetching jobs:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  }, [user]);

  const refreshJobs = useCallback(async () => {
    setLoading(true);
    await fetchJobs();
  }, [fetchJobs]);

  const addJob = useCallback((newJob: VideoJob) => {
    setJobs(prevJobs => [newJob, ...prevJobs]);
  }, []);

  // Adaptive polling - faster when jobs are processing, slower when stable
  const getPollingInterval = useCallback((jobs: VideoJob[]) => {
    const hasProcessingJobs = jobs.some(job => 
      job.status === 'processing' || job.status === 'pending' || job.status === 'queued'
    );
    
    // 2 seconds if actively processing, 10 seconds if stable
    return hasProcessingJobs ? 2000 : 10000;
  }, []);

  // Set up intelligent polling
  useEffect(() => {
    if (!user) return;

    const setupPolling = () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      const interval = getPollingInterval(jobs);
      intervalRef.current = setInterval(fetchJobs, interval);
    };

    // Initial fetch
    fetchJobs();

    // Set up polling
    setupPolling();

    // Cleanup
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [user, jobs.length, fetchJobs, getPollingInterval]);

  // Page visibility API - pause polling when tab is not active
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Tab is hidden, stop polling
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      } else {
        // Tab is visible, resume polling
        fetchJobs(); // Immediate fetch
        const interval = getPollingInterval(jobs);
        intervalRef.current = setInterval(fetchJobs, interval);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [fetchJobs, getPollingInterval, jobs]);

  return {
    jobs,
    loading,
    error,
    refreshJobs,
    addJob
  };
}
