// Database connection and utilities
import { PrismaClient } from '@prisma/client';

// Prevent multiple instances of Prisma Client in development
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const db = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = db;
}

// Database utility functions
export class DatabaseService {
  
  /**
   * Create or update user from Clerk webhook
   */
  static async upsertUserFromClerk(clerkData: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
  }) {
    const fullName = clerkData.firstName && clerkData.lastName 
      ? `${clerkData.firstName} ${clerkData.lastName}`.trim()
      : clerkData.firstName || clerkData.lastName || null;

    return await db.user.upsert({
      where: { clerkUserId: clerkData.id },
      update: {
        email: clerkData.email,
        fullName: fullName,
        updatedAt: new Date()
      },
      create: {
        clerkUserId: clerkData.id,
        email: clerkData.email,
        fullName: fullName,
        subscriptionStatus: 'trial',
        freeRepurposingsUsed: 0
      }
    });
  }

  /**
   * Get user with usage statistics
   */
  static async getUserWithUsage(clerkUserId: string) {
    const user = await db.user.findUnique({
      where: { clerkUserId },
      include: {
        videoJobs: {
          include: {
            outputs: true
          }
        },
        paymentTransactions: true
      }
    });

    if (!user) return null;

    const totalJobs = user.videoJobs.length;
    const completedJobs = user.videoJobs.filter(job => job.status === 'completed').length;
    const failedJobs = user.videoJobs.filter(job => job.status === 'failed').length;
    const totalOutputs = user.videoJobs.reduce((sum, job) => sum + job.outputs.length, 0);
    const storageUsedBytes = user.videoJobs.reduce((sum, job) => 
      sum + Number(job.fileSizeBytes), 0
    );

    return {
      ...user,
      usage: {
        totalJobs,
        completedJobs,
        failedJobs,
        totalOutputs,
        storageUsedBytes,
        freeRepurposingsRemaining: Math.max(0, 3 - user.freeRepurposingsUsed)
      }
    };
  }

  /**
   * Create video job
   */
  static async createVideoJob(data: {
    userId: string;
    originalFilename: string;
    s3InputKey: string;
    fileSizeBytes: bigint;
    durationSeconds: number;
    width: number;
    height: number;
    aspectRatio: number;
    formType: string;
    selectedPlatforms: string[];
  }) {
    return await db.videoJob.create({
      data: {
        ...data,
        selectedPlatforms: data.selectedPlatforms,
        status: 'pending'
      }
    });
  }

  /**
   * Get user's video jobs with pagination
   */
  static async getUserJobs(
    clerkUserId: string, 
    page = 1, 
    limit = 10,
    status?: string
  ) {
    const user = await db.user.findUnique({
      where: { clerkUserId },
      select: { id: true }
    });

    if (!user) throw new Error('User not found');

    const whereClause = {
      userId: user.id,
      ...(status && { status })
    };

    const [jobs, total] = await Promise.all([
      db.videoJob.findMany({
        where: whereClause,
        include: {
          outputs: true
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      db.videoJob.count({ where: whereClause })
    ]);

    return {
      jobs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Log user usage
   */
  static async logUsage(
    clerkUserId: string,
    action: string,
    details?: any,
    jobId?: string,
    ipAddress?: string,
    userAgent?: string
  ) {
    const user = await db.user.findUnique({
      where: { clerkUserId },
      select: { id: true }
    });

    if (!user) return;

    return await db.usageLog.create({
      data: {
        userId: user.id,
        jobId,
        action,
        details,
        ipAddress,
        userAgent
      }
    });
  }

  /**
   * Clean up expired jobs
   */
  static async cleanupExpiredJobs() {
    const expiredJobs = await db.videoJob.findMany({
      where: {
        expiresAt: {
          lt: new Date()
        },
        status: {
          not: 'expired'
        }
      },
      include: {
        outputs: true
      }
    });

    // Mark as expired
    await db.videoJob.updateMany({
      where: {
        id: {
          in: expiredJobs.map(job => job.id)
        }
      },
      data: {
        status: 'expired'
      }
    });

    return expiredJobs;
  }
}

// Helper function for BigInt serialization
export function serializeBigInt(obj: any): any {
  if (typeof obj === 'bigint') {
    return obj.toString();
  }
  
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(serializeBigInt);
  }
  
  if (typeof obj === 'object') {
    const serialized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      serialized[key] = serializeBigInt(value);
    }
    return serialized;
  }
  
  return obj;
}

// Ensure connection is closed when the process exits
process.on('beforeExit', async () => {
  await db.$disconnect();
});
