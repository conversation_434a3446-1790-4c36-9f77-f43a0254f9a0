// Database service using Supabase REST API
import { supabaseClient } from './supabase-client';

export class DatabaseService {
  // Create or update user from Clerk data
  static async upsertUserFromClerk(userData: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
  }) {
    const fullName = [userData.firstName, userData.lastName].filter(Boolean).join(' ') || undefined;

    try {
      const user = await supabaseClient.upsertUser({
        clerk_user_id: userData.id,
        email: userData.email,
        full_name: fullName,
      });

      return user;
    } catch (error) {
      console.error('Error upserting user from Clerk:', error);
      throw error;
    }
  }

  // Get user by Clerk ID
  static async getUserByClerkId(clerkId: string) {
    try {
      return await supabaseClient.findUserByClerkId(clerkId);
    } catch (error) {
      console.error('Error getting user by Clerk ID:', error);
      throw error;
    }
  }

  // Get user statistics
  static async getUserStats(clerkUserId: string) {
    try {
      const stats = await supabaseClient.getUserStats(clerkUserId);
      
      if (!stats) {
        throw new Error('User not found');
      }

      return {
        totalJobs: stats.totalJobs,
        completedJobs: stats.completedJobs, 
        processingJobs: stats.processingJobs,
        failedJobs: stats.failedJobs,
        totalProcessingTime: stats.completedJobs * 120, // Mock: 2 minutes per job
        storageUsed: 0, // Mock for now
        averageProcessingTime: stats.completedJobs > 0 ? 120 : 0,
        freeRepurposingsUsed: stats.freeRepurposingsUsed,
        subscriptionStatus: stats.subscriptionStatus,
      };
    } catch (error) {
      console.error('Error getting user stats:', error);
      throw error;
    }
  }

  // Create a video job
  static async createVideoJob(data: {
    clerkUserId: string;
    originalFilename: string;
    s3InputKey: string;
    fileSizeBytes: number;
    durationSeconds?: number;
    targetPlatforms: string[];
    jobId?: string;
  }) {
    try {
      // Try to get existing user first
      let user = await this.getUserByClerkId(data.clerkUserId);
      
      // If user doesn't exist, create them automatically
      if (!user) {
        console.log('User not found in database, creating automatically for Clerk ID:', data.clerkUserId);
        try {
          // We'll create a basic user record - the webhook will update it later with full details
          user = await supabaseClient.createUser({
            clerk_user_id: data.clerkUserId,
            email: '<EMAIL>', // Placeholder - will be updated by webhook
            full_name: 'User', // Placeholder - will be updated by webhook
            subscription_status: 'trial'
          });
          console.log('Auto-created user:', user.id);
        } catch (createError) {
          console.error('Failed to auto-create user:', createError);
          // Try one more time to get the user (in case webhook just created it)
          user = await this.getUserByClerkId(data.clerkUserId);
          if (!user) {
            throw new Error(`User not found and could not be created for Clerk ID: ${data.clerkUserId}`);
          }
        }
      }

      const job = await supabaseClient.createVideoJob({
        user_id: user.id,
        original_filename: data.originalFilename,
        s3_input_key: data.s3InputKey,
        file_size_bytes: data.fileSizeBytes,
        duration_seconds: data.durationSeconds || 0,
        target_platforms: data.targetPlatforms,
        job_id: data.jobId,
      });

      console.log('Successfully created video job:', job.id);
      return job;
    } catch (error: any) {
      console.error('Error creating video job:', {
        error: error?.message || 'Unknown error',
        clerkUserId: data.clerkUserId,
        originalFilename: data.originalFilename,
        s3InputKey: data.s3InputKey
      });
      throw error;
    }
  }

  // Get user's video jobs with pagination
  static async getUserJobs(clerkUserId: string, page: number = 1, limit: number = 10, status?: string) {
    try {
      return await supabaseClient.getUserJobs(clerkUserId, page, limit, status);
    } catch (error) {
      console.error('Error getting user jobs:', error);
      throw error;
    }
  }

  // Update job status
  static async updateJobStatus(jobId: string, status: string, additionalData?: any) {
    try {
      const job = await supabaseClient.updateJobStatus(jobId, status, additionalData);
      return job;
    } catch (error) {
      console.error('Error updating job status:', error);
      throw error;
    }
  }

  // Simplified methods for compatibility - these will use mock data until we implement video outputs table
  
  // Get video outputs for a specific job (mock for now)
  static async getVideoOutputs(jobId: string) {
    // Mock data - implement when video_outputs table is ready
    return [];
  }

  // Update job progress (simplified)
  static async updateJobProgress(jobId: string, progress: number) {
    try {
      return await supabaseClient.updateJobStatus(jobId, 'processing', {
        progress: Math.max(0, Math.min(100, progress))
      });
    } catch (error) {
      console.error('Error updating job progress:', error);
      // Don't throw error for progress updates to avoid breaking the job
    }
  }

  // Create job output record (mock for now)
  static async createJobOutput(jobId: string, outputData: {
    platform: string;
    outputS3Key: string;
    downloadUrl: string;
    fileSize: number;
    duration: number;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    errorMessage?: string;
  }) {
    // Mock implementation - will implement when video_outputs table is ready
    console.log('Mock: Creating job output for', jobId, outputData);
    return { id: crypto.randomUUID(), ...outputData };
  }

  // Update job output status (mock for now) 
  static async updateJobOutput(outputId: string, status: 'pending' | 'processing' | 'completed' | 'failed', additionalData?: {
    downloadUrl?: string;
    fileSize?: number;
    duration?: number;
    errorMessage?: string;
  }) {
    // Mock implementation
    console.log('Mock: Updating job output', outputId, status, additionalData);
    return { id: outputId, status, ...additionalData };
  }

  // Get jobs with their outputs (simplified)
  static async getJobsWithOutputs(clerkUserId: string, limit: number = 50) {
    try {
      const result = await this.getUserJobs(clerkUserId, 1, limit);
      // Add mock outputs for compatibility
      const jobs = result.jobs.map((job: any) => ({
        ...job,
        outputs: [] // Mock empty outputs for now
      }));
      return jobs;
    } catch (error) {
      console.error('Error getting jobs with outputs:', error);
      return [];
    }
  }

  // Get job by ID with outputs (simplified)
  static async getJobWithOutputs(jobId: string) {
    try {
      // Mock implementation - just return job data with empty outputs
      return {
        id: jobId,
        outputs: [] // Mock empty outputs
      };
    } catch (error) {
      console.error('Error getting job with outputs:', error);
      return null;
    }
  }

  // Delete video job
  static async deleteVideoJob(jobId: string) {
    try {
      return await supabaseClient.deleteVideoJob(jobId);
    } catch (error) {
      console.error('Error deleting video job:', error);
      throw error;
    }
  }

  // Mark stale jobs as failed
  static async markStaleJobsAsFailed() {
    try {
      return await supabaseClient.markStaleJobsAsFailed();
    } catch (error) {
      console.error('Error marking stale jobs as failed:', error);
      throw error;
    }
  }
}