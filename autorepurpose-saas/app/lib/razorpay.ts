
import Razorpay from 'razorpay';

// Initialize Razorpay instance
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID!,
  key_secret: process.env.RAZORPAY_KEY_SECRET!,
});

export default razorpay;

// Razorpay configuration constants
export const RAZORPAY_CONFIG = {
  MONTHLY_PLAN_AMOUNT: 250000, // ₹2,500 in paise
  CURRENCY: 'INR',
  PLAN_ID: process.env.RAZORPAY_PLAN_ID,
  KEY_ID: process.env.RAZORPAY_KEY_ID,
};

// Helper function to create subscription
export async function createRazorpaySubscription(customerId: string, planId: string) {
  try {
    const subscription = await razorpay.subscriptions.create({
      plan_id: planId,
      customer_id: customerId,
      total_count: 12, // 12 months
      start_at: Math.floor(Date.now() / 1000), // Start immediately
      customer_notify: 1
    } as any);

    return subscription;
  } catch (error) {
    console.error('Error creating Razorpay subscription:', error);
    throw error;
  }
}

// Helper function to create customer
export async function createRazorpayCustomer(email: string, name: string, phone?: string) {
  try {
    const customer = await razorpay.customers.create({
      name,
      email,
      contact: phone,
      notes: {
        created_from: 'autorepurpose_saas'
      }
    });

    return customer;
  } catch (error) {
    console.error('Error creating Razorpay customer:', error);
    throw error;
  }
}

// Helper function to verify payment signature
export function verifyRazorpaySignature(
  orderId: string,
  paymentId: string,
  signature: string,
  secret: string
): boolean {
  const crypto = require('crypto');
  const hmac = crypto.createHmac('sha256', secret);
  hmac.update(`${orderId}|${paymentId}`);
  const generatedSignature = hmac.digest('hex');
  
  return generatedSignature === signature;
}
