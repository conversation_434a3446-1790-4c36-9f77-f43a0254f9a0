
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs/promises';
import { S3Client, GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Readable } from 'stream';
import { MockVideoProcessor } from './video-processor-mock';

const execAsync = promisify(exec);

interface PlatformRule {
  platform: string;
  displayName: string;
  maxDuration: number;
  aspectRatio: string;
  resolution: {
    width: number;
    height: number;
  };
  maxFileSize: number; // in MB
  format: string;
  videoCodec: string;
  audioCodec: string;
  framerate: number;
  bitrate: {
    video: string;
    audio: string;
  };
  description: string;
}

interface ProcessingOptions {
  inputPath: string;
  outputPath: string;
  rule: PlatformRule;
  onProgress?: (progress: number) => void;
}

interface VideoMetadata {
  duration: number;
  width: number;
  height: number;
  framerate: number;
  bitrate: number;
  format: string;
  size: number;
}

export class VideoProcessor {
  private s3Client: S3Client;
  private tempDir: string;
  private mockProcessor: MockVideoProcessor;
  private ffmpegAvailable: boolean = false;

  constructor() {
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION!,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      },
    });
    
    this.tempDir = path.join(process.cwd(), 'temp');
    this.mockProcessor = new MockVideoProcessor();
    this.ensureTempDir();
    this.checkFFmpegAvailability();
  }

  private async checkFFmpegAvailability(): Promise<void> {
    try {
      await execAsync('ffmpeg -version');
      this.ffmpegAvailable = true;
      console.log('✅ FFmpeg is available - using real video processing');
    } catch (error) {
      this.ffmpegAvailable = false;
      console.log('⚠️ FFmpeg not available - using mock video processing for demonstration');
    }
  }

  private async ensureTempDir(): Promise<void> {
    try {
      await fs.access(this.tempDir);
    } catch {
      await fs.mkdir(this.tempDir, { recursive: true });
    }
  }

  /**
   * Download file from S3 to local temp directory
   */
  async downloadFromS3(s3Key: string, localPath: string): Promise<void> {
    // Use mock processor if ffmpeg is not available
    if (!this.ffmpegAvailable) {
      return this.mockProcessor.downloadFromS3(s3Key, localPath);
    }

    const command = new GetObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET!,
      Key: s3Key,
    });

    const response = await this.s3Client.send(command);
    
    if (response.Body) {
      const stream = response.Body as Readable;
      const writeStream = require('fs').createWriteStream(localPath);
      
      return new Promise((resolve, reject) => {
        stream.pipe(writeStream);
        writeStream.on('finish', resolve);
        writeStream.on('error', reject);
      });
    }
    
    throw new Error('Failed to download file from S3');
  }

  /**
   * Upload processed file to S3
   */
  async uploadToS3(localPath: string, s3Key: string): Promise<string> {
    // Use mock processor if ffmpeg is not available
    if (!this.ffmpegAvailable) {
      return this.mockProcessor.uploadToS3(localPath, s3Key);
    }

    const fileBuffer = await fs.readFile(localPath);
    
    const command = new PutObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET!,
      Key: s3Key,
      Body: fileBuffer,
      ContentType: 'video/mp4',
    });

    await this.s3Client.send(command);
    
    // Generate signed URL for download
    const downloadCommand = new GetObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET!,
      Key: s3Key,
    });
    
    return await getSignedUrl(this.s3Client, downloadCommand, { expiresIn: 3600 * 24 * 3 }); // 3 days
  }

  /**
   * Get video metadata using ffprobe
   */
  async getVideoMetadata(videoPath: string): Promise<VideoMetadata> {
    const command = `ffprobe -v quiet -print_format json -show_format -show_streams "${videoPath}"`;
    
    try {
      const { stdout } = await execAsync(command);
      const metadata = JSON.parse(stdout);
      
      const videoStream = metadata.streams.find((stream: any) => stream.codec_type === 'video');
      const format = metadata.format;
      
      if (!videoStream) {
        throw new Error('No video stream found');
      }
      
      return {
        duration: parseFloat(format.duration) || 0,
        width: parseInt(videoStream.width) || 0,
        height: parseInt(videoStream.height) || 0,
        framerate: eval(videoStream.r_frame_rate) || 30,
        bitrate: parseInt(format.bit_rate) || 0,
        format: format.format_name || '',
        size: parseInt(format.size) || 0,
      };
    } catch (error) {
      console.error('Error getting video metadata:', error);
      throw new Error('Failed to get video metadata');
    }
  }

  /**
   * Process video according to platform rules
   */
  async processVideo(options: ProcessingOptions): Promise<{
    success: boolean;
    outputPath?: string;
    duration?: number;
    fileSize?: number;
    error?: string;
  }> {
    // Use mock processor if ffmpeg is not available
    if (!this.ffmpegAvailable) {
      return this.mockProcessor.processVideo(options);
    }

    const { inputPath, outputPath, rule, onProgress } = options;
    
    try {
      // Get input video metadata
      const inputMetadata = await this.getVideoMetadata(inputPath);
      
      // Build ffmpeg command
      const ffmpegCommand = this.buildFFmpegCommand(inputPath, outputPath, rule, inputMetadata);
      
      console.log('Executing ffmpeg command:', ffmpegCommand);
      
      // Execute ffmpeg with progress tracking
      await this.executeFFmpegWithProgress(ffmpegCommand, inputMetadata.duration, onProgress);
      
      // Get output video metadata
      const outputMetadata = await this.getVideoMetadata(outputPath);
      
      return {
        success: true,
        outputPath,
        duration: outputMetadata.duration,
        fileSize: outputMetadata.size,
      };
      
    } catch (error) {
      console.error('Video processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Build ffmpeg command based on platform rules
   */
  private buildFFmpegCommand(
    inputPath: string, 
    outputPath: string, 
    rule: PlatformRule, 
    inputMetadata: VideoMetadata
  ): string {
    const parts = ['ffmpeg', '-y', '-i', `"${inputPath}"`];
    
    // Video codec
    parts.push('-c:v', rule.videoCodec);
    
    // Resolution and aspect ratio
    const { width, height } = rule.resolution;
    parts.push('-s', `${width}x${height}`);
    
    // Video bitrate
    parts.push('-b:v', rule.bitrate.video);
    
    // Frame rate
    parts.push('-r', rule.framerate.toString());
    
    // Audio codec
    parts.push('-c:a', rule.audioCodec);
    
    // Audio bitrate
    parts.push('-b:a', rule.bitrate.audio);
    
    // Duration limit (if video is longer than max duration)
    if (inputMetadata.duration > rule.maxDuration) {
      parts.push('-t', rule.maxDuration.toString());
    }
    
    // Pixel format for compatibility
    parts.push('-pix_fmt', 'yuv420p');
    
    // Optimize for streaming
    parts.push('-movflags', '+faststart');
    
    // Progress reporting
    parts.push('-progress', 'pipe:1');
    
    // Output path
    parts.push(`"${outputPath}"`);
    
    return parts.join(' ');
  }

  /**
   * Execute ffmpeg with progress tracking
   */
  private executeFFmpegWithProgress(
    command: string, 
    totalDuration: number, 
    onProgress?: (progress: number) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const process = require('child_process').spawn('bash', ['-c', command]);
      
      let stderr = '';
      
      process.stdout.on('data', (data: Buffer) => {
        const output = data.toString();
        
        // Parse progress from ffmpeg output
        const timeMatch = output.match(/out_time_ms=(\d+)/);
        if (timeMatch && onProgress && totalDuration > 0) {
          const currentTime = parseInt(timeMatch[1]) / 1000000; // Convert microseconds to seconds
          const progress = Math.min(100, (currentTime / totalDuration) * 100);
          onProgress(Math.round(progress));
        }
      });
      
      process.stderr.on('data', (data: Buffer) => {
        stderr += data.toString();
      });
      
      process.on('close', (code: number) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`FFmpeg failed with code ${code}: ${stderr}`));
        }
      });
      
      process.on('error', (error: Error) => {
        reject(error);
      });
    });
  }

  /**
   * Clean up temporary files
   */
  async cleanup(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        await fs.unlink(filePath);
        console.log(`Cleaned up temp file: ${filePath}`);
      } catch (error) {
        console.warn(`Failed to clean up temp file ${filePath}:`, error);
      }
    }
  }

  /**
   * Validate video file before processing
   */
  async validateVideo(videoPath: string): Promise<{
    valid: boolean;
    error?: string;
    metadata?: VideoMetadata;
  }> {
    // Use mock processor if ffmpeg is not available
    if (!this.ffmpegAvailable) {
      return this.mockProcessor.validateVideo(videoPath);
    }

    try {
      const metadata = await this.getVideoMetadata(videoPath);
      
      // Basic validation
      if (metadata.duration <= 0) {
        return { valid: false, error: 'Invalid video duration' };
      }
      
      if (metadata.width <= 0 || metadata.height <= 0) {
        return { valid: false, error: 'Invalid video dimensions' };
      }
      
      // File size check (max 5GB)
      const maxSize = 5 * 1024 * 1024 * 1024; // 5GB in bytes
      if (metadata.size > maxSize) {
        return { valid: false, error: 'Video file too large (max 5GB)' };
      }
      
      return { valid: true, metadata };
      
    } catch (error) {
      return { 
        valid: false, 
        error: error instanceof Error ? error.message : 'Failed to validate video' 
      };
    }
  }

  /**
   * Check if FFmpeg is available on the system
   */
  get isFFmpegAvailable(): boolean {
    return this.ffmpegAvailable;
  }

  /**
   * Get processor info
   */
  getProcessorInfo() {
    return {
      ffmpegAvailable: this.ffmpegAvailable,
      processingMode: this.ffmpegAvailable ? 'real' : 'mock',
      tempDir: this.tempDir,
    };
  }
}

export default VideoProcessor;
