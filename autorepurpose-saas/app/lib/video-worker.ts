
import { Worker, Job, Queue } from 'bullmq';
import { Redis } from 'ioredis';
import VideoProcessor from './video-processor';
import PlatformRulesService from './platform-rules';
import { DatabaseService } from './db-extensions';
import path from 'path';
import { randomUUID } from 'crypto';

interface VideoJobData {
  jobId: string;
  userId: string;
  inputVideoUrl: string;
  inputVideoS3Key: string;
  originalFileName: string;
  platformsRequested: string[];
  metadata?: {
    duration?: number;
    size?: number;
    format?: string;
  };
}

interface ProcessingResult {
  platform: string;
  success: boolean;
  outputS3Key?: string;
  downloadUrl?: string;
  duration?: number;
  fileSize?: number;
  error?: string;
}

export class VideoProcessingWorker {
  private worker: Worker;
  private redis: Redis;
  private videoProcessor: VideoProcessor;
  private platformRules: PlatformRulesService;

  constructor() {
    // Initialize Redis connection
    this.redis = new Redis(process.env.REDIS_URL!, {
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });

    this.videoProcessor = new VideoProcessor();
    this.platformRules = PlatformRulesService.getInstance();

    // Create worker
    this.worker = new Worker('video-processing', this.processJob.bind(this), {
      connection: this.redis,
      concurrency: 3, // Process up to 3 jobs simultaneously
      removeOnComplete: { age: 3600, count: 10 }, // Keep last 10 completed jobs for 1 hour
      removeOnFail: { age: 3600 * 24, count: 20 }, // Keep last 20 failed jobs for 24 hours
    });

    this.setupWorkerEvents();
  }

  private setupWorkerEvents(): void {
    this.worker.on('completed', (job: Job) => {
      console.log(`Job ${job.id} completed successfully`);
    });

    this.worker.on('failed', (job: Job | undefined, error: Error) => {
      console.error(`Job ${job?.id} failed:`, error);
    });

    this.worker.on('progress', (job: Job, progress: any) => {
      const progressNum = typeof progress === 'number' ? progress : 0;
      console.log(`Job ${job.id} progress: ${progressNum}%`);
      this.updateJobProgress(job.data.jobId, progressNum);
    });

    this.worker.on('error', (error: Error) => {
      console.error('Worker error:', error);
    });
  }

  private async processJob(job: Job<VideoJobData>): Promise<ProcessingResult[]> {
    const { jobId, userId, inputVideoS3Key, originalFileName, platformsRequested } = job.data;
    
    console.log(`Starting job ${jobId} for user ${userId}`);
    
    try {
      // Update job status to processing
      await DatabaseService.updateJobStatus(jobId, 'processing');
      
      // Get platform rules for requested platforms
      const platformRules = await this.platformRules.getRulesForPlatforms(platformsRequested);
      
      if (platformRules.length === 0) {
        throw new Error('No valid platform rules found for requested platforms');
      }

      // Validate platforms
      const { valid: validRules, invalid: invalidPlatforms } = await this.platformRules.validatePlatforms(platformsRequested);
      
      if (invalidPlatforms.length > 0) {
        console.warn(`Invalid platforms requested: ${invalidPlatforms.join(', ')}`);
      }

      // Create temp directory for processing
      const tempDir = path.join(process.cwd(), 'temp', jobId);
      await this.ensureDirectoryExists(tempDir);

      const inputFilePath = path.join(tempDir, `input_${originalFileName}`);
      const results: ProcessingResult[] = [];

      try {
        // Download input video from S3
        job.updateProgress(10);
        await this.videoProcessor.downloadFromS3(inputVideoS3Key, inputFilePath);
        
        // Validate input video
        const validation = await this.videoProcessor.validateVideo(inputFilePath);
        if (!validation.valid) {
          throw new Error(validation.error || 'Invalid video file');
        }

        console.log('Video validation passed:', validation.metadata);

        // Process video for each platform
        const totalPlatforms = validRules.length;
        
        for (let i = 0; i < validRules.length; i++) {
          const rule = validRules[i];
          const startProgress = 20 + (i * 60 / totalPlatforms);
          const endProgress = 20 + ((i + 1) * 60 / totalPlatforms);
          
          try {
            console.log(`Processing for ${rule.displayName}...`);
            job.updateProgress(startProgress);
            
            const outputFileName = `${jobId}_${rule.platform}.${rule.format}`;
            const outputFilePath = path.join(tempDir, outputFileName);
            const outputS3Key = `processed/${userId}/${jobId}/${outputFileName}`;
            
            // Process video with platform-specific settings
            const processResult = await this.videoProcessor.processVideo({
              inputPath: inputFilePath,
              outputPath: outputFilePath,
              rule,
              onProgress: (progress) => {
                const jobProgress = startProgress + (progress * (endProgress - startProgress) / 100);
                job.updateProgress(Math.round(jobProgress));
              },
            });

            if (processResult.success && processResult.outputPath) {
              // Upload processed video to S3
              const downloadUrl = await this.videoProcessor.uploadToS3(
                processResult.outputPath,
                outputS3Key
              );

              // Create job output record
              await DatabaseService.createJobOutput(jobId, {
                platform: rule.platform,
                outputS3Key,
                downloadUrl,
                fileSize: processResult.fileSize || 0,
                duration: processResult.duration || 0,
                status: 'completed',
              });

              results.push({
                platform: rule.platform,
                success: true,
                outputS3Key,
                downloadUrl,
                duration: processResult.duration,
                fileSize: processResult.fileSize,
              });

              console.log(`✅ Successfully processed for ${rule.displayName}`);
              
            } else {
              throw new Error(processResult.error || 'Processing failed');
            }
            
          } catch (platformError) {
            console.error(`❌ Failed to process for ${rule.displayName}:`, platformError);
            
            // Create failed output record
            await DatabaseService.createJobOutput(jobId, {
              platform: rule.platform,
              outputS3Key: '',
              downloadUrl: '',
              fileSize: 0,
              duration: 0,
              status: 'failed',
              errorMessage: platformError instanceof Error ? platformError.message : 'Unknown error',
            });

            results.push({
              platform: rule.platform,
              success: false,
              error: platformError instanceof Error ? platformError.message : 'Unknown error',
            });
          }
        }

        // Clean up temp files
        job.updateProgress(90);
        await this.videoProcessor.cleanup([inputFilePath, ...results.map(r => 
          r.outputS3Key ? path.join(tempDir, `${jobId}_${r.platform}.mp4`) : ''
        ).filter(Boolean)]);

        // Update final job status
        const hasAnySuccess = results.some(r => r.success);
        const finalStatus = hasAnySuccess ? 'completed' : 'failed';
        
        await DatabaseService.updateJobStatus(jobId, finalStatus, {
          completedAt: new Date(),
          outputsGenerated: results.filter(r => r.success).length,
          totalOutputs: results.length,
        });

        job.updateProgress(100);
        console.log(`✅ Job ${jobId} completed with ${results.filter(r => r.success).length}/${results.length} successful outputs`);
        
        return results;

      } catch (error) {
        console.error(`❌ Job ${jobId} failed:`, error);
        
        // Update job status to failed
        await DatabaseService.updateJobStatus(jobId, 'failed', {
          completedAt: new Date(),
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        });
        
        throw error;
      }
      
    } catch (error) {
      console.error(`Fatal error in job ${jobId}:`, error);
      throw error;
    }
  }

  private async updateJobProgress(jobId: string, progress: number): Promise<void> {
    try {
      await DatabaseService.updateJobProgress(jobId, progress);
    } catch (error) {
      console.warn('Failed to update job progress:', error);
    }
  }

  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    const fs = require('fs').promises;
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  public async start(): Promise<void> {
    console.log('🚀 Video processing worker started');
  }

  public async stop(): Promise<void> {
    await this.worker.close();
    await this.redis.quit();
    console.log('🛑 Video processing worker stopped');
  }

  public getWorkerInfo() {
    return {
      name: 'video-processing',
      concurrency: 3,
      status: this.worker ? 'running' : 'stopped',
    };
  }
}

// Singleton instance
let workerInstance: VideoProcessingWorker | null = null;

export function getVideoProcessingWorker(): VideoProcessingWorker {
  if (!workerInstance) {
    workerInstance = new VideoProcessingWorker();
  }
  return workerInstance;
}

// For manual testing and development
if (require.main === module) {
  const worker = new VideoProcessingWorker();
  worker.start();
  
  // Graceful shutdown
  process.on('SIGINT', async () => {
    console.log('Shutting down worker...');
    await worker.stop();
    process.exit(0);
  });
}

export default VideoProcessingWorker;
