
// Platform Rules Types based on video_platforms_rules_updated.json

export interface PlatformRulesMetadata {
  created_date: string;
  version: string;
  description: string;
  last_updated: string;
  source: string;
  form_type_support: boolean;
  repurposing_mode: string;
}

export interface FormClassificationLogic {
  short_form: {
    duration_seconds: string;
    aspect_ratio_height_width: string;
    description: string;
  };
  long_form: {
    criteria: string;
    description: string;
  };
}

export interface ValidationRules {
  classification_function: string;
  same_form_enforcement: boolean;
  cross_form_conversion: boolean;
}

export interface FormClassification {
  logic: FormClassificationLogic;
  validation_rules: ValidationRules;
}

export interface AspectRatios {
  recommended?: string | string[];
  supported: string[];
  range?: string;
  required?: string;
  notes?: string;
}

export interface Resolution {
  minimum?: string;
  recommended: string;
  maximum?: string;
  supported_range?: string;
}

export interface Duration {
  minimum?: string;
  maximum: string;
  recommended?: string;
  form_classification_max?: number;
  organic?: string;
  ads?: string;
  ads_max?: string;
  standard_users?: string;
  premium_users?: string;
}

export interface FileSize {
  maximum?: string;
  general?: string;
  ads?: string;
  organic?: string;
  standard_users?: string;
  premium_users?: string;
}

export interface FileFormats {
  primary?: string[];
  supported: string[];
  recommended?: string;
  general?: string[];
  ads?: string;
}

export interface TechnicalSpecifications {
  aspect_ratios: AspectRatios;
  resolution: Resolution;
  duration: Duration;
  file_size?: FileSize;
  file_formats: FileFormats;
  video_codec?: string;
  audio_codec?: string;
  frame_rate?: string;
  bitrate?: {
    minimum?: string;
    recommended?: string;
  };
}

export interface ConversionPreset {
  resolution: string;
  bitrate: string;
  frame_rate: string;
  audio_bitrate: string;
  video_codec: string;
  audio_codec: string;
}

export interface ConversionPresets {
  [presetName: string]: ConversionPreset;
}

export interface FormTypeSpecs {
  technical_specifications: TechnicalSpecifications;
  conversion_presets: ConversionPresets;
}

export interface ContentPolicies {
  community_guidelines?: string[] | {
    focus: string[];
    prohibited: string[];
  };
  community_standards?: string;
  prohibited?: string[];
  authenticity?: string;
  branded_content?: {
    disclosure_required: boolean;
    paid_partnership_label: string;
  };
  caption_limits?: {
    non_spark_ads: string;
    emojis_supported: boolean;
    clickable_links_restricted: boolean;
  };
  audio_requirements?: {
    commercial_music_library: string;
    copyright_enforcement: string;
    audio_quality: string;
  };
  strikes_system?: boolean;
  channel_termination?: string;
}

export interface SafeZones {
  required: boolean;
  varies_by_region: boolean;
  top_margin: string;
  bottom_margin: string;
  description: string;
}

export interface Platform {
  name: string;
  form_types: {
    short_form?: FormTypeSpecs;
    long_form?: FormTypeSpecs;
  };
  content_policies?: ContentPolicies;
  safe_zones?: SafeZones;
}

export interface CompatiblePair {
  source: string;
  targets: string[];
}

export interface ConversionRules {
  short_to_short: {
    allowed: boolean;
    compatible_pairs: CompatiblePair[];
  };
  long_to_long: {
    allowed: boolean;
    compatible_pairs: CompatiblePair[];
  };
  cross_form_conversion: {
    short_to_long: boolean;
    long_to_short: boolean;
    reason: string;
  };
}

export interface FormCompatibilityMatrix {
  short_form_platforms: string[];
  long_form_platforms: string[];
  conversion_rules: ConversionRules;
}

export interface ClassificationFunction {
  name: string;
  parameters: string[];
  logic: {
    short_form_criteria: {
      duration: string;
      aspect_ratio: string;
    };
    implementation: string;
  };
}

export interface SameFormEnforcement {
  enabled: boolean;
  validation_steps: string[];
}

export interface ConversionValidation {
  required_checks: string[];
}

export interface ValidationLogic {
  form_classification_function: ClassificationFunction;
  same_form_enforcement: SameFormEnforcement;
  conversion_validation: ConversionValidation;
}

export interface PlatformIdentifiers {
  short_form_identifiers: Record<string, string>;
  long_form_identifiers: Record<string, string>;
}

export interface UsageGuidelines {
  platform_identification: PlatformIdentifiers;
  conversion_best_practices: {
    quality_optimization: string[];
    content_adaptation: string[];
  };
}

export interface ResponseFormats {
  classification_response: {
    form_type: string;
    duration_seconds: string;
    aspect_ratio: string;
    classification_confidence: string;
  };
  validation_response: {
    valid: string;
    source_form: string;
    target_form: string;
    compatibility: string;
    errors: string;
  };
}

export interface APIIntegration {
  classification_endpoint: string;
  validation_endpoint: string;
  conversion_endpoint: string;
  presets_endpoint: string;
  response_formats: ResponseFormats;
}

export interface MaintenanceGuide {
  form_type_updates: {
    classification_logic: string;
    duration_thresholds: string;
    aspect_ratio_trends: string;
  };
  conversion_presets: {
    [key: string]: any;
  };
}

export interface PlatformRules {
  metadata: PlatformRulesMetadata;
  form_classification: FormClassification;
  platforms: Record<string, Platform>;
  form_compatibility_matrix: FormCompatibilityMatrix;
  validation_logic: ValidationLogic;
  usage_guidelines: UsageGuidelines;
  api_integration: APIIntegration;
  maintenance_guide: MaintenanceGuide;
}

// Helper types for video classification
export type FormType = 'short_form' | 'long_form';
export type JobStatus = 'pending' | 'classifying' | 'queued' | 'processing' | 'completed' | 'failed' | 'expired';
export type OutputStatus = 'pending' | 'processing' | 'completed' | 'failed';
export type SubscriptionStatus = 'trial' | 'active' | 'cancelled' | 'expired';

export interface VideoMetadata {
  width: number;
  height: number;
  duration_seconds: number;
  aspect_ratio: number;
  form_type: FormType;
  file_size_bytes: number;
}

export interface ClassificationResult {
  form_type: FormType;
  duration_seconds: number;
  dimensions: {
    width: number;
    height: number;
    aspect_ratio: number;
  };
  classification_confidence: number;
  compatible_platforms: string[];
}

export interface JobProgress {
  id: string;
  status: JobStatus;
  progress: number; // 0-100
  current_step: string;
  outputs: {
    platform_id: string;
    status: OutputStatus;
    file_size?: number;
    error_message?: string;
  }[];
  estimated_completion?: string;
  error_message?: string;
}
