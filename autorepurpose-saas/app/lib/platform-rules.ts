
import path from 'path';
import fs from 'fs/promises';

export interface PlatformRule {
  platform: string;
  displayName: string;
  maxDuration: number;
  aspectRatio: string;
  resolution: {
    width: number;
    height: number;
  };
  maxFileSize: number;
  format: string;
  videoCodec: string;
  audioCodec: string;
  framerate: number;
  bitrate: {
    video: string;
    audio: string;
  };
  description: string;
  category?: string;
  tags?: string[];
}

export interface PlatformRulesData {
  version: string;
  lastUpdated: string;
  platforms: PlatformRule[];
}

export class PlatformRulesService {
  private static instance: PlatformRulesService;
  private rules: PlatformRulesData | null = null;
  private rulesPath: string;

  constructor() {
    this.rulesPath = path.join(process.cwd(), 'data', 'video_platforms_rules_updated.json');
  }

  public static getInstance(): PlatformRulesService {
    if (!PlatformRulesService.instance) {
      PlatformRulesService.instance = new PlatformRulesService();
    }
    return PlatformRulesService.instance;
  }

  /**
   * Load platform rules from JSON file
   */
  async loadRules(): Promise<PlatformRulesData> {
    if (this.rules) {
      return this.rules;
    }

    try {
      const fileContent = await fs.readFile(this.rulesPath, 'utf-8');
      this.rules = JSON.parse(fileContent);
      
      if (!this.rules || !this.rules.platforms) {
        throw new Error('Invalid rules file format');
      }
      
      console.log(`Loaded ${this.rules.platforms.length} platform rules from ${this.rulesPath}`);
      return this.rules;
      
    } catch (error) {
      console.error('Error loading platform rules:', error);
      
      // Return default rules if file is missing
      return this.getDefaultRules();
    }
  }

  /**
   * Get rules for specific platforms
   */
  async getRulesForPlatforms(platformNames: string[]): Promise<PlatformRule[]> {
    const allRules = await this.loadRules();
    
    return allRules.platforms.filter(rule => 
      platformNames.some(name => 
        name.toLowerCase() === rule.platform.toLowerCase() ||
        name.toLowerCase() === rule.displayName.toLowerCase()
      )
    );
  }

  /**
   * Get all available platforms
   */
  async getAllPlatforms(): Promise<PlatformRule[]> {
    const rules = await this.loadRules();
    return rules.platforms;
  }

  /**
   * Get platform rule by name
   */
  async getPlatformRule(platformName: string): Promise<PlatformRule | null> {
    const rules = await this.loadRules();
    
    return rules.platforms.find(rule => 
      rule.platform.toLowerCase() === platformName.toLowerCase() ||
      rule.displayName.toLowerCase() === platformName.toLowerCase()
    ) || null;
  }

  /**
   * Get platforms grouped by category
   */
  async getPlatformsByCategory(): Promise<{[category: string]: PlatformRule[]}> {
    const rules = await this.loadRules();
    const grouped: {[category: string]: PlatformRule[]} = {};
    
    for (const rule of rules.platforms) {
      const category = rule.category || 'Other';
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(rule);
    }
    
    return grouped;
  }

  /**
   * Validate platform selection
   */
  async validatePlatforms(platformNames: string[]): Promise<{
    valid: PlatformRule[];
    invalid: string[];
  }> {
    const rules = await this.loadRules();
    const valid: PlatformRule[] = [];
    const invalid: string[] = [];
    
    for (const platformName of platformNames) {
      const rule = rules.platforms.find(r => 
        r.platform.toLowerCase() === platformName.toLowerCase() ||
        r.displayName.toLowerCase() === platformName.toLowerCase()
      );
      
      if (rule) {
        valid.push(rule);
      } else {
        invalid.push(platformName);
      }
    }
    
    return { valid, invalid };
  }

  /**
   * Get default platform rules (fallback)
   */
  private getDefaultRules(): PlatformRulesData {
    return {
      version: "1.0.0",
      lastUpdated: new Date().toISOString(),
      platforms: [
        {
          platform: "instagram_story",
          displayName: "Instagram Story",
          maxDuration: 15,
          aspectRatio: "9:16",
          resolution: { width: 1080, height: 1920 },
          maxFileSize: 100,
          format: "mp4",
          videoCodec: "libx264",
          audioCodec: "aac",
          framerate: 30,
          bitrate: { video: "2M", audio: "128k" },
          description: "Vertical video format for Instagram Stories",
          category: "Social Media"
        },
        {
          platform: "instagram_reel",
          displayName: "Instagram Reel",
          maxDuration: 90,
          aspectRatio: "9:16",
          resolution: { width: 1080, height: 1920 },
          maxFileSize: 500,
          format: "mp4",
          videoCodec: "libx264",
          audioCodec: "aac",
          framerate: 30,
          bitrate: { video: "3.5M", audio: "128k" },
          description: "Vertical video format for Instagram Reels",
          category: "Social Media"
        },
        {
          platform: "youtube_shorts",
          displayName: "YouTube Shorts",
          maxDuration: 60,
          aspectRatio: "9:16",
          resolution: { width: 1080, height: 1920 },
          maxFileSize: 512,
          format: "mp4",
          videoCodec: "libx264",
          audioCodec: "aac",
          framerate: 30,
          bitrate: { video: "4M", audio: "128k" },
          description: "Vertical short-form content for YouTube",
          category: "Video Platform"
        },
        {
          platform: "tiktok",
          displayName: "TikTok",
          maxDuration: 180,
          aspectRatio: "9:16",
          resolution: { width: 1080, height: 1920 },
          maxFileSize: 500,
          format: "mp4",
          videoCodec: "libx264",
          audioCodec: "aac",
          framerate: 30,
          bitrate: { video: "3M", audio: "128k" },
          description: "Vertical video format for TikTok",
          category: "Social Media"
        },
        {
          platform: "facebook_feed",
          displayName: "Facebook Feed",
          maxDuration: 240,
          aspectRatio: "16:9",
          resolution: { width: 1920, height: 1080 },
          maxFileSize: 1024,
          format: "mp4",
          videoCodec: "libx264",
          audioCodec: "aac",
          framerate: 30,
          bitrate: { video: "4M", audio: "128k" },
          description: "Landscape video for Facebook feed posts",
          category: "Social Media"
        },
        {
          platform: "twitter",
          displayName: "Twitter/X",
          maxDuration: 140,
          aspectRatio: "16:9",
          resolution: { width: 1280, height: 720 },
          maxFileSize: 512,
          format: "mp4",
          videoCodec: "libx264",
          audioCodec: "aac",
          framerate: 30,
          bitrate: { video: "2M", audio: "128k" },
          description: "Video format for Twitter/X posts",
          category: "Social Media"
        },
        {
          platform: "linkedin",
          displayName: "LinkedIn",
          maxDuration: 300,
          aspectRatio: "16:9",
          resolution: { width: 1920, height: 1080 },
          maxFileSize: 1024,
          format: "mp4",
          videoCodec: "libx264",
          audioCodec: "aac",
          framerate: 30,
          bitrate: { video: "3M", audio: "128k" },
          description: "Professional video content for LinkedIn",
          category: "Professional"
        }
      ]
    };
  }

  /**
   * Reload rules from file (for when rules are updated)
   */
  async reloadRules(): Promise<PlatformRulesData> {
    this.rules = null;
    return this.loadRules();
  }

  /**
   * Get rules file info
   */
  async getRulesInfo(): Promise<{
    version: string;
    lastUpdated: string;
    totalPlatforms: number;
    filePath: string;
  }> {
    const rules = await this.loadRules();
    
    return {
      version: rules.version,
      lastUpdated: rules.lastUpdated,
      totalPlatforms: rules.platforms.length,
      filePath: this.rulesPath,
    };
  }
}

export default PlatformRulesService;
