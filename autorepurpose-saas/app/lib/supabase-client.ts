

// Supabase REST API client for database operations
class SupabaseClient {
  private baseUrl: string;
  private serviceKey: string;
  private anonKey: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    this.serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    this.anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
  }

  async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseUrl}/rest/v1/${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'apikey': this.serviceKey,
        'Authorization': `Bearer ${this.serviceKey}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Supabase API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    }
    return null;
  }

  // User operations
  async createUser(userData: {
    clerk_user_id: string;
    email: string;
    full_name?: string;
    subscription_status?: string;
  }) {
    const user = {
      clerk_user_id: userData.clerk_user_id,
      email: userData.email,
      full_name: userData.full_name || null,
      subscription_status: userData.subscription_status || 'trial',
      free_repurposings_used: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const result = await this.request('users', {
      method: 'POST',
      body: JSON.stringify(user),
    });

    return result?.[0] || result;
  }

  async upsertUser(userData: {
    clerk_user_id: string;
    email: string;
    full_name?: string;
  }) {
    // First try to find existing user
    const existingUsers = await this.request(`users?clerk_user_id=eq.${userData.clerk_user_id}&select=*`);
    
    if (existingUsers && existingUsers.length > 0) {
      // Update existing user
      const updateData = {
        email: userData.email,
        full_name: userData.full_name || null,
        updated_at: new Date().toISOString()
      };

      const result = await this.request(`users?clerk_user_id=eq.${userData.clerk_user_id}`, {
        method: 'PATCH',
        body: JSON.stringify(updateData),
      });
      return result?.[0] || existingUsers[0];
    } else {
      // Create new user
      return await this.createUser({
        clerk_user_id: userData.clerk_user_id,
        email: userData.email,
        full_name: userData.full_name,
        subscription_status: 'trial'
      });
    }
  }

  async findUserByClerkId(clerkId: string) {
    const users = await this.request(`users?clerk_user_id=eq.${clerkId}&select=*`);
    return users?.[0] || null;
  }

  async getUserStats(clerkId: string) {
    const user = await this.findUserByClerkId(clerkId);
    if (!user) return null;

    // Get job counts
    const jobs = await this.request(`video_jobs?user_id=eq.${user.id}&select=status`);
    
    const stats = {
      totalJobs: jobs?.length || 0,
      completedJobs: jobs?.filter((job: any) => job.status === 'completed').length || 0,
      processingJobs: jobs?.filter((job: any) => job.status === 'processing').length || 0,
      failedJobs: jobs?.filter((job: any) => job.status === 'failed').length || 0,
      freeRepurposingsUsed: user.free_repurposings_used || 0,
      subscriptionStatus: user.subscription_status || 'trial'
    };

    return stats;
  }

  // Video job operations
  async createVideoJob(jobData: {
    user_id: string;
    original_filename: string;
    s3_input_key: string;
    file_size_bytes: number;
    duration_seconds?: number;
    target_platforms: string[];
    job_id?: string;
  }) {
    const job = {
      id: jobData.job_id || crypto.randomUUID(),
      user_id: jobData.user_id,
      original_filename: jobData.original_filename,
      s3_input_key: jobData.s3_input_key,
      file_size_bytes: jobData.file_size_bytes,
      duration_seconds: jobData.duration_seconds || 0,
      width: 1920, // Default values - will be updated by worker
      height: 1080,
      aspect_ratio: 1.777, // 16:9 default
      form_type: 'landscape', // Default - will be updated by worker
      status: 'queued',
      selected_platforms: jobData.target_platforms, // Correct column name
      queue_job_id: jobData.job_id, // Store the job ID for queue reference
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      progress: 0
    };

    const result = await this.request('video_jobs', {
      method: 'POST',
      body: JSON.stringify(job),
    });

    return result?.[0] || result;
  }

  async getUserJobs(clerkId: string, page: number = 1, limit: number = 10, status?: string) {
    const user = await this.findUserByClerkId(clerkId);
    if (!user) return { jobs: [], totalCount: 0 };

    let query = `video_jobs?user_id=eq.${user.id}&order=created_at.desc&limit=${limit}&offset=${(page - 1) * limit}`;
    
    if (status) {
      query += `&status=eq.${status}`;
    }

    const jobs = await this.request(query + '&select=*');
    
    // Get total count
    const countQuery = `video_jobs?user_id=eq.${user.id}${status ? `&status=eq.${status}` : ''}&select=id`;
    const countResult = await this.request(countQuery, {
      headers: { 'Prefer': 'count=exact' }
    });
    
    return {
      jobs: jobs || [],
      totalCount: Array.isArray(countResult) ? countResult.length : 0
    };
  }

  async updateJobStatus(jobId: string, status: string, additionalData?: any) {
    const updateData = {
      status,
      updated_at: new Date().toISOString(),
      ...additionalData
    };

    const result = await this.request(`video_jobs?id=eq.${jobId}`, {
      method: 'PATCH',
      body: JSON.stringify(updateData),
    });

    return result?.[0] || result;
  }

  async deleteVideoJob(jobId: string) {
    const result = await this.request(`video_jobs?id=eq.${jobId}`, {
      method: 'DELETE',
    });

    return result;
  }

  async markStaleJobsAsFailed() {
    // Mark jobs older than 10 minutes that are still queued as failed
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString();
    
    const result = await this.request(
      `video_jobs?status=eq.queued&created_at=lt.${tenMinutesAgo}`, 
      {
        method: 'PATCH',
        body: JSON.stringify({
          status: 'failed',
          error_message: 'Job timed out - not processed within 10 minutes',
          updated_at: new Date().toISOString(),
        }),
      }
    );

    return result;
  }

  async getJobById(jobId: string) {
    const jobs = await this.request(`video_jobs?id=eq.${jobId}&select=*`);
    return jobs?.[0] || null;
  }

  async createPlatformOutput(outputData: any) {
    const result = await this.request('platform_outputs', {
      method: 'POST',
      body: JSON.stringify(outputData),
    });

    return result?.[0] || result;
  }

  async getPlatformOutputs(jobId: string) {
    const outputs = await this.request(`platform_outputs?job_id=eq.${jobId}&select=*`);
    return outputs || [];
  }

  // Test operations
  async testConnection() {
    try {
      const result = await this.request('users?select=count', {
        headers: { 'Prefer': 'count=exact' }
      });
      return { success: true, message: 'Connection successful' };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  async getUserCount() {
    try {
      const response = await fetch(`${this.baseUrl}/rest/v1/users?select=count`, {
        headers: {
          'apikey': this.serviceKey,
          'Authorization': `Bearer ${this.serviceKey}`,
          'Prefer': 'count=exact'
        }
      });
      
      const countHeader = response.headers.get('content-range');
      return countHeader ? parseInt(countHeader.split('/')[1]) || 0 : 0;
    } catch (error) {
      return 0;
    }
  }
}

export const supabaseClient = new SupabaseClient();
