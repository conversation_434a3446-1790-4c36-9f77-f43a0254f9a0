
// Supabase client configuration
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Client for frontend/browser use
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Service client for server-side operations (full access)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Types for database tables
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          clerk_user_id: string
          email: string
          full_name: string | null
          subscription_status: string
          subscription_id: string | null
          current_period_end: string | null
          free_repurposings_used: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          clerk_user_id: string
          email: string
          full_name?: string | null
          subscription_status?: string
          subscription_id?: string | null
          current_period_end?: string | null
          free_repurposings_used?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          clerk_user_id?: string
          email?: string
          full_name?: string | null
          subscription_status?: string
          subscription_id?: string | null
          current_period_end?: string | null
          free_repurposings_used?: number
          created_at?: string
          updated_at?: string
        }
      }
      video_jobs: {
        Row: {
          id: string
          user_id: string
          original_filename: string
          s3_input_key: string
          file_size_bytes: number
          duration_seconds: number
          width: number
          height: number
          aspect_ratio: number
          form_type: string
          status: string
          error_message: string | null
          selected_platforms: any
          queue_job_id: string | null
          processing_started_at: string | null
          processing_completed_at: string | null
          expires_at: string
          created_at: string
          updated_at: string
        }
      }
      // Add other table types as needed
    }
  }
}

// Helper types for database operations
export type UserRow = Database['public']['Tables']['users']['Row']
export type UserInsert = Database['public']['Tables']['users']['Insert']  
export type UserUpdate = Database['public']['Tables']['users']['Update']

export type VideoJobRow = Database['public']['Tables']['video_jobs']['Row']
