
import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

// Payment provider types
export type PaymentProvider = 'stripe' | 'razorpay';

export interface PaymentConfig {
  provider: PaymentProvider;
  amount: number;
  currency: string;
  planName: string;
}

// Payment configurations
export const PAYMENT_CONFIGS: Record<PaymentProvider, PaymentConfig> = {
  stripe: {
    provider: 'stripe',
    amount: 3000, // $30.00 in cents
    currency: 'USD',
    planName: 'AutoRepurpose Monthly (USD)'
  },
  razorpay: {
    provider: 'razorpay',
    amount: 250000, // ₹2,500 in paise
    currency: 'INR',
    planName: 'AutoRepurpose Monthly (INR)'
  }
};

// Stripe payment functions
export async function createStripeCheckoutSession(userId: string) {
  try {
    const response = await fetch('/api/payments/stripe/create-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID,
        successUrl: `${window.location.origin}/dashboard?payment=success`,
        cancelUrl: `${window.location.origin}/pricing?payment=cancelled`,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to create checkout session');
    }

    const { sessionId } = await response.json();
    
    const stripe = await stripePromise;
    if (!stripe) {
      throw new Error('Stripe failed to load');
    }

    const { error } = await stripe.redirectToCheckout({ sessionId });
    
    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Stripe payment error:', error);
    throw error;
  }
}

// Razorpay payment functions
export async function createRazorpayOrder(userId: string) {
  try {
    const response = await fetch('/api/payments/razorpay/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });

    if (!response.ok) {
      throw new Error('Failed to create Razorpay order');
    }

    return await response.json();
  } catch (error) {
    console.error('Razorpay order creation error:', error);
    throw error;
  }
}

export async function openRazorpayCheckout(orderData: any) {
  return new Promise((resolve, reject) => {
    // Load Razorpay script
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.async = true;
    
    script.onload = () => {
      const options = {
        key: orderData.key,
        amount: orderData.amount,
        currency: orderData.currency,
        name: 'AutoRepurpose',
        description: 'Monthly Subscription',
        order_id: orderData.orderId,
        prefill: {
          name: orderData.user.name,
          email: orderData.user.email,
        },
        theme: {
          color: '#3b82f6',
        },
        handler: async (response: any) => {
          try {
            const verifyResponse = await fetch('/api/payments/razorpay/verify', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature,
              }),
            });

            if (!verifyResponse.ok) {
              throw new Error('Payment verification failed');
            }

            const result = await verifyResponse.json();
            resolve(result);
          } catch (error) {
            reject(error);
          }
        },
        modal: {
          ondismiss: () => {
            reject(new Error('Payment cancelled by user'));
          },
        },
      };

      // @ts-ignore
      const rzp = new window.Razorpay(options);
      rzp.open();
    };

    script.onerror = () => {
      reject(new Error('Failed to load Razorpay'));
    };

    document.head.appendChild(script);
  });
}

// Unified payment function
export async function processPayment(provider: PaymentProvider, userId: string) {
  try {
    switch (provider) {
      case 'stripe':
        await createStripeCheckoutSession(userId);
        break;
        
      case 'razorpay':
        const orderData = await createRazorpayOrder(userId);
        await openRazorpayCheckout(orderData);
        break;
        
      default:
        throw new Error(`Unsupported payment provider: ${provider}`);
    }
  } catch (error) {
    console.error(`${provider} payment error:`, error);
    throw error;
  }
}

// Helper function to get payment config
export function getPaymentConfig(provider: PaymentProvider): PaymentConfig {
  return PAYMENT_CONFIGS[provider];
}

// Helper function to format currency
export function formatCurrency(amount: number, currency: string): string {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  });
  
  // Convert cents/paise to main currency unit
  const mainAmount = currency.toLowerCase() === 'inr' ? amount / 100 : amount / 100;
  
  return formatter.format(mainAmount);
}

// Helper function to detect user's preferred payment provider based on location
export function getPreferredPaymentProvider(): PaymentProvider {
  // Simple heuristic - can be enhanced with proper geolocation
  const userLang = navigator.language || 'en-US';
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  
  // If user is likely in India, prefer Razorpay
  if (userLang.includes('hi') || timezone.includes('Kolkata') || timezone.includes('Mumbai')) {
    return 'razorpay';
  }
  
  // Default to Stripe for international users
  return 'stripe';
}

// Helper function to validate subscription status
export interface SubscriptionStatus {
  isActive: boolean;
  provider?: PaymentProvider;
  expiresAt?: Date;
  subscriptionId?: string;
}

export async function getSubscriptionStatus(userId: string): Promise<SubscriptionStatus> {
  try {
    const response = await fetch('/api/payments/subscription', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch subscription status');
    }

    return await response.json();
  } catch (error) {
    console.error('Subscription status error:', error);
    return { isActive: false };
  }
}
