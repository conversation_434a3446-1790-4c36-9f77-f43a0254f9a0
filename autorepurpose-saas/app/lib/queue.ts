
import { Queue, Worker } from 'bullmq';
import Redis from 'ioredis';

// Redis connection configuration for Upstash (TCP)
const redis = new Redis(process.env.REDIS_URL!, {
  maxRetriesPerRequest: null, // Required for BullMQ
  enableReadyCheck: false,
  lazyConnect: true,
});

// Create job queue
const videoProcessingQueue = new Queue('video-processing', {
  connection: redis,
  defaultJobOptions: {
    removeOnComplete: 10,
    removeOnFail: 5,
    delay: 1000, // 1 second delay before processing
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 5000,
    },
  },
});

export interface VideoProcessingJobData {
  jobId: string;
  userId: string;
  s3Key: string;
  platforms: string[];
  originalFileName: string;
}

export class JobQueueService {
  static async addVideoProcessingJob(data: VideoProcessingJobData) {
    try {
      // Load platform rules from the uploaded file
      const platformRulesData = await JobQueueService.loadPlatformRules();
      
      // Transform data to match worker expectations
      const workerJobData = {
        jobId: data.jobId,
        inputS3Key: data.s3Key,           // Fix field name
        selectedPlatforms: data.platforms, // Fix field name
        platformRules: platformRulesData,  // Add missing platform rules
        userId: data.userId,              // Keep for context
        originalFileName: data.originalFileName // Keep for context
      };

      const job = await videoProcessingQueue.add(
        'process-video',
        workerJobData,
        {
          jobId: data.jobId, // Use the job ID as the Bull job ID
          delay: 2000, // 2 second delay to allow S3 upload to complete
        }
      );

      console.log(`Added video processing job: ${job.id}`);
      return job;
    } catch (error) {
      console.error('Error adding job to queue:', error);
      throw error;
    }
  }

  static async loadPlatformRules() {
    try {
      // Load platform rules from the project public folder
      const fs = await import('fs');
      const path = await import('path');
      
      const rulesPath = path.join(process.cwd(), 'public', 'video_platforms_rules_updated.json');
      const rulesContent = await fs.promises.readFile(rulesPath, 'utf8');
      const rulesData = JSON.parse(rulesContent);
      
      console.log(`Loaded platform rules: ${Object.keys(rulesData.platforms).length} platforms`);
      return rulesData.platforms;
    } catch (error) {
      console.error('Error loading platform rules:', error);
      // Return default minimal rules as fallback
      return {
        instagram_reel: {
          name: "Instagram Reel",
          output_preset: {
            resolution: { width: 1080, height: 1920 },
            video_codec: "libx264",
            audio_codec: "aac",
            bitrate: { video: "3000k", audio: "128k" },
            frame_rate: 30,
            max_duration_seconds: 90,
            fit_strategy: "scale_crop"
          },
          optimization: {
            two_pass_encoding: false,
            hardware_acceleration: true
          }
        },
        twitter: {
          name: "Twitter/X",
          output_preset: {
            resolution: { width: 1280, height: 720 },
            video_codec: "libx264", 
            audio_codec: "aac",
            bitrate: { video: "2000k", audio: "128k" },
            frame_rate: 30,
            max_duration_seconds: 140,
            fit_strategy: "scale_crop"
          },
          optimization: {
            two_pass_encoding: false,
            hardware_acceleration: true
          }
        }
      };
    }
  }

  static async getJobStatus(jobId: string) {
    try {
      const job = await videoProcessingQueue.getJob(jobId);
      if (!job) {
        return null;
      }

      return {
        id: job.id,
        status: await job.getState(),
        progress: job.progress,
        data: job.data,
        processedOn: job.processedOn,
        finishedOn: job.finishedOn,
        failedReason: job.failedReason,
      };
    } catch (error) {
      console.error('Error getting job status:', error);
      return null;
    }
  }

  static async removeJob(jobId: string) {
    try {
      const job = await videoProcessingQueue.getJob(jobId);
      if (job) {
        await job.remove();
      }
    } catch (error) {
      console.error('Error removing job:', error);
    }
  }

  static getQueue() {
    return videoProcessingQueue;
  }

  static async getQueueStats() {
    try {
      const waiting = await videoProcessingQueue.getWaiting();
      const active = await videoProcessingQueue.getActive();
      const completed = await videoProcessingQueue.getCompleted();
      const failed = await videoProcessingQueue.getFailed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
      };
    } catch (error) {
      console.error('Error getting queue stats:', error);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
      };
    }
  }
}

// Export for use in other parts of the application
export { videoProcessingQueue, redis };
