
-- AutoRepurpose SaaS - Payment Integration Migration
-- Run this in your Supabase SQL Editor AFTER the initial schema

-- Add missing payment-related columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS stripe_customer_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS razorpay_customer_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS preferred_payment_provider VARCHAR(20) DEFAULT 'razorpay';

-- Add missing columns to video_jobs table 
ALTER TABLE video_jobs 
ADD COLUMN IF NOT EXISTS progress INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS download_url TEXT;

-- Add missing columns to video_outputs table
ALTER TABLE video_outputs 
ADD COLUMN IF NOT EXISTS download_url TEXT,
ADD COLUMN IF NOT EXISTS duration DECIMAL(10, 2);

-- Update payment_transactions table to support both Stripe and Razorpay
ALTER TABLE payment_transactions 
ADD COLUMN IF NOT EXISTS provider VARCHAR(20) DEFAULT 'razorpay',
ADD COLUMN IF NOT EXISTS razorpay_payment_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS razorpay_order_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS razorpay_subscription_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS razorpay_customer_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS stripe_customer_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS provider_transaction_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS metadata JSONB;

-- Create additional indexes for payment queries
CREATE INDEX IF NOT EXISTS idx_payment_transactions_provider ON payment_transactions(provider);
CREATE INDEX IF NOT EXISTS idx_users_preferred_payment ON users(preferred_payment_provider);
CREATE INDEX IF NOT EXISTS idx_video_jobs_progress ON video_jobs(progress);

-- Update RLS policies for new payment fields
DROP POLICY IF EXISTS "Users can update own payment data" ON users;
CREATE POLICY "Users can update own payment data" ON users
    FOR UPDATE USING (auth.uid()::text = clerk_user_id)
    WITH CHECK (auth.uid()::text = clerk_user_id);

-- Success message
SELECT 'Payment integration migration completed successfully!' as message;
