
-- AutoRepurpose SaaS Database Schema
-- Run this in your Supabase SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (synced with Clerk)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clerk_user_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(255),
    subscription_status VARCHAR(50) NOT NULL DEFAULT 'trial',
    subscription_id VARCHAR(255),
    current_period_end TIMESTAMPTZ,
    free_repurposings_used INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Video jobs table
CREATE TABLE IF NOT EXISTS video_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    original_filename VARCHAR(500) NOT NULL,
    s3_input_key VARCHAR(1000) NOT NULL,
    file_size_bytes BIGINT NOT NULL,
    duration_seconds DECIMAL(10, 2) NOT NULL,
    width INTEGER NOT NULL,
    height INTEGER NOT NULL,
    aspect_ratio DECIMAL(10, 6) NOT NULL,
    form_type VARCHAR(20) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    error_message TEXT,
    selected_platforms JSONB NOT NULL,
    queue_job_id VARCHAR(255),
    processing_started_at TIMESTAMPTZ,
    processing_completed_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '3 days'),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Video outputs table (one per platform conversion)
CREATE TABLE IF NOT EXISTS video_outputs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES video_jobs(id) ON DELETE CASCADE,
    platform_id VARCHAR(50) NOT NULL,
    preset_name VARCHAR(100) NOT NULL,
    s3_output_key VARCHAR(1000) NOT NULL,
    filename VARCHAR(500) NOT NULL,
    file_size_bytes BIGINT,
    resolution VARCHAR(20) NOT NULL,
    bitrate VARCHAR(20),
    video_codec VARCHAR(20),
    audio_codec VARCHAR(20),
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    error_message TEXT,
    processing_time_seconds INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Platform rules cache table (for performance)
CREATE TABLE IF NOT EXISTS platform_rules_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rules_version VARCHAR(50) NOT NULL,
    rules_data JSONB NOT NULL,
    checksum VARCHAR(64) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Payment transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    stripe_payment_intent_id VARCHAR(255),
    stripe_subscription_id VARCHAR(255),
    amount_cents INTEGER NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'usd',
    status VARCHAR(50) NOT NULL,
    transaction_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Usage tracking table
CREATE TABLE IF NOT EXISTS usage_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    job_id UUID REFERENCES video_jobs(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_clerk_id ON users(clerk_user_id);
CREATE INDEX IF NOT EXISTS idx_users_subscription ON users(subscription_status);
CREATE INDEX IF NOT EXISTS idx_video_jobs_user_id ON video_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_video_jobs_status ON video_jobs(status);
CREATE INDEX IF NOT EXISTS idx_video_jobs_expires_at ON video_jobs(expires_at);
CREATE INDEX IF NOT EXISTS idx_video_outputs_job_id ON video_outputs(job_id);
CREATE INDEX IF NOT EXISTS idx_video_outputs_status ON video_outputs(status);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_user ON payment_transactions(user_id);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_jobs_updated_at BEFORE UPDATE ON video_jobs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_outputs_updated_at BEFORE UPDATE ON video_outputs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_outputs ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_logs ENABLE ROW LEVEL SECURITY;

-- Policies for users table
CREATE POLICY "Users can view own data" ON users
    FOR SELECT USING (auth.uid()::text = clerk_user_id);

CREATE POLICY "Users can update own data" ON users
    FOR UPDATE USING (auth.uid()::text = clerk_user_id);

-- Policies for video_jobs table
CREATE POLICY "Users can view own video jobs" ON video_jobs
    FOR SELECT USING (user_id IN (
        SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ));

CREATE POLICY "Users can insert own video jobs" ON video_jobs
    FOR INSERT WITH CHECK (user_id IN (
        SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ));

CREATE POLICY "Users can update own video jobs" ON video_jobs
    FOR UPDATE USING (user_id IN (
        SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ));

-- Policies for video_outputs table
CREATE POLICY "Users can view own video outputs" ON video_outputs
    FOR SELECT USING (job_id IN (
        SELECT video_jobs.id FROM video_jobs 
        JOIN users ON video_jobs.user_id = users.id 
        WHERE users.clerk_user_id = auth.uid()::text
    ));

-- Policies for payment_transactions table
CREATE POLICY "Users can view own payment transactions" ON payment_transactions
    FOR SELECT USING (user_id IN (
        SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ));

-- Policies for usage_logs table
CREATE POLICY "Users can view own usage logs" ON usage_logs
    FOR SELECT USING (user_id IN (
        SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ));

-- Platform rules cache is read-only for authenticated users
CREATE POLICY "Authenticated users can read platform rules" ON platform_rules_cache
    FOR SELECT TO authenticated USING (is_active = true);

-- Service role can do everything (for backend operations)
CREATE POLICY "Service role full access" ON users FOR ALL TO service_role USING (true);
CREATE POLICY "Service role full access" ON video_jobs FOR ALL TO service_role USING (true);
CREATE POLICY "Service role full access" ON video_outputs FOR ALL TO service_role USING (true);
CREATE POLICY "Service role full access" ON payment_transactions FOR ALL TO service_role USING (true);
CREATE POLICY "Service role full access" ON usage_logs FOR ALL TO service_role USING (true);
CREATE POLICY "Service role full access" ON platform_rules_cache FOR ALL TO service_role USING (true);

-- Insert initial platform rules
INSERT INTO platform_rules_cache (rules_version, rules_data, checksum, is_active)
VALUES ('2.0', '{
    "version": "2.0",
    "updated_at": "2025-08-16T00:00:00Z",
    "classification_rules": {
        "short_form": {
            "max_duration_seconds": 120,
            "min_aspect_ratio": 1.6
        }
    },
    "platforms": {}
}'::jsonb, 'placeholder', true)
ON CONFLICT DO NOTHING;

-- Success message
SELECT 'AutoRepurpose database schema created successfully!' as message;
