// Database seeding script
import { PrismaClient } from '@prisma/client';
import { readFileSync } from 'fs';
import { join } from 'path';
import crypto from 'crypto';

const prisma = new PrismaClient();

async function seed() {
  console.log('Starting database seeding...');

  try {
    // Load platform rules from JSON file
    const rulesPath = join(process.cwd(), 'data', 'video_platforms_rules_updated.json');
    const rulesData = JSON.parse(readFileSync(rulesPath, 'utf-8'));

    // Calculate checksum for the rules
    const checksum = crypto.createHash('sha256')
      .update(JSON.stringify(rulesData))
      .digest('hex');

    // Check if rules already exist with this checksum
    const existingRules = await prisma.platformRulesCache.findFirst({
      where: { checksum, isActive: true }
    });

    if (!existingRules) {
      // Deactivate old rules
      await prisma.platformRulesCache.updateMany({
        where: { isActive: true },
        data: { isActive: false }
      });

      // Insert new rules
      await prisma.platformRulesCache.create({
        data: {
          rulesVersion: rulesData.metadata.version,
          rulesData: rulesData,
          checksum: checksum,
          isActive: true
        }
      });

      console.log('✓ Platform rules seeded successfully');
    } else {
      console.log('✓ Platform rules already up to date');
    }

    console.log('Database seeding completed successfully!');

  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seed().catch((error) => {
  console.error('Seeding failed:', error);
  process.exit(1);
});
