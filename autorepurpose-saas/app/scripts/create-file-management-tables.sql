
-- Download tracking table
CREATE TABLE IF NOT EXISTS download_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  video_output_id UUID NOT NULL REFERENCES video_outputs(id) ON DELETE CASCADE,
  job_id UUID NOT NULL REFERENCES video_jobs(id) ON DELETE CASCADE,
  downloaded_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Cleanup logs table
CREATE TABLE IF NOT EXISTS cleanup_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cleanup_type TEXT NOT NULL, -- 'expired_files', 'failed_jobs', etc.
  files_found INTEGER NOT NULL DEFAULT 0,
  files_deleted INTEGER NOT NULL DEFAULT 0,
  files_failed INTEGER NOT NULL DEFAULT 0,
  cleanup_date TIMESTAMPTZ NOT NULL DEFAULT now(),
  details JSONB, -- Store additional cleanup details
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add additional columns to video_outputs table if they don't exist
DO $$ 
BEGIN
  -- Add download tracking columns
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_outputs' AND column_name = 'download_count') THEN
    ALTER TABLE video_outputs ADD COLUMN download_count INTEGER NOT NULL DEFAULT 0;
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_outputs' AND column_name = 'last_downloaded_at') THEN
    ALTER TABLE video_outputs ADD COLUMN last_downloaded_at TIMESTAMPTZ;
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_outputs' AND column_name = 'deleted_at') THEN
    ALTER TABLE video_outputs ADD COLUMN deleted_at TIMESTAMPTZ;
  END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_download_logs_user_id ON download_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_download_logs_downloaded_at ON download_logs(downloaded_at DESC);
CREATE INDEX IF NOT EXISTS idx_video_outputs_download_count ON video_outputs(download_count DESC);
CREATE INDEX IF NOT EXISTS idx_video_outputs_created_at ON video_outputs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_cleanup_logs_cleanup_date ON cleanup_logs(cleanup_date DESC);

-- Row Level Security (RLS) policies
ALTER TABLE download_logs ENABLE ROW LEVEL SECURITY;

-- Users can only see their own download logs
CREATE POLICY IF NOT EXISTS "Users can view own download logs" ON download_logs
  FOR SELECT USING (user_id = current_user);

-- Allow service to insert download logs
CREATE POLICY IF NOT EXISTS "Service can insert download logs" ON download_logs
  FOR INSERT WITH CHECK (true);

-- Cleanup logs are only accessible by service/admin
ALTER TABLE cleanup_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY IF NOT EXISTS "Service can manage cleanup logs" ON cleanup_logs
  FOR ALL USING (true);

-- Grant permissions
GRANT SELECT, INSERT ON download_logs TO authenticated;
GRANT SELECT ON cleanup_logs TO authenticated;
GRANT ALL ON cleanup_logs TO service_role;
