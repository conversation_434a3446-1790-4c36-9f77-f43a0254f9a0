
# File Management Setup Guide

## 📋 **Database Setup Required**

You need to run the SQL script to create the necessary database tables for file management.

### **Step 1: Run Database Migration**

1. **Go to your Supabase project dashboard**
2. **Navigate to SQL Editor**
3. **Copy and paste the contents of** `scripts/create-file-management-tables.sql`
4. **Run the script** to create:
   - `download_logs` table (track user downloads)
   - `cleanup_logs` table (track cleanup operations)
   - Additional columns for `video_outputs` table
   - Proper indexes and RLS policies

### **Step 2: Environment Variables**

Add these to your `.env` file:

```env
# File Cleanup Configuration
CLEANUP_SECRET_TOKEN="your-secure-cleanup-token-here"
CRON_SECRET="your-secure-cron-secret-here"

# File Management Settings  
MAX_FILE_SIZE_GB="5"
FILE_RETENTION_DAYS="3"
```

### **Step 3: Automated Cleanup Setup**

For production, set up automated file cleanup using one of these methods:

#### **Option A: Vercel Cron (Recommended for Vercel deployment)**
Create `vercel.json`:
```json
{
  "crons": [
    {
      "path": "/api/cleanup/cron",
      "schedule": "0 2 * * *"
    }
  ]
}
```

#### **Option B: GitHub Actions (For any deployment)**
Create `.github/workflows/cleanup.yml`:
```yaml
name: File Cleanup
on:
  schedule:
    - cron: '0 2 * * *'  # 2 AM UTC daily
jobs:
  cleanup:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Cleanup
        run: |
          curl -X POST "${{ secrets.APP_URL }}/api/cleanup/cron" \
            -H "X-Cron-Secret: ${{ secrets.CRON_SECRET }}"
```

#### **Option C: External Cron Service**
Use services like:
- **cron-job.org**
- **EasyCron**
- **Zapier scheduled workflows**

Call: `POST https://yourapp.com/api/cleanup/cron`
Header: `X-Cron-Secret: your-secret`

### **Step 4: Manual Cleanup (For Testing)**

You can manually trigger cleanup:
```bash
curl -X POST "https://yourapp.com/api/files/cleanup" \
  -H "Authorization: Bearer your-cleanup-token"
```

## ✅ **Features Included**

### **🔐 Secure File Downloads**
- User authentication required
- Access control (users can only download their files)
- Signed S3 URLs (5-minute expiry)
- Download tracking and analytics

### **📊 File Management Dashboard**
- View all processed files organized by project
- Filter by status (completed, failed, processing)
- Real-time file expiration countdown
- Download statistics and file analytics

### **🧹 Automatic Cleanup**
- Files automatically deleted after 3 days
- S3 cleanup with database sync
- Cleanup activity logging
- Configurable retention period

### **📈 File Analytics**
- Download counts per file
- Storage usage tracking
- Processing success rates
- File expiration monitoring

## 🚀 **Ready to Use**

Once you've completed the database setup, the file management system is fully functional:

1. ✅ **Users can view their files** at `/dashboard/files`
2. ✅ **Secure downloads** work immediately 
3. ✅ **Analytics** show file statistics
4. ✅ **Cleanup** can be triggered manually or automated

The system integrates seamlessly with your existing video processing pipeline!
