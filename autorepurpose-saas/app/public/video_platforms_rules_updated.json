{"metadata": {"created_date": "2025-08-02", "version": "2.0", "description": "Comprehensive video platform rules and specifications for form-type specific video repurposing platform", "last_updated": "2025-08-09", "source": "Official platform documentation and comprehensive research", "form_type_support": true, "repurposing_mode": "same_form_only"}, "form_classification": {"logic": {"short_form": {"duration_seconds": "≤ 120", "aspect_ratio_height_width": "≥ 1.6", "description": "Vertical mobile-first content optimized for quick consumption"}, "long_form": {"criteria": "All content that doesn't meet short_form criteria", "description": "Traditional horizontal or longer content formats"}}, "validation_rules": {"classification_function": "classify_video_form(duration_seconds, height, width)", "same_form_enforcement": true, "cross_form_conversion": false}}, "platforms": {"tiktok": {"name": "TikTok", "form_types": {"short_form": {"technical_specifications": {"aspect_ratios": {"recommended": "9:16", "supported": ["9:16"], "notes": "9:16 strongly recommended for full-screen experience"}, "resolution": {"minimum": "540x960", "recommended": "1080x1920", "maximum": "1080x1920"}, "file_formats": {"primary": ["MP4", "MOV"], "supported": ["MP4", "MOV", "MPEG", "3GP", "AVI", "WebM"], "recommended": "MP4"}, "duration": {"minimum": "1 second", "maximum": "120 seconds", "recommended": "15-60 seconds", "form_classification_max": 120}, "file_size": {"maximum": "500 MB"}, "video_codec": "H.264", "audio_codec": "AAC", "frame_rate": "30 FPS", "bitrate": {"minimum": "516 kbps", "recommended": "2500 kbps"}}, "conversion_presets": {"quality_high": {"resolution": "1080x1920", "bitrate": "2500 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}, "quality_medium": {"resolution": "720x1280", "bitrate": "1500 kbps", "frame_rate": "30", "audio_bitrate": "96 kbps", "video_codec": "H.264", "audio_codec": "AAC"}, "quality_low": {"resolution": "540x960", "bitrate": "800 kbps", "frame_rate": "30", "audio_bitrate": "64 kbps", "video_codec": "H.264", "audio_codec": "AAC"}}}}, "content_policies": {"community_guidelines": {"focus": ["harm prevention", "civility", "privacy protection"], "prohibited": ["misleading visuals", "hate speech", "harassment", "private info sharing"]}, "caption_limits": {"non_spark_ads": "100 characters", "emojis_supported": true, "clickable_links_restricted": true}, "audio_requirements": {"commercial_music_library": "recommended", "copyright_enforcement": "strict", "audio_quality": "clear, engaging, narrative-enhancing"}}, "safe_zones": {"required": true, "varies_by_region": true, "top_margin": "12%", "bottom_margin": "20%", "description": "Areas where text/logos should be placed to avoid UI obstruction"}}, "instagram": {"name": "Instagram", "form_types": {"short_form": {"technical_specifications": {"aspect_ratios": {"recommended": "9:16", "supported": ["9:16"], "notes": "Reels format optimized for mobile viewing"}, "resolution": {"recommended": "1080x1920", "minimum": "720x1280"}, "file_formats": {"supported": ["MP4", "MOV"], "recommended": "MP4"}, "duration": {"minimum": "1 second", "maximum": "90 seconds", "recommended": "15-60 seconds", "form_classification_max": 90}, "file_size": {"maximum": "4 GB"}, "video_codec": "H.264", "audio_codec": "AAC", "frame_rate": "30 FPS"}, "conversion_presets": {"quality_high": {"resolution": "1080x1920", "bitrate": "3000 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}, "quality_medium": {"resolution": "1080x1920", "bitrate": "2000 kbps", "frame_rate": "30", "audio_bitrate": "96 kbps", "video_codec": "H.264", "audio_codec": "AAC"}}}, "long_form": {"technical_specifications": {"aspect_ratios": {"supported": ["9:16", "1:1", "4:5"], "recommended": ["1:1", "4:5"]}, "resolution": {"recommended": "1080x1080", "minimum": "600x600"}, "duration": {"minimum": "3 seconds", "maximum": "60 minutes"}, "file_size": {"maximum": "4 GB"}, "video_codec": "H.264", "audio_codec": "AAC", "frame_rate": "30 FPS"}, "conversion_presets": {"square_feed": {"resolution": "1080x1080", "bitrate": "2500 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}, "portrait_feed": {"resolution": "1080x1350", "bitrate": "2500 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}}}}, "content_policies": {"community_standards": "Meta Community Standards", "prohibited": ["hate speech", "bullying", "graphic violence", "misinformation", "spam"], "authenticity": "inauthentic behavior strictly forbidden", "branded_content": {"disclosure_required": true, "paid_partnership_label": "mandatory for commercial partnerships"}}}, "youtube": {"name": "YouTube", "form_types": {"short_form": {"technical_specifications": {"aspect_ratios": {"supported": ["9:16", "1:1"], "recommended": "9:16"}, "resolution": {"recommended": "1080x1920", "minimum": "720x1280"}, "duration": {"maximum": "60 seconds", "recommended": "15-60 seconds", "form_classification_max": 60}, "file_formats": {"supported": ["MP4", "MOV", "AVI", "WMV", "FLV"], "recommended": "MP4"}, "video_codec": "H.264", "audio_codec": "AAC", "frame_rate": "24-60 FPS"}, "conversion_presets": {"shorts_high": {"resolution": "1080x1920", "bitrate": "4000 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}, "shorts_medium": {"resolution": "720x1280", "bitrate": "2000 kbps", "frame_rate": "30", "audio_bitrate": "96 kbps", "video_codec": "H.264", "audio_codec": "AAC"}}}, "long_form": {"technical_specifications": {"aspect_ratios": {"recommended": "16:9", "supported": ["16:9", "4:3", "1:1"]}, "resolution": {"recommended": "1920x1080", "supported_range": "240p to 8K"}, "duration": {"maximum": "12 hours", "minimum": "121 seconds"}, "file_size": {"maximum": "256 GB"}, "video_codec": "H.264", "audio_codec": "AAC", "frame_rate": "24-60 FPS"}, "conversion_presets": {"youtube_1080p": {"resolution": "1920x1080", "bitrate": "8000 kbps", "frame_rate": "30", "audio_bitrate": "192 kbps", "video_codec": "H.264", "audio_codec": "AAC"}, "youtube_720p": {"resolution": "1280x720", "bitrate": "5000 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}, "youtube_4k": {"resolution": "3840x2160", "bitrate": "35000 kbps", "frame_rate": "30", "audio_bitrate": "192 kbps", "video_codec": "H.264", "audio_codec": "AAC"}}}}, "content_policies": {"community_guidelines": ["no spam", "no deceptive practices", "no sensitive content", "no violence", "no misinformation"], "strikes_system": true, "channel_termination": "for repeat offenders"}}, "x_twitter": {"name": "X (formerly Twitter)", "form_types": {"short_form": {"technical_specifications": {"aspect_ratios": {"supported": ["9:16", "1:1"], "range": "1:2.39 to 2.39:1"}, "resolution": {"maximum": "1200x1900", "recommended": "1080x1920"}, "duration": {"maximum": "140 seconds", "form_classification_max": 120}, "file_formats": ["MP4", "MOV"], "video_codec": "H.264", "audio_codec": "AAC", "frame_rate": "40 FPS maximum", "file_size": {"standard_users": "512 MB maximum"}}, "conversion_presets": {"twitter_vertical": {"resolution": "1080x1920", "bitrate": "6000 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}, "twitter_square": {"resolution": "1080x1080", "bitrate": "6000 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}}}, "long_form": {"technical_specifications": {"aspect_ratios": {"supported": ["16:9", "4:3", "1:1"], "range": "1:2.39 to 2.39:1"}, "resolution": {"maximum": "1920x1200", "recommended": "1920x1080"}, "duration": {"standard_users": "140 seconds maximum", "premium_users": "3 hours (720p) or 2 hours (1080p)"}, "file_size": {"standard_users": "512 MB maximum", "premium_users": "8 GB maximum"}, "video_codec": "H.264", "audio_codec": "AAC", "frame_rate": "40 FPS maximum"}, "conversion_presets": {"twitter_landscape": {"resolution": "1920x1080", "bitrate": "8000 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}}}}}, "facebook": {"name": "Facebook", "form_types": {"short_form": {"technical_specifications": {"aspect_ratios": {"recommended": "9:16", "supported": ["9:16"]}, "resolution": {"recommended": "1080x1920"}, "duration": {"recommended": "15-60 seconds", "maximum": "120 seconds"}, "file_formats": ["MP4", "MOV", "AVI", "WMV"], "video_codec": "H.264", "audio_codec": "AAC"}, "conversion_presets": {"facebook_reels": {"resolution": "1080x1920", "bitrate": "4000 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}}}, "long_form": {"technical_specifications": {"aspect_ratios": {"supported": ["16:9", "1:1", "4:5"], "recommended": ["1:1", "4:5"]}, "duration": {"maximum": "240 minutes"}, "file_size": {"maximum": "10 GB"}, "file_formats": ["MP4", "MOV", "AVI", "WMV"], "video_codec": "H.264", "audio_codec": "AAC"}, "conversion_presets": {"facebook_feed": {"resolution": "1080x1080", "bitrate": "4000 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}}}}}, "linkedin": {"name": "LinkedIn", "form_types": {"long_form": {"technical_specifications": {"aspect_ratios": {"range": "1:2.4 to 2.4:1", "recommended": ["4:5", "9:16", "1:1", "16:9"]}, "resolution": {"minimum": "256x144", "maximum": "4096x2304", "recommended": "1920x1080"}, "duration": {"maximum": "10 minutes", "ads_max": "30 minutes"}, "file_size": {"general": "5 GB maximum", "ads": "200-500 MB maximum"}, "file_formats": {"general": ["MP4", "MOV", "AVI", "MKV"], "ads": "MP4 only"}}, "conversion_presets": {"linkedin_professional": {"resolution": "1920x1080", "bitrate": "5000 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}, "linkedin_square": {"resolution": "1080x1080", "bitrate": "4000 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}}}}}, "snapchat": {"name": "Snapchat", "form_types": {"short_form": {"technical_specifications": {"aspect_ratios": {"required": "9:16"}, "resolution": {"recommended": "1080x1920"}, "duration": {"organic": "3-10 seconds", "ads": "up to 180 seconds", "form_classification_max": 120}, "file_size": {"organic": "32 MB maximum", "ads": "1 GB maximum"}, "file_formats": ["MP4", "MOV"], "video_codec": "H.264", "audio_codec": "AAC", "frame_rate": "30 FPS"}, "conversion_presets": {"snapchat_story": {"resolution": "1080x1920", "bitrate": "3000 kbps", "frame_rate": "30", "audio_bitrate": "128 kbps", "video_codec": "H.264", "audio_codec": "AAC"}}}}}}, "form_compatibility_matrix": {"short_form_platforms": ["tiktok", "instagram_reels", "youtube_shorts", "x_twitter_short", "facebook_reels", "snapchat"], "long_form_platforms": ["youtube_long", "instagram_feed", "x_twitter_long", "facebook_feed", "linkedin"], "conversion_rules": {"short_to_short": {"allowed": true, "compatible_pairs": [{"source": "tiktok", "targets": ["instagram_reels", "youtube_shorts", "x_twitter_short", "facebook_reels", "snapchat"]}, {"source": "instagram_reels", "targets": ["tiktok", "youtube_shorts", "x_twitter_short", "facebook_reels", "snapchat"]}, {"source": "youtube_shorts", "targets": ["tiktok", "instagram_reels", "x_twitter_short", "facebook_reels", "snapchat"]}, {"source": "x_twitter_short", "targets": ["tiktok", "instagram_reels", "youtube_shorts", "facebook_reels", "snapchat"]}, {"source": "facebook_reels", "targets": ["tiktok", "instagram_reels", "youtube_shorts", "x_twitter_short", "snapchat"]}, {"source": "snapchat", "targets": ["tiktok", "instagram_reels", "youtube_shorts", "x_twitter_short", "facebook_reels"]}]}, "long_to_long": {"allowed": true, "compatible_pairs": [{"source": "youtube_long", "targets": ["instagram_feed", "x_twitter_long", "facebook_feed", "linkedin"]}, {"source": "instagram_feed", "targets": ["youtube_long", "x_twitter_long", "facebook_feed", "linkedin"]}, {"source": "x_twitter_long", "targets": ["youtube_long", "instagram_feed", "facebook_feed", "linkedin"]}, {"source": "facebook_feed", "targets": ["youtube_long", "instagram_feed", "x_twitter_long", "linkedin"]}, {"source": "linkedin", "targets": ["youtube_long", "instagram_feed", "x_twitter_long", "facebook_feed"]}]}, "cross_form_conversion": {"short_to_long": false, "long_to_short": false, "reason": "Form-type mismatch not supported for content integrity"}}}, "validation_logic": {"form_classification_function": {"name": "classify_video_form", "parameters": ["duration_seconds", "height", "width"], "logic": {"short_form_criteria": {"duration": "duration_seconds <= 120", "aspect_ratio": "(height / width) >= 1.6"}, "implementation": "return 'short_form' if (duration_seconds <= 120 and height/width >= 1.6) else 'long_form'"}}, "same_form_enforcement": {"enabled": true, "validation_steps": ["1. Classify source video form type", "2. Check target platform form type support", "3. Verify same-form compatibility", "4. Reject cross-form conversions"]}, "conversion_validation": {"required_checks": ["source_form_classification", "target_platform_form_support", "same_form_compatibility", "technical_specification_compatibility", "duration_limits", "file_size_limits", "resolution_compatibility"]}}, "usage_guidelines": {"platform_identification": {"short_form_identifiers": {"tiktok": "Vertical mobile-first, 15-120s", "instagram_reels": "Vertical stories, 15-90s", "youtube_shorts": "Vertical short-form, max 60s", "x_twitter_short": "Quick engagement, max 140s", "facebook_reels": "Mobile-first vertical, max 120s", "snapchat": "Ephemeral vertical, 3-180s"}, "long_form_identifiers": {"youtube_long": "Traditional video content, 2+ minutes", "instagram_feed": "Square/portrait feed posts", "x_twitter_long": "Extended content for premium users", "facebook_feed": "Traditional social media posts", "linkedin": "Professional business content"}}, "conversion_best_practices": {"quality_optimization": ["Use platform-specific presets", "Maintain aspect ratio integrity", "Optimize for mobile viewing (short-form)", "Consider safe zones for UI elements", "Ensure audio quality preservation"], "content_adaptation": ["Adjust pacing for platform expectations", "Consider platform-specific UI overlays", "Optimize thumbnail/first frame", "Ensure captions compatibility", "Test across different devices"]}}, "api_integration": {"classification_endpoint": "/api/v1/classify-video-form", "validation_endpoint": "/api/v1/validate-conversion", "conversion_endpoint": "/api/v1/convert-video", "presets_endpoint": "/api/v1/conversion-presets", "response_formats": {"classification_response": {"form_type": "string (short_form|long_form)", "duration_seconds": "number", "aspect_ratio": "number", "classification_confidence": "number (0-1)"}, "validation_response": {"valid": "boolean", "source_form": "string", "target_form": "string", "compatibility": "boolean", "errors": "array of strings"}}}, "maintenance_guide": {"form_type_updates": {"classification_logic": "Review quarterly for platform changes", "duration_thresholds": "Monitor platform max duration changes", "aspect_ratio_trends": "Track emerging format trends"}, "conversion_presets": {"quality_optimization": "Test and update monthly", "new_platform_support": "Add presets for new platforms", "deprecated_formats": "Remove outdated specifications"}}}