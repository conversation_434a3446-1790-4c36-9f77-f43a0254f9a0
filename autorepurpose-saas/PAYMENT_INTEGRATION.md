
# Payment Integration Guide - AutoRepurpose SaaS

This document outlines the dual payment provider integration for AutoRepurpose, supporting both Stripe and Razorpay payment systems.

## Overview

AutoRepurpose supports two payment providers to cater to different regional preferences:

- **Stripe**: International payments (USD $30/month)
- **Razorpay**: India-focused payments (INR ₹2,500/month)

## Architecture

### Database Schema

The payment system uses a unified database schema that supports both providers:

#### Users Table Updates
```sql
-- New fields added for dual payment support
ALTER TABLE users ADD COLUMN stripe_customer_id VARCHAR(255);
ALTER TABLE users ADD COLUMN razorpay_customer_id VARCHAR(255);
ALTER TABLE users ADD COLUMN preferred_payment_provider VARCHAR(20) DEFAULT 'stripe';
```

#### Payment Transactions Table
```sql
-- Unified payment transactions table
CREATE TABLE payment_transactions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  provider VARCHAR(20) NOT NULL, -- 'stripe' or 'razorpay'
  
  -- Strip<PERSON> fields
  stripe_payment_intent_id VARCHAR(255),
  stripe_subscription_id VARCHAR(255),
  stripe_customer_id VARCHAR(255),
  
  -- <PERSON><PERSON><PERSON><PERSON> fields
  razorpay_payment_id VARCHAR(255),
  razorpay_order_id VARCHAR(255),
  razorpay_subscription_id VARCHAR(255),
  razorpay_customer_id VARCHAR(255),
  
  -- Common fields
  amount_cents INTEGER NOT NULL,
  currency VARCHAR(3) NOT NULL,
  status VARCHAR(50) NOT NULL,
  transaction_type VARCHAR(50) NOT NULL,
  provider_transaction_id VARCHAR(255),
  metadata JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## API Endpoints

### Stripe Endpoints
- `POST /api/payments/stripe/create-session` - Create checkout session
- `POST /api/payments/stripe/webhook` - Handle Stripe webhooks

### Razorpay Endpoints
- `POST /api/payments/razorpay/create-order` - Create payment order
- `POST /api/payments/razorpay/verify` - Verify payment signature
- `POST /api/payments/razorpay/webhook` - Handle Razorpay webhooks

### Unified Endpoints
- `GET /api/payments/subscription` - Get subscription status (both providers)
- `POST /api/payments/cancel` - Cancel subscription (both providers)

## Environment Variables

### Stripe Configuration
```env
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PRICE_ID=price_...
```

### Razorpay Configuration
```env
RAZORPAY_KEY_ID=rzp_live_...
RAZORPAY_KEY_SECRET=...
RAZORPAY_WEBHOOK_SECRET=...
RAZORPAY_PLAN_ID=plan_...
```

## Implementation Details

### Payment Flow

1. **Provider Selection**: User chooses between Stripe or Razorpay
2. **Payment Processing**: Different flows based on provider
3. **Verification**: Signature/webhook verification
4. **Subscription Activation**: Update user subscription status
5. **Transaction Logging**: Record payment details in unified table

### Stripe Flow
```typescript
// Create checkout session
const session = await stripe.checkout.sessions.create({
  customer: stripeCustomerId,
  mode: 'subscription',
  line_items: [{
    price: process.env.STRIPE_PRICE_ID,
    quantity: 1,
  }],
  success_url: successUrl,
  cancel_url: cancelUrl,
});

// Redirect to Stripe Checkout
window.location.href = session.url;
```

### Razorpay Flow
```typescript
// Create order
const order = await razorpay.orders.create({
  amount: 250000, // ₹2,500 in paise
  currency: 'INR',
  receipt: `order_${userId}_${timestamp}`,
});

// Open Razorpay checkout
const rzp = new Razorpay({
  key: process.env.RAZORPAY_KEY_ID,
  amount: order.amount,
  order_id: order.id,
  handler: async (response) => {
    // Verify payment on server
    await verifyPayment(response);
  }
});
rzp.open();
```

## Security Considerations

### Webhook Verification

#### Stripe
```typescript
const sig = req.headers['stripe-signature'];
const event = stripe.webhooks.constructEvent(
  req.body,
  sig,
  process.env.STRIPE_WEBHOOK_SECRET
);
```

#### Razorpay
```typescript
const expectedSignature = crypto
  .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET)
  .update(req.body)
  .digest('hex');

if (signature !== expectedSignature) {
  throw new Error('Invalid signature');
}
```

### Payment Verification

#### Razorpay Payment Signature
```typescript
const hmac = crypto.createHmac('sha256', process.env.RAZORPAY_KEY_SECRET);
hmac.update(`${order_id}|${payment_id}`);
const generated_signature = hmac.digest('hex');
```

## Error Handling

### Failed Payments
- Log failed transactions in database
- Send appropriate error responses to client
- Implement retry logic for transient failures
- Notify users of payment failures

### Webhook Failures
- Implement exponential backoff for webhook retries
- Log webhook processing errors
- Handle duplicate webhook events (idempotency)

## Testing

### Stripe Test Mode
```env
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
```

### Razorpay Test Mode
```env
RAZORPAY_KEY_ID=rzp_test_...
RAZORPAY_KEY_SECRET=test_secret...
```

### Test Cards/Credentials
- Use provider-specific test credentials
- Test both success and failure scenarios
- Verify webhook handling

## Monitoring & Analytics

### Payment Metrics
- Track conversion rates by provider
- Monitor failed payment rates
- Analyze regional preferences
- Revenue tracking by currency

### Alerts
- Failed webhook deliveries
- High payment failure rates
- Subscription cancellations
- Revenue anomalies

## Regional Considerations

### Currency & Pricing
- **Stripe**: USD $30/month (global)
- **Razorpay**: INR ₹2,500/month (India-focused)

### Payment Methods
- **Stripe**: Cards, Apple Pay, Google Pay
- **Razorpay**: UPI, Net Banking, Cards, Wallets, EMI

### Compliance
- **Stripe**: PCI DSS compliant, international regulations
- **Razorpay**: RBI compliant, Indian payment regulations

## Migration & Rollback

### Provider Migration
```sql
-- Update user's preferred provider
UPDATE users SET preferred_payment_provider = 'razorpay' WHERE id = ?;

-- Migrate customer data
UPDATE users SET razorpay_customer_id = ? WHERE stripe_customer_id = ?;
```

### Data Consistency
- Maintain transaction history for both providers
- Ensure subscription status consistency
- Handle provider-specific subscription IDs

## Future Enhancements

1. **Dynamic Provider Selection**: Auto-select based on user location
2. **Payment Method Preferences**: Remember user's preferred method
3. **Multi-Currency Support**: Support more currencies
4. **Payment Analytics Dashboard**: Provider performance comparison
5. **Subscription Pausing**: Temporary subscription holds
6. **Prorated Billing**: Mid-cycle plan changes

---

This dual payment provider setup ensures maximum conversion rates by offering region-appropriate payment methods while maintaining a unified backend architecture.
