{"name": "autorepurpose-worker", "version": "1.0.0", "description": "Video processing worker for AutoRepurpose SaaS", "main": "dist/index.js", "scripts": {"dev": "tsx --watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"No tests yet\" && exit 0"}, "dependencies": {"@aws-sdk/client-s3": "^3.864.0", "axios": "^1.7.2", "bullmq": "^5.58.0", "dotenv": "^16.5.0", "fluent-ffmpeg": "^2.1.2", "fs-extra": "^11.2.0", "ioredis": "^5.7.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/fluent-ffmpeg": "^2.1.24", "@types/fs-extra": "^11.0.4", "@types/node": "^20.6.2", "@types/uuid": "^9.0.8", "tsx": "^4.20.3", "typescript": "^5.2.2"}, "keywords": ["video", "processing", "ffmpeg", "worker", "saas"], "author": "AutoRepurpose Team", "license": "MIT"}