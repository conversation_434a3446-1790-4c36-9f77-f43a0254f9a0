
import { Job } from 'bullmq';
import path from 'path';
import fs from 'fs-extra';
import { v4 as uuidv4 } from 'uuid';
import { VideoJob, PlatformOutput } from '../types/job';
import { S3Service } from '../services/s3Service';
import { VideoProcessor } from '../services/videoProcessor';
import { PlatformRulesService } from '../services/platformRules';
import { ApiClient } from '../services/apiClient';
import { config } from '../config';

export class JobProcessor {
  private s3Service: S3Service;
  private videoProcessor: VideoProcessor;
  private apiClient: ApiClient;

  constructor() {
    this.s3Service = new S3Service();
    this.videoProcessor = new VideoProcessor();
    this.apiClient = new ApiClient();
  }

  /**
   * Process a video job
   */
  async processJob(job: Job<VideoJob>): Promise<void> {
    const jobData = job.data;
    const { id: jobId, target_platforms, s3_input_key, original_filename } = jobData;

    console.log(`🚀 Starting job processing: ${jobId}`);
    console.log(`📄 Original file: ${original_filename}`);
    console.log(`🎯 Target platforms: ${target_platforms.join(', ')}`);

    try {
      // Update job status to processing
      await this.apiClient.updateJobStatus(jobId, 'processing', 0);

      // Create temp directories
      const tempDir = path.join(config.processing.tempDir, jobId);
      const inputDir = path.join(tempDir, 'input');
      const outputDir = path.join(tempDir, 'output');
      
      await fs.ensureDir(inputDir);
      await fs.ensureDir(outputDir);

      // Download original video from S3
      const inputPath = path.join(inputDir, original_filename);
      console.log(`📥 Downloading original video: ${s3_input_key}`);
      
      await this.s3Service.downloadFile(s3_input_key, inputPath);
      await this.apiClient.updateJobStatus(jobId, 'processing', 10);

      // Validate input video
      const isValid = await this.videoProcessor.validateVideoFile(inputPath);
      if (!isValid) {
        throw new Error('Invalid video file');
      }

      // Get video metadata
      const metadata = await this.videoProcessor.getVideoMetadata(inputPath);
      const duration = metadata.format.duration;
      console.log(`⏱️  Video duration: ${duration} seconds`);

      await this.apiClient.updateJobStatus(jobId, 'processing', 15);

      // Process video for each target platform
      const outputs: PlatformOutput[] = [];
      const totalPlatforms = target_platforms.length;
      
      for (let i = 0; i < totalPlatforms; i++) {
        const platform = target_platforms[i];
        const baseProgress = 15 + (i * 70 / totalPlatforms);
        const platformProgress = 70 / totalPlatforms;

        console.log(`🎬 Processing for platform: ${platform}`);

        try {
          // Get platform processing options
          const processingOptions = PlatformRulesService.getProcessingOptions(platform);
          if (!processingOptions) {
            throw new Error(`Unsupported platform: ${platform}`);
          }

          // Generate output filename and path
          const outputFilename = `${path.parse(original_filename).name}_${platform}.mp4`;
          const outputPath = path.join(outputDir, outputFilename);

          // Process video with progress tracking
          await this.videoProcessor.processVideo(
            inputPath, 
            outputPath, 
            processingOptions,
            (progress) => {
              const totalProgress = baseProgress + (progress * platformProgress / 100);
              this.apiClient.updateJobStatus(jobId, 'processing', Math.round(totalProgress));
            }
          );

          // Upload processed video to S3
          const s3OutputKey = this.s3Service.generateProcessedKey(s3_input_key, platform, jobId);
          console.log(`📤 Uploading processed video: ${s3OutputKey}`);
          
          await this.s3Service.uploadFile(outputPath, s3OutputKey, 'video/mp4');

          // Get file stats
          const outputStats = await fs.stat(outputPath);
          const outputMetadata = await this.videoProcessor.getVideoMetadata(outputPath);

          // Create platform output record
          const output: PlatformOutput = {
            id: uuidv4(),
            job_id: jobId,
            platform: platform,
            s3_output_key: s3OutputKey,
            file_size_bytes: outputStats.size,
            duration_seconds: Math.round(outputMetadata.format.duration),
            resolution: `${processingOptions.resolution.width}x${processingOptions.resolution.height}`,
            format: 'mp4',
            status: 'completed',
            created_at: new Date().toISOString(),
          };

          outputs.push(output);

          // Clean up platform output file
          await fs.remove(outputPath);

          console.log(`✅ Successfully processed ${platform}`);
        } catch (platformError) {
          console.error(`❌ Failed to process ${platform}:`, platformError);
          
          // Create failed output record
          const output: PlatformOutput = {
            id: uuidv4(),
            job_id: jobId,
            platform: platform,
            s3_output_key: '',
            file_size_bytes: 0,
            duration_seconds: 0,
            resolution: '0x0',
            format: 'mp4',
            status: 'failed',
            created_at: new Date().toISOString(),
          };

          outputs.push(output);
        }
      }

      // Update job progress to 90%
      await this.apiClient.updateJobStatus(jobId, 'processing', 90);

      // Create platform output records
      for (const output of outputs) {
        await this.apiClient.createPlatformOutput(output);
      }

      // Complete the job
      await this.apiClient.completeJob(jobId, outputs);
      await this.apiClient.updateJobStatus(jobId, 'completed', 100);

      // Clean up temp directory
      await fs.remove(tempDir);

      console.log(`🎉 Job ${jobId} completed successfully!`);
      console.log(`📊 Processed ${outputs.filter(o => o.status === 'completed').length}/${totalPlatforms} platforms`);

    } catch (error) {
      console.error(`❌ Job ${jobId} failed:`, error);
      
      // Update job status to failed
      await this.apiClient.updateJobStatus(
        jobId, 
        'failed', 
        0, 
        error instanceof Error ? error.message : 'Unknown error'
      );

      // Clean up temp directory
      const tempDir = path.join(config.processing.tempDir, jobId);
      await fs.remove(tempDir).catch(console.error);

      throw error;
    }
  }
}
