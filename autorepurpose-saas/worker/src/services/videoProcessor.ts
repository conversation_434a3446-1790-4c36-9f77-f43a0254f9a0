
import ffmpeg from 'fluent-ffmpeg';
import path from 'path';
import fs from 'fs-extra';
import { config } from '../config';
import { ProcessingOptions } from '../types/job';

export class VideoProcessor {
  constructor() {
    // Set FFmpeg paths
    ffmpeg.setFfmpegPath(config.ffmpeg.path);
    ffmpeg.setFfprobePath(config.ffmpeg.probePath);
  }

  /**
   * Get video metadata using ffprobe
   */
  async getVideoMetadata(inputPath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          console.error('❌ FFprobe error:', err);
          reject(err);
          return;
        }
        
        console.log('📊 Video metadata:', {
          duration: metadata.format.duration,
          size: metadata.format.size,
          bitrate: metadata.format.bit_rate,
        });
        
        resolve(metadata);
      });
    });
  }

  /**
   * Process video according to platform specifications
   */
  async processVideo(
    inputPath: string, 
    outputPath: string, 
    options: ProcessingOptions,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    console.log(`🎬 Processing video for ${options.platform}`);
    console.log(`📁 Input: ${inputPath}`);
    console.log(`📁 Output: ${outputPath}`);
    console.log(`⚙️  Options:`, options);

    // Ensure output directory exists
    await fs.ensureDir(path.dirname(outputPath));

    return new Promise((resolve, reject) => {
      let command = ffmpeg(inputPath);

      // Video codec and quality settings
      command = command
        .videoCodec(options.videoCodec)
        .audioCodec(options.audioCodec)
        .videoBitrate(options.videoBitrate)
        .audioBitrate(options.audioBitrate)
        .fps(options.framerate);

      // Resolution and aspect ratio
      const { width, height } = options.resolution;
      command = command.size(`${width}x${height}`);

      // Aspect ratio handling
      if (options.aspectRatio === '9:16') {
        // Vertical video - crop or pad as needed
        command = command.aspect('9:16');
      } else if (options.aspectRatio === '16:9') {
        // Horizontal video
        command = command.aspect('16:9');
      } else if (options.aspectRatio === '1:1') {
        // Square video
        command = command.aspect('1:1');
      }

      // Duration limit if specified
      if (options.maxDuration) {
        command = command.duration(options.maxDuration);
      }

      // Quality optimizations
      if (config.processing.outputQuality === 'high') {
        command = command
          .addOption('-crf', '23')  // Good quality for h264
          .addOption('-preset', 'medium')
          .addOption('-tune', 'film');
      } else {
        command = command
          .addOption('-crf', '28')  // Lower quality, smaller file
          .addOption('-preset', 'fast');
      }

      // Platform-specific optimizations
      switch (options.platform.toLowerCase()) {
        case 'instagram_story':
        case 'instagram_reel':
        case 'tiktok':
          // Optimize for mobile vertical viewing
          command = command
            .addOption('-movflags', '+faststart')
            .addOption('-pix_fmt', 'yuv420p');
          break;
          
        case 'youtube_shorts':
          // YouTube-specific optimizations
          command = command
            .addOption('-movflags', '+faststart')
            .addOption('-pix_fmt', 'yuv420p')
            .addOption('-g', '30'); // GOP size
          break;
          
        case 'facebook_feed':
        case 'twitter':
        case 'linkedin':
          // Web-optimized settings
          command = command
            .addOption('-movflags', '+faststart')
            .addOption('-pix_fmt', 'yuv420p');
          break;
      }

      // Progress tracking
      command.on('progress', (progress) => {
        const percent = Math.round(progress.percent || 0);
        console.log(`⏳ Processing: ${percent}%`);
        if (onProgress) {
          onProgress(percent);
        }
      });

      // Error handling
      command.on('error', (err) => {
        console.error(`❌ FFmpeg processing error:`, err);
        reject(err);
      });

      // Success handling
      command.on('end', () => {
        console.log(`✅ Successfully processed video for ${options.platform}`);
        resolve();
      });

      // Start processing
      command.save(outputPath);
    });
  }

  /**
   * Validate video file
   */
  async validateVideoFile(inputPath: string): Promise<boolean> {
    try {
      const metadata = await this.getVideoMetadata(inputPath);
      
      // Check if file has video stream
      const hasVideo = metadata.streams.some((stream: any) => stream.codec_type === 'video');
      if (!hasVideo) {
        throw new Error('No video stream found');
      }

      // Check file size
      const stats = await fs.stat(inputPath);
      const fileSizeGB = stats.size / (1024 * 1024 * 1024);
      if (fileSizeGB > config.processing.maxFileSizeGB) {
        throw new Error(`File size (${fileSizeGB.toFixed(2)}GB) exceeds maximum allowed (${config.processing.maxFileSizeGB}GB)`);
      }

      console.log('✅ Video file validation passed');
      return true;
    } catch (error) {
      console.error('❌ Video file validation failed:', error);
      return false;
    }
  }
}
