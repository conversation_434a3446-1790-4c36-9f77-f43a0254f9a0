
import { S3Client, GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { config } from '../config';
import fs from 'fs-extra';
import path from 'path';

export class S3Service {
  private s3Client: S3Client;

  constructor() {
    this.s3Client = new S3Client({
      region: config.aws.region,
      credentials: {
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey,
      },
    });
  }

  /**
   * Download file from S3 to local filesystem
   */
  async downloadFile(s3Key: string, localPath: string): Promise<void> {
    console.log(`📥 Downloading ${s3Key} from S3 to ${localPath}`);
    
    try {
      const command = new GetObjectCommand({
        Bucket: config.aws.bucketName,
        Key: s3Key,
      });

      const response = await this.s3Client.send(command);
      
      if (!response.Body) {
        throw new Error('No response body from S3');
      }

      // Ensure directory exists
      await fs.ensureDir(path.dirname(localPath));

      // Handle the response body
      if (response.Body) {
        // Convert stream to buffer
        const chunks = [];
        for await (const chunk of response.Body as any) {
          chunks.push(chunk);
        }
        const buffer = Buffer.concat(chunks);
        await fs.writeFile(localPath, buffer);
        console.log(`✅ Successfully downloaded ${s3Key}`);
      } else {
        throw new Error('No response body received');
      }
    } catch (error) {
      console.error(`❌ Error downloading ${s3Key}:`, error);
      throw error;
    }
  }

  /**
   * Upload file from local filesystem to S3
   */
  async uploadFile(localPath: string, s3Key: string, contentType?: string): Promise<string> {
    console.log(`📤 Uploading ${localPath} to S3 as ${s3Key}`);
    
    try {
      const fileStream = fs.createReadStream(localPath);
      const stats = await fs.stat(localPath);

      const command = new PutObjectCommand({
        Bucket: config.aws.bucketName,
        Key: s3Key,
        Body: fileStream,
        ContentType: contentType || 'video/mp4',
        ContentLength: stats.size,
      });

      await this.s3Client.send(command);
      
      const s3Url = `https://${config.aws.bucketName}.s3.${config.aws.region}.amazonaws.com/${s3Key}`;
      console.log(`✅ Successfully uploaded to ${s3Url}`);
      
      return s3Url;
    } catch (error) {
      console.error(`❌ Error uploading ${s3Key}:`, error);
      throw error;
    }
  }

  /**
   * Generate S3 key for processed video
   */
  generateProcessedKey(originalKey: string, platform: string, jobId: string): string {
    const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
    const extension = path.extname(originalKey);
    const baseName = path.basename(originalKey, extension);
    
    return `processed/${timestamp}/${jobId}/${baseName}_${platform}${extension}`;
  }

  /**
   * Get file size from S3
   */
  async getFileSize(s3Key: string): Promise<number> {
    try {
      const command = new GetObjectCommand({
        Bucket: config.aws.bucketName,
        Key: s3Key,
      });

      const response = await this.s3Client.send(command);
      return response.ContentLength || 0;
    } catch (error) {
      console.error(`❌ Error getting file size for ${s3Key}:`, error);
      throw error;
    }
  }
}
