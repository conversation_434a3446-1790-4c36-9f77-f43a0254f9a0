
import { ProcessingOptions } from '../types/job';

interface PlatformRule {
  platform: string;
  displayName: string;
  aspectRatio: string;
  resolution: {
    width: number;
    height: number;
  };
  maxDuration?: number;
  videoCodec: string;
  audioCodec: string;
  framerate: number;
  videoBitrate: string;
  audioBitrate: string;
  format: string;
}

export class PlatformRulesService {
  private static rules: Record<string, PlatformRule> = {
    'instagram_story': {
      platform: 'instagram_story',
      displayName: 'Instagram Story',
      aspectRatio: '9:16',
      resolution: { width: 1080, height: 1920 },
      maxDuration: 15,
      videoCodec: 'libx264',
      audioCodec: 'aac',
      framerate: 30,
      videoBitrate: '2500k',
      audioBitrate: '128k',
      format: 'mp4'
    },
    'instagram_reel': {
      platform: 'instagram_reel',
      displayName: 'Instagram Reel',
      aspectRatio: '9:16',
      resolution: { width: 1080, height: 1920 },
      maxDuration: 90,
      videoCodec: 'libx264',
      audioCodec: 'aac',
      framerate: 30,
      videoBitrate: '3500k',
      audioBitrate: '192k',
      format: 'mp4'
    },
    'youtube_shorts': {
      platform: 'youtube_shorts',
      displayName: 'YouTube Shorts',
      aspectRatio: '9:16',
      resolution: { width: 1080, height: 1920 },
      maxDuration: 60,
      videoCodec: 'libx264',
      audioCodec: 'aac',
      framerate: 30,
      videoBitrate: '4000k',
      audioBitrate: '192k',
      format: 'mp4'
    },
    'tiktok': {
      platform: 'tiktok',
      displayName: 'TikTok',
      aspectRatio: '9:16',
      resolution: { width: 1080, height: 1920 },
      maxDuration: 180,
      videoCodec: 'libx264',
      audioCodec: 'aac',
      framerate: 30,
      videoBitrate: '3000k',
      audioBitrate: '128k',
      format: 'mp4'
    },
    'facebook_feed': {
      platform: 'facebook_feed',
      displayName: 'Facebook Feed',
      aspectRatio: '16:9',
      resolution: { width: 1920, height: 1080 },
      maxDuration: 240,
      videoCodec: 'libx264',
      audioCodec: 'aac',
      framerate: 30,
      videoBitrate: '4000k',
      audioBitrate: '192k',
      format: 'mp4'
    },
    'twitter': {
      platform: 'twitter',
      displayName: 'Twitter/X',
      aspectRatio: '16:9',
      resolution: { width: 1280, height: 720 },
      maxDuration: 140,
      videoCodec: 'libx264',
      audioCodec: 'aac',
      framerate: 30,
      videoBitrate: '3000k',
      audioBitrate: '128k',
      format: 'mp4'
    },
    'linkedin': {
      platform: 'linkedin',
      displayName: 'LinkedIn',
      aspectRatio: '16:9',
      resolution: { width: 1920, height: 1080 },
      maxDuration: 300,
      videoCodec: 'libx264',
      audioCodec: 'aac',
      framerate: 30,
      videoBitrate: '5000k',
      audioBitrate: '192k',
      format: 'mp4'
    }
  };

  /**
   * Get processing options for a platform
   */
  static getProcessingOptions(platform: string): ProcessingOptions | null {
    const rule = this.rules[platform.toLowerCase()];
    if (!rule) {
      console.error(`❌ Platform not supported: ${platform}`);
      return null;
    }

    return {
      platform: rule.platform,
      aspectRatio: rule.aspectRatio,
      resolution: rule.resolution,
      maxDuration: rule.maxDuration,
      videoCodec: rule.videoCodec,
      audioCodec: rule.audioCodec,
      framerate: rule.framerate,
      videoBitrate: rule.videoBitrate,
      audioBitrate: rule.audioBitrate,
    };
  }

  /**
   * Get all supported platforms
   */
  static getSupportedPlatforms(): string[] {
    return Object.keys(this.rules);
  }

  /**
   * Validate platform
   */
  static isValidPlatform(platform: string): boolean {
    return platform.toLowerCase() in this.rules;
  }

  /**
   * Get platform display name
   */
  static getDisplayName(platform: string): string {
    const rule = this.rules[platform.toLowerCase()];
    return rule ? rule.displayName : platform;
  }
}
