
import { Worker, Queue } from 'bullmq';
import IORedis from 'ioredis';
import { config } from './config';
import { JobProcessor } from './worker/jobProcessor';
import { VideoJob } from './types/job';

// Create Redis connection
const redis = new IORedis(config.redis.url, {
  maxRetriesPerRequest: 3,
});

// Create job processor
const jobProcessor = new JobProcessor();

// Create worker
const worker = new Worker(
  'video-processing',
  async (job) => {
    console.log(`🔄 Processing job: ${job.id}`);
    await jobProcessor.processJob(job);
  },
  {
    connection: redis,
    concurrency: config.worker.concurrency,
    removeOnComplete: { count: 100 },
    removeOnFail: { count: 50 },
  }
);

// Worker event handlers
worker.on('ready', () => {
  console.log(`🚀 Worker '${config.worker.name}' is ready!`);
  console.log(`⚙️  Concurrency: ${config.worker.concurrency}`);
  console.log(`🔗 Redis: Connected`);
  console.log(`🎬 FFmpeg: ${config.ffmpeg.path}`);
});

worker.on('active', (job) => {
  console.log(`▶️  Job ${job.id} started processing`);
});

worker.on('completed', (job, result) => {
  console.log(`✅ Job ${job.id} completed successfully`);
});

worker.on('failed', (job, err) => {
  console.error(`❌ Job ${job?.id} failed:`, err.message);
});

worker.on('error', (err) => {
  console.error('❌ Worker error:', err);
});

worker.on('stalled', (jobId) => {
  console.warn(`⚠️  Job ${jobId} stalled`);
});

// Graceful shutdown
const shutdown = async (signal: string) => {
  console.log(`📡 Received ${signal}, shutting down gracefully...`);
  
  try {
    await worker.close();
    await redis.disconnect();
    console.log('✅ Worker shut down successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
};

process.on('SIGTERM', () => shutdown('SIGTERM'));
process.on('SIGINT', () => shutdown('SIGINT'));

// Health check endpoint (basic HTTP server)
import { createServer } from 'http';

const healthServer = createServer((req, res) => {
  if (req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      status: 'healthy', 
      worker: config.worker.name,
      timestamp: new Date().toISOString() 
    }));
  } else {
    res.writeHead(404);
    res.end('Not Found');
  }
});

const PORT = process.env.PORT || 3001;
healthServer.listen(PORT, () => {
  console.log(`🏥 Health check server running on port ${PORT}`);
});

console.log(`🎥 AutoRepurpose Video Processing Worker started`);
console.log(`📦 Environment: ${config.env}`);
console.log(`🔧 Temp Directory: ${config.processing.tempDir}`);
console.log(`📡 API URL: ${config.app.apiUrl}`);

export { worker, redis };
