
export interface VideoJob {
  id: string;
  user_id: string;
  original_filename: string;
  s3_input_key: string;
  file_size_bytes: number;
  duration_seconds?: number;
  target_platforms: string[];
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress?: number;
  error_message?: string;
  created_at: string;
  updated_at?: string;
}

export interface PlatformOutput {
  id: string;
  job_id: string;
  platform: string;
  s3_output_key: string;
  file_size_bytes: number;
  duration_seconds: number;
  resolution: string;
  format: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
}

export interface ProcessingOptions {
  platform: string;
  aspectRatio: string;
  resolution: {
    width: number;
    height: number;
  };
  maxDuration?: number;
  videoCodec: string;
  audioCodec: string;
  framerate: number;
  videoBitrate: string;
  audioBitrate: string;
}
