
# 🔐 RAILWAY DEPLOYMENT CREDENTIALS - KEEP PRIVATE!
# This file is gitignored and will NOT be pushed to GitHub

## Copy These Environment Variables to Railway Dashboard

### Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://fjdrtzxfjjgsjfknmvvl.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZqZHJ0enhmampnc2pma25tdnZsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTMyMTk5NywiZXhwIjoyMDcwODk3OTk3fQ.geElVD_BMOaksKVISUev6jeVDNC0b39UsBJXKAcaoZg

### AWS S3 Configuration  
AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=twF21iE+cHN8/P4lzbz3+fuFZLDKAvv2fIxQ8/19
S3_BUCKET_NAME=auto-repurpose

### Redis/Upstash Configuration
UPSTASH_REDIS_REST_URL=https://boss-cockatoo-47800.upstash.io
UPSTASH_REDIS_REST_TOKEN=Abq4AAIncDE3YWNjN2ZjMzZiZjg0M2NiYmZhNGVlZTJkMGQ0NmYyOHAxNDc4MDA

### Worker Configuration
WORKER_CONCURRENCY=3
MAX_PROCESSING_TIME_MS=600000
LOG_LEVEL=info
NODE_ENV=production

## Railway Deployment Steps:
1. Create GitHub repo from worker directory
2. Push to GitHub (sensitive files will be ignored)
3. Deploy to Railway from GitHub
4. Add ALL above environment variables in Railway Dashboard
5. Deploy and monitor logs

