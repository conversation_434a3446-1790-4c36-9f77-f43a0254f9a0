
# AutoRepurpose Worker Service

Video processing worker for AutoRepurpose SaaS platform.

## Features

- **Video Processing**: Convert videos for multiple social media platforms
- **BullMQ Integration**: Reliable job queue processing
- **S3 Integration**: Download/upload videos from/to AWS S3
- **Platform Optimization**: Specific settings for Instagram, TikTok, YouTube, etc.
- **Progress Tracking**: Real-time job progress updates
- **Error Handling**: Retry failed jobs with exponential backoff

## Quick Start

1. **Install Dependencies**:
   ```bash
   yarn install
   ```

2. **Install FFmpeg** (Required for video processing):
   ```bash
   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg

   # macOS
   brew install ffmpeg

   # Windows
   # Download from https://ffmpeg.org/download.html
   ```

3. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

4. **Development**:
   ```bash
   yarn dev
   ```

5. **Production**:
   ```bash
   yarn build
   yarn start
   ```

## Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `REDIS_URL` | Redis connection URL | `rediss://user:pass@host:6379` |
| `AWS_*` | AWS S3 credentials | See .env.example |
| `WORKER_CONCURRENCY` | Number of concurrent jobs | `3` |
| `APP_API_URL` | Main app API URL | `https://your-app.com` |
| `TEMP_DIR` | Temporary processing directory | `/tmp/autorepurpose` |

## Supported Platforms

- **Instagram**: Stories, Reels
- **TikTok**: Vertical videos
- **YouTube**: Shorts, Regular
- **Facebook**: Feed posts
- **Twitter/X**: Video posts
- **LinkedIn**: Professional content

## Processing Pipeline

1. **Download** original video from S3
2. **Validate** video file format and size
3. **Process** for each target platform:
   - Apply platform-specific resolution
   - Optimize video/audio codecs
   - Adjust aspect ratio
   - Set duration limits
4. **Upload** processed videos to S3
5. **Update** job status in main app
6. **Cleanup** temporary files

## Deployment

### Railway (Recommended)

1. Connect your GitHub repository to Railway
2. Set environment variables in Railway dashboard
3. Railway will automatically deploy on push to main branch

### Docker

```bash
# Build image
docker build -t autorepurpose-worker .

# Run container
docker run -d \
  --name autorepurpose-worker \
  --env-file .env \
  -v /tmp:/tmp \
  autorepurpose-worker
```

## Monitoring

- Health check endpoint: `GET /health`
- Logs include detailed job processing information
- BullMQ provides job queue metrics

## Troubleshooting

**FFmpeg not found**:
```bash
# Check FFmpeg installation
ffmpeg -version
ffprobe -version

# Install if missing
sudo apt install ffmpeg
```

**Redis connection issues**:
- Verify REDIS_URL format
- Check network connectivity
- Ensure Redis server is running

**S3 upload failures**:
- Verify AWS credentials
- Check bucket permissions
- Ensure bucket exists in correct region
