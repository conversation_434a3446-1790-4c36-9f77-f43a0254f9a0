{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitThis": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}