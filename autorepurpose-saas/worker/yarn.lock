# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@aws-crypto/crc32@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/crc32@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/eab9581d3363af5ea498ae0e72de792f54d8890360e14a9d8261b7b5c55ebe080279fb2556e07994d785341cdaa99ab0b1ccf137832b53b5904cd6928f2b094b
  languageName: node
  linkType: hard

"@aws-crypto/crc32c@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/crc32c@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/223efac396cdebaf5645568fa9a38cd0c322c960ae1f4276bedfe2e1031d0112e49d7d39225d386354680ecefae29f39af469a84b2ddfa77cb6692036188af77
  languageName: node
  linkType: hard

"@aws-crypto/sha1-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha1-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/supports-web-crypto": "npm:^5.2.0"
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    "@aws-sdk/util-locate-window": "npm:^3.0.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/51fed0bf078c10322d910af179871b7d299dde5b5897873ffbeeb036f427e5d11d23db9794439226544b73901920fd19f4d86bbc103ed73cc0cfdea47a83c6ac
  languageName: node
  linkType: hard

"@aws-crypto/sha256-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/sha256-js": "npm:^5.2.0"
    "@aws-crypto/supports-web-crypto": "npm:^5.2.0"
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    "@aws-sdk/util-locate-window": "npm:^3.0.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/05f6d256794df800fe9aef5f52f2ac7415f7f3117d461f85a6aecaa4e29e91527b6fd503681a17136fa89e9dd3d916e9c7e4cfb5eba222875cb6c077bdc1d00d
  languageName: node
  linkType: hard

"@aws-crypto/sha256-js@npm:5.2.0, @aws-crypto/sha256-js@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-js@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6c48701f8336341bb104dfde3d0050c89c288051f6b5e9bdfeb8091cf3ffc86efcd5c9e6ff2a4a134406b019c07aca9db608128f8d9267c952578a3108db9fd1
  languageName: node
  linkType: hard

"@aws-crypto/supports-web-crypto@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/supports-web-crypto@npm:5.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/4d2118e29d68ca3f5947f1e37ce1fbb3239a0c569cc938cdc8ab8390d595609b5caf51a07c9e0535105b17bf5c52ea256fed705a07e9681118120ab64ee73af2
  languageName: node
  linkType: hard

"@aws-crypto/util@npm:5.2.0, @aws-crypto/util@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/util@npm:5.2.0"
  dependencies:
    "@aws-sdk/types": "npm:^3.222.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/0362d4c197b1fd64b423966945130207d1fe23e1bb2878a18e361f7743c8d339dad3f8729895a29aa34fff6a86c65f281cf5167c4bf253f21627ae80b6dd2951
  languageName: node
  linkType: hard

"@aws-sdk/client-s3@npm:^3.864.0":
  version: 3.873.0
  resolution: "@aws-sdk/client-s3@npm:3.873.0"
  dependencies:
    "@aws-crypto/sha1-browser": "npm:5.2.0"
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/credential-provider-node": "npm:3.873.0"
    "@aws-sdk/middleware-bucket-endpoint": "npm:3.873.0"
    "@aws-sdk/middleware-expect-continue": "npm:3.873.0"
    "@aws-sdk/middleware-flexible-checksums": "npm:3.873.0"
    "@aws-sdk/middleware-host-header": "npm:3.873.0"
    "@aws-sdk/middleware-location-constraint": "npm:3.873.0"
    "@aws-sdk/middleware-logger": "npm:3.873.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.873.0"
    "@aws-sdk/middleware-sdk-s3": "npm:3.873.0"
    "@aws-sdk/middleware-ssec": "npm:3.873.0"
    "@aws-sdk/middleware-user-agent": "npm:3.873.0"
    "@aws-sdk/region-config-resolver": "npm:3.873.0"
    "@aws-sdk/signature-v4-multi-region": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@aws-sdk/util-endpoints": "npm:3.873.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.873.0"
    "@aws-sdk/util-user-agent-node": "npm:3.873.0"
    "@aws-sdk/xml-builder": "npm:3.873.0"
    "@smithy/config-resolver": "npm:^4.1.5"
    "@smithy/core": "npm:^3.8.0"
    "@smithy/eventstream-serde-browser": "npm:^4.0.5"
    "@smithy/eventstream-serde-config-resolver": "npm:^4.1.3"
    "@smithy/eventstream-serde-node": "npm:^4.0.5"
    "@smithy/fetch-http-handler": "npm:^5.1.1"
    "@smithy/hash-blob-browser": "npm:^4.0.5"
    "@smithy/hash-node": "npm:^4.0.5"
    "@smithy/hash-stream-node": "npm:^4.0.5"
    "@smithy/invalid-dependency": "npm:^4.0.5"
    "@smithy/md5-js": "npm:^4.0.5"
    "@smithy/middleware-content-length": "npm:^4.0.5"
    "@smithy/middleware-endpoint": "npm:^4.1.18"
    "@smithy/middleware-retry": "npm:^4.1.19"
    "@smithy/middleware-serde": "npm:^4.0.9"
    "@smithy/middleware-stack": "npm:^4.0.5"
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/node-http-handler": "npm:^4.1.1"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/smithy-client": "npm:^4.4.10"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/url-parser": "npm:^4.0.5"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.26"
    "@smithy/util-defaults-mode-node": "npm:^4.0.26"
    "@smithy/util-endpoints": "npm:^3.0.7"
    "@smithy/util-middleware": "npm:^4.0.5"
    "@smithy/util-retry": "npm:^4.0.7"
    "@smithy/util-stream": "npm:^4.2.4"
    "@smithy/util-utf8": "npm:^4.0.0"
    "@smithy/util-waiter": "npm:^4.0.7"
    "@types/uuid": "npm:^9.0.1"
    tslib: "npm:^2.6.2"
    uuid: "npm:^9.0.1"
  checksum: 10c0/054d8f5489f7c56422ac0c918cb3977dce7c17693dba756eebbd06a4df6bfcb2fc1acd94a3990357f17976589b3c09951f573677670bc74cd3d88e3b54dd1ba1
  languageName: node
  linkType: hard

"@aws-sdk/client-sso@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/client-sso@npm:3.873.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/middleware-host-header": "npm:3.873.0"
    "@aws-sdk/middleware-logger": "npm:3.873.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.873.0"
    "@aws-sdk/middleware-user-agent": "npm:3.873.0"
    "@aws-sdk/region-config-resolver": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@aws-sdk/util-endpoints": "npm:3.873.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.873.0"
    "@aws-sdk/util-user-agent-node": "npm:3.873.0"
    "@smithy/config-resolver": "npm:^4.1.5"
    "@smithy/core": "npm:^3.8.0"
    "@smithy/fetch-http-handler": "npm:^5.1.1"
    "@smithy/hash-node": "npm:^4.0.5"
    "@smithy/invalid-dependency": "npm:^4.0.5"
    "@smithy/middleware-content-length": "npm:^4.0.5"
    "@smithy/middleware-endpoint": "npm:^4.1.18"
    "@smithy/middleware-retry": "npm:^4.1.19"
    "@smithy/middleware-serde": "npm:^4.0.9"
    "@smithy/middleware-stack": "npm:^4.0.5"
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/node-http-handler": "npm:^4.1.1"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/smithy-client": "npm:^4.4.10"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/url-parser": "npm:^4.0.5"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.26"
    "@smithy/util-defaults-mode-node": "npm:^4.0.26"
    "@smithy/util-endpoints": "npm:^3.0.7"
    "@smithy/util-middleware": "npm:^4.0.5"
    "@smithy/util-retry": "npm:^4.0.7"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/fe8e19572c128465162a39e68526fdc2eade75db81fbf30fdd2d4dc79679f108dc985c8e57e76565ee46d6179bef37347ad4d2c83bb8d9e021ca3f53c2d27f33
  languageName: node
  linkType: hard

"@aws-sdk/core@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/core@npm:3.873.0"
  dependencies:
    "@aws-sdk/types": "npm:3.862.0"
    "@aws-sdk/xml-builder": "npm:3.873.0"
    "@smithy/core": "npm:^3.8.0"
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/signature-v4": "npm:^5.1.3"
    "@smithy/smithy-client": "npm:^4.4.10"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.5"
    "@smithy/util-utf8": "npm:^4.0.0"
    fast-xml-parser: "npm:5.2.5"
    tslib: "npm:^2.6.2"
  checksum: 10c0/894af2a10e29ef37cf59854d3c52b7e0b3f040ba440a47a257258a43a1a0b39f959c72bfe4380e52a39ff3e947a3cafe0eabcd5b2fea26dccf9f6355d84f2595
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-env@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/credential-provider-env@npm:3.873.0"
  dependencies:
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/2763cc38bfd9e516d3651024fe43320b0fb6dee144b7e6fed52661baec57aca661b8c9ee102662748e38ddc7c7fc4b6b20df782fcb5502c48170d7cb87e3ca29
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-http@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/credential-provider-http@npm:3.873.0"
  dependencies:
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/fetch-http-handler": "npm:^5.1.1"
    "@smithy/node-http-handler": "npm:^4.1.1"
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/smithy-client": "npm:^4.4.10"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-stream": "npm:^4.2.4"
    tslib: "npm:^2.6.2"
  checksum: 10c0/50cc6f3fdbbfe518c3e7fd93a05fd54a24c6f30269b924465a5a8b391030f37769c742ddb5572e4a3ec9180043f1f07855d8d7d5a39698e2957e1bb1f91749df
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-ini@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/credential-provider-ini@npm:3.873.0"
  dependencies:
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/credential-provider-env": "npm:3.873.0"
    "@aws-sdk/credential-provider-http": "npm:3.873.0"
    "@aws-sdk/credential-provider-process": "npm:3.873.0"
    "@aws-sdk/credential-provider-sso": "npm:3.873.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.873.0"
    "@aws-sdk/nested-clients": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/credential-provider-imds": "npm:^4.0.7"
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/shared-ini-file-loader": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/5c314a7ae80eb7a05616186f9fd26b523b4e97b5df59cf3e8be501b3167335abb62f74f1153a73f601fbbe3588d8f182c585477b555b9f59db9a26e493440d4d
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-node@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/credential-provider-node@npm:3.873.0"
  dependencies:
    "@aws-sdk/credential-provider-env": "npm:3.873.0"
    "@aws-sdk/credential-provider-http": "npm:3.873.0"
    "@aws-sdk/credential-provider-ini": "npm:3.873.0"
    "@aws-sdk/credential-provider-process": "npm:3.873.0"
    "@aws-sdk/credential-provider-sso": "npm:3.873.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/credential-provider-imds": "npm:^4.0.7"
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/shared-ini-file-loader": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/1a8129d392e5e7975419fa54fa6995efc75784daf3a8a367f4bce0bb088d87405ce35b6c3e3f28b96c1517fee44068aebc3707988dcafce19c825c4573a68ff3
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-process@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/credential-provider-process@npm:3.873.0"
  dependencies:
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/shared-ini-file-loader": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d1502eb5af899dfa34dc7bf5f57702558b1483a1db17e5cc9191bdd784dd01f0cbd09a4e9c0bcff30c75ac903d043aecf774a546cf507f97cf658e617ab7ffeb
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-sso@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/credential-provider-sso@npm:3.873.0"
  dependencies:
    "@aws-sdk/client-sso": "npm:3.873.0"
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/token-providers": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/shared-ini-file-loader": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6ec9c3b956ea24455045e400be8defcfb232fd4826664a3e7eb81540daedbc8a1c0ded2c77e94d2ae39e2613118f513ca71b55e126ee4ec157c4155b4cd9d653
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-web-identity@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/credential-provider-web-identity@npm:3.873.0"
  dependencies:
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/nested-clients": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d0a205a595face1e568d34448d7eeed5c39dc1b1c6d8d4a293ac70ac0f90e48837c9ec3c55320a403989ac8b5a831b8f7a78429d3843239fbfcc1e27be4fcdba
  languageName: node
  linkType: hard

"@aws-sdk/middleware-bucket-endpoint@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/middleware-bucket-endpoint@npm:3.873.0"
  dependencies:
    "@aws-sdk/types": "npm:3.862.0"
    "@aws-sdk/util-arn-parser": "npm:3.873.0"
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-config-provider": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/08b8c7204ab12f9ee877d2cdd1f8422bdf253731fcb1331c880f4d69872c15da0e076e996739e3d8ebb33f6da76da0d3ea42f4eca95955a7c5a004ee023ab284
  languageName: node
  linkType: hard

"@aws-sdk/middleware-expect-continue@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/middleware-expect-continue@npm:3.873.0"
  dependencies:
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/738e1f33b47f91dc5b3cc8a2564e47d72b9e3add99e9336128545303fa01cf96c4b6a75bee5e477e6369fbe2ae4c7449aa754a246d5c0027c51c9e1bd7482bec
  languageName: node
  linkType: hard

"@aws-sdk/middleware-flexible-checksums@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/middleware-flexible-checksums@npm:3.873.0"
  dependencies:
    "@aws-crypto/crc32": "npm:5.2.0"
    "@aws-crypto/crc32c": "npm:5.2.0"
    "@aws-crypto/util": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/is-array-buffer": "npm:^4.0.0"
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-middleware": "npm:^4.0.5"
    "@smithy/util-stream": "npm:^4.2.4"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/4286a68d03157e98e84ca9bec530ef1c734c654984485db778a322749cf787af3f273eedaf38a7e43a72c4a538d22035b68c8b7cb562adaae798d5b0e054de8e
  languageName: node
  linkType: hard

"@aws-sdk/middleware-host-header@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/middleware-host-header@npm:3.873.0"
  dependencies:
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/177f42c4342666ba8270a3a0f5de16e713b7245b8b5435f1b4863d46cbad9ea8930d23d9731d79ffc0b960be7f70a96693fa0b524a2dd24ca61d1ce8873c9b55
  languageName: node
  linkType: hard

"@aws-sdk/middleware-location-constraint@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/middleware-location-constraint@npm:3.873.0"
  dependencies:
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/a76c3df86ba5ac393c9cfd4c4ff9db8b486ea299b5f27f73e12b8d73700bddd87b0c826b380c3ba02208f2e1f98aa7e0086109143ba984f2f96e0bf0cbc64d25
  languageName: node
  linkType: hard

"@aws-sdk/middleware-logger@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/middleware-logger@npm:3.873.0"
  dependencies:
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/12fbb3c98a5873807834d983189470cee8371ca34b9ba3bdca31e3a81c011b35be260278ae8b3baf64385d4741c001d0749c2686f0747f64a801825084351efb
  languageName: node
  linkType: hard

"@aws-sdk/middleware-recursion-detection@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/middleware-recursion-detection@npm:3.873.0"
  dependencies:
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ce8b10a9da07faf41246c581d342ac3a8e4373bd8c1e07e131573575fed8d6d379e15b4d8f0efebd84936bf4fc800ae7719e36b50931bcf4df2ac547bde31f61
  languageName: node
  linkType: hard

"@aws-sdk/middleware-sdk-s3@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/middleware-sdk-s3@npm:3.873.0"
  dependencies:
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@aws-sdk/util-arn-parser": "npm:3.873.0"
    "@smithy/core": "npm:^3.8.0"
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/signature-v4": "npm:^5.1.3"
    "@smithy/smithy-client": "npm:^4.4.10"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.5"
    "@smithy/util-stream": "npm:^4.2.4"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/8bac428749123994b04e51ba2893a04e53022933a21fed4abbcef8f013014f7f1673fecb490285a123c484fa3e7c4d1e64fb8c679e6d1f259a1ecc03e0e553ef
  languageName: node
  linkType: hard

"@aws-sdk/middleware-ssec@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/middleware-ssec@npm:3.873.0"
  dependencies:
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/87f0ecfff55b922c61016b802c5c41a82222beb072b66dee0bc9bb422a1297c92444444a25b3cf4ea416a1be3cd85401beebc929e6661473f778846685b95c35
  languageName: node
  linkType: hard

"@aws-sdk/middleware-user-agent@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/middleware-user-agent@npm:3.873.0"
  dependencies:
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@aws-sdk/util-endpoints": "npm:3.873.0"
    "@smithy/core": "npm:^3.8.0"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/4cdd2d08f206099900dff674eda0a115e09b8a809d9a420baf720cc096949d82ca9f6e9f78d2a06534af9a7be3b59ac126f200ccd5dc61699fa1371422184e14
  languageName: node
  linkType: hard

"@aws-sdk/nested-clients@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/nested-clients@npm:3.873.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/middleware-host-header": "npm:3.873.0"
    "@aws-sdk/middleware-logger": "npm:3.873.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.873.0"
    "@aws-sdk/middleware-user-agent": "npm:3.873.0"
    "@aws-sdk/region-config-resolver": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@aws-sdk/util-endpoints": "npm:3.873.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.873.0"
    "@aws-sdk/util-user-agent-node": "npm:3.873.0"
    "@smithy/config-resolver": "npm:^4.1.5"
    "@smithy/core": "npm:^3.8.0"
    "@smithy/fetch-http-handler": "npm:^5.1.1"
    "@smithy/hash-node": "npm:^4.0.5"
    "@smithy/invalid-dependency": "npm:^4.0.5"
    "@smithy/middleware-content-length": "npm:^4.0.5"
    "@smithy/middleware-endpoint": "npm:^4.1.18"
    "@smithy/middleware-retry": "npm:^4.1.19"
    "@smithy/middleware-serde": "npm:^4.0.9"
    "@smithy/middleware-stack": "npm:^4.0.5"
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/node-http-handler": "npm:^4.1.1"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/smithy-client": "npm:^4.4.10"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/url-parser": "npm:^4.0.5"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.26"
    "@smithy/util-defaults-mode-node": "npm:^4.0.26"
    "@smithy/util-endpoints": "npm:^3.0.7"
    "@smithy/util-middleware": "npm:^4.0.5"
    "@smithy/util-retry": "npm:^4.0.7"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/1adfee407ce16b5cd9b8087d2514d3b42df07589300f775dedaecbeb7b485940df4846a4795b9e34269e8cde1e2a6a7aefe8dd9d7c67506d72b11db1c0785d4c
  languageName: node
  linkType: hard

"@aws-sdk/region-config-resolver@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/region-config-resolver@npm:3.873.0"
  dependencies:
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.5"
    tslib: "npm:^2.6.2"
  checksum: 10c0/5d2141beaafcc2cf56fe8c92efb176f773de1fbaaac9645fb26ca4478648c0bc440b73bbe8a9837f1843d66bdb513d1ad6e649ffbbd03dc3dae9eeb06c318622
  languageName: node
  linkType: hard

"@aws-sdk/signature-v4-multi-region@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/signature-v4-multi-region@npm:3.873.0"
  dependencies:
    "@aws-sdk/middleware-sdk-s3": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/signature-v4": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/bf39a7902bec6f66f3881b0a426b57253959e808da46e6870e14897e357ac995c387b0bd00790bfea7725a1ed2940dea54b7174789b22a4d0faa63b79f963afd
  languageName: node
  linkType: hard

"@aws-sdk/token-providers@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/token-providers@npm:3.873.0"
  dependencies:
    "@aws-sdk/core": "npm:3.873.0"
    "@aws-sdk/nested-clients": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/shared-ini-file-loader": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/fb56b26781c3b65e23149ee406d2e45b85d41fdf28b51d4f26762c09137d4c1b3a3726fd28a530b59d9b4d039917ecab235d8865ce9cabe0d1141d207115a16b
  languageName: node
  linkType: hard

"@aws-sdk/types@npm:3.862.0, @aws-sdk/types@npm:^3.222.0":
  version: 3.862.0
  resolution: "@aws-sdk/types@npm:3.862.0"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d8e13eadde27c29e39d8effa861a3dc8ef43fba6ecb9772e3461619a76897873c8d4355be89aa5090294d1f17e1a6697834f0bbf6a7f73902a77fe00b1fbe5c2
  languageName: node
  linkType: hard

"@aws-sdk/util-arn-parser@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/util-arn-parser@npm:3.873.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/d99aa771464ca63d0cc737fad34ac40be2b1fecbfa0eeb3aea0b59be6a5e628c24be2847490e8786ed782b818b9248a7b0391d988f2914b3a415f551cb5d1b93
  languageName: node
  linkType: hard

"@aws-sdk/util-endpoints@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/util-endpoints@npm:3.873.0"
  dependencies:
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/url-parser": "npm:^4.0.5"
    "@smithy/util-endpoints": "npm:^3.0.7"
    tslib: "npm:^2.6.2"
  checksum: 10c0/663b882c69400f6473d5d3acd7c337031412bc86d2edd60a1f3faa40c575a79f0a2927b03b0b059453c43cc818e05fb6da0e4b8ab7f960d1c9d2a2b7de952a4d
  languageName: node
  linkType: hard

"@aws-sdk/util-locate-window@npm:^3.0.0":
  version: 3.873.0
  resolution: "@aws-sdk/util-locate-window@npm:3.873.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/b72af4921c5f036bd9aeb1b7a40d66c6cc317a9b5d6e249ef296bbbb2402f262139df03e21a5300b941d6914d879742911841cf2189c8b63df4fd2f8e0c76cc2
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-browser@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/util-user-agent-browser@npm:3.873.0"
  dependencies:
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/types": "npm:^4.3.2"
    bowser: "npm:^2.11.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/a65177d5b27632ebeb4e922ee3a7ab1bce06d8120a9ac33343900125be67498837f0df8495008df6144ef7b687c095477016dba54d40be11397b6a418a4f81ef
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-node@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/util-user-agent-node@npm:3.873.0"
  dependencies:
    "@aws-sdk/middleware-user-agent": "npm:3.873.0"
    "@aws-sdk/types": "npm:3.862.0"
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  peerDependencies:
    aws-crt: ">=1.0.0"
  peerDependenciesMeta:
    aws-crt:
      optional: true
  checksum: 10c0/84e5a97d5d4b2a3e86e8521d0a6deec02ef990841024cdc5de0bd1585fcd2ae58c140f68e22a28b625e9f4900035c526f9ec8cb65078852fae5272690583cf25
  languageName: node
  linkType: hard

"@aws-sdk/xml-builder@npm:3.873.0":
  version: 3.873.0
  resolution: "@aws-sdk/xml-builder@npm:3.873.0"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/f58d49c1bbee2fb8a225b23ff7c26e84264df657d55cf7ca204f07f583603cbb75be356a5ef576810f9a1fddfe133d556ead0a39349cdb94e28bcaae6c279128
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/aix-ppc64@npm:0.25.9"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/android-arm64@npm:0.25.9"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/android-arm@npm:0.25.9"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/android-x64@npm:0.25.9"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/darwin-arm64@npm:0.25.9"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/darwin-x64@npm:0.25.9"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/freebsd-arm64@npm:0.25.9"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/freebsd-x64@npm:0.25.9"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-arm64@npm:0.25.9"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-arm@npm:0.25.9"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-ia32@npm:0.25.9"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-loong64@npm:0.25.9"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-mips64el@npm:0.25.9"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-ppc64@npm:0.25.9"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-riscv64@npm:0.25.9"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-s390x@npm:0.25.9"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/linux-x64@npm:0.25.9"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/netbsd-arm64@npm:0.25.9"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/netbsd-x64@npm:0.25.9"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/openbsd-arm64@npm:0.25.9"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/openbsd-x64@npm:0.25.9"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openharmony-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/openharmony-arm64@npm:0.25.9"
  conditions: os=openharmony & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/sunos-x64@npm:0.25.9"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/win32-arm64@npm:0.25.9"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/win32-ia32@npm:0.25.9"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.9":
  version: 0.25.9
  resolution: "@esbuild/win32-x64@npm:0.25.9"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@ioredis/commands@npm:^1.3.0":
  version: 1.3.0
  resolution: "@ioredis/commands@npm:1.3.0"
  checksum: 10c0/5ab990a8f69c20daf3d7d64307aa9f13ee727c92ab4c7664a6943bb500227667a0c368892e9c4913f06416377db47dba78d58627fe723da476d25f2c04a6d5aa
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-darwin-arm64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-darwin-arm64@npm:3.0.3"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-darwin-x64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-darwin-x64@npm:3.0.3"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-arm64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-linux-arm64@npm:3.0.3"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-arm@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-linux-arm@npm:3.0.3"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-x64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-linux-x64@npm:3.0.3"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-win32-x64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-win32-x64@npm:3.0.3"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@smithy/abort-controller@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/abort-controller@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/0a16d5571f5aa3d6d43465ce1060263a92c6eba011cf448adaeafb940121981ecb26fabb0185745520cace9dfd9aebe6879930ff3b55c8f1b42ac6a337070f20
  languageName: node
  linkType: hard

"@smithy/chunked-blob-reader-native@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/chunked-blob-reader-native@npm:4.0.0"
  dependencies:
    "@smithy/util-base64": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/4387f4e8841f20c1c4e689078141de7e6f239e7883be3a02810a023aa30939b15576ee00227b991972d2c5a2f3b6152bcaeca0975c9fa8d3669354c647bd532a
  languageName: node
  linkType: hard

"@smithy/chunked-blob-reader@npm:^5.0.0":
  version: 5.0.0
  resolution: "@smithy/chunked-blob-reader@npm:5.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/55ba0fe366ddaa3f93e1faf8a70df0b67efedbd0008922295efe215df09b68df0ba3043293e65b17e7d1be71448d074c2bfc54e5eb6bd18f59b425822c2b9e9a
  languageName: node
  linkType: hard

"@smithy/config-resolver@npm:^4.1.5":
  version: 4.1.5
  resolution: "@smithy/config-resolver@npm:4.1.5"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.5"
    tslib: "npm:^2.6.2"
  checksum: 10c0/f76f2365403411810a205763a6744eb84d4edfc6bedb87ba0d41b4b310b9c693f3cb17610f963f706b06e90c12864fe54617c9ff1f435fe3b94d825f2def2bfb
  languageName: node
  linkType: hard

"@smithy/core@npm:^3.8.0":
  version: 3.8.0
  resolution: "@smithy/core@npm:3.8.0"
  dependencies:
    "@smithy/middleware-serde": "npm:^4.0.9"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.5"
    "@smithy/util-stream": "npm:^4.2.4"
    "@smithy/util-utf8": "npm:^4.0.0"
    "@types/uuid": "npm:^9.0.1"
    tslib: "npm:^2.6.2"
    uuid: "npm:^9.0.1"
  checksum: 10c0/0fe1c19b0a2f371ed04b47e51edac896ed24d868a3f78290ea8913e255fef7d023a9c0ba252f5af2b606bfadfdca7fbc545db01dcd0d2162c228d10b2eadc303
  languageName: node
  linkType: hard

"@smithy/credential-provider-imds@npm:^4.0.7":
  version: 4.0.7
  resolution: "@smithy/credential-provider-imds@npm:4.0.7"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/url-parser": "npm:^4.0.5"
    tslib: "npm:^2.6.2"
  checksum: 10c0/862ac40520e2756918e8ecdf2259ec82f1b1556595b3b8d19d7c68390119c416fdd9c716c78773a2ccec21c32cb81f465e0474073a8a90808e171fbdcdcfbd81
  languageName: node
  linkType: hard

"@smithy/eventstream-codec@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/eventstream-codec@npm:4.0.5"
  dependencies:
    "@aws-crypto/crc32": "npm:5.2.0"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d94928e22468cb6e6d09bdc8a6ee04f05947c141c0b040aa90e95b6edc123ba03a562ff3994b5827c57295981183325ed8e8f6c60448a4eec392227735e86d62
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-browser@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/eventstream-serde-browser@npm:4.0.5"
  dependencies:
    "@smithy/eventstream-serde-universal": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/352c6b73482d844f8184d6e6ffd28b1f69b376b59a3246a0ab967c990c15b11507323ff6362b27478062865e131739b27bafa7c6dedfa4c52ac8d795ce0cf8f5
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-config-resolver@npm:^4.1.3":
  version: 4.1.3
  resolution: "@smithy/eventstream-serde-config-resolver@npm:4.1.3"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/bfe98977649bcbfbe93cdbfb118c363759da6910ca8fa462870427dbc6f1f2f835103831147eee7eef27ace1bc58b65a40969c4630cf76be1eedfcd36996fd28
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-node@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/eventstream-serde-node@npm:4.0.5"
  dependencies:
    "@smithy/eventstream-serde-universal": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/21c389202d2db2bcff23166b220ff3ba6c178f2b9eff1918317cca24c49cb6e0a53ab7f43fb8039f776f63ffd3a2bf69c909dafa5199d5d4278797cb121d3daa
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-universal@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/eventstream-serde-universal@npm:4.0.5"
  dependencies:
    "@smithy/eventstream-codec": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/183c6d4895395bbea34d12d3a87e9c69cc598c19e0cf51ba0036277ab520230fdc4b27026d1e438304b3ac4624a4408df15fc037f5c6a96bec0c71c400711170
  languageName: node
  linkType: hard

"@smithy/fetch-http-handler@npm:^5.1.1":
  version: 5.1.1
  resolution: "@smithy/fetch-http-handler@npm:5.1.1"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/querystring-builder": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-base64": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/c07f5cad58d5da7cd0de95e2d600e8dee8cda54bba65e7327c5beb25d2aa3eb815d228944bf20860de8927068d3d80baa28f71ecee0a1a3e131307774f53813b
  languageName: node
  linkType: hard

"@smithy/hash-blob-browser@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/hash-blob-browser@npm:4.0.5"
  dependencies:
    "@smithy/chunked-blob-reader": "npm:^5.0.0"
    "@smithy/chunked-blob-reader-native": "npm:^4.0.0"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d44a12847dabe3ba33b52306ef8f0f61ed22943d12a1fe2d669a5425d9e7b97fc1fd52b78bd362ba56a24c8f0723630e579f2de5362cddc9c8060854150545d9
  languageName: node
  linkType: hard

"@smithy/hash-node@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/hash-node@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6c5aeba12b651d74fa05e03b7019d48193b0fac4995ad84fe313961c4e51d16cdbe46f529a3fe435a061fbe7eebee0620def92f9821add28e466152fd3270560
  languageName: node
  linkType: hard

"@smithy/hash-stream-node@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/hash-stream-node@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/19c7ce086eb86c3a8660a587c70278ab8e201aed10157e6c2f1a7d8071967429bf70b3cd6e81fb29042b3c3ffcf40866bc94ada34b86a1688dc28527884f10de
  languageName: node
  linkType: hard

"@smithy/invalid-dependency@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/invalid-dependency@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/8cc2a14dc47ac5513641747297e6e7e79dceb687e962e1520949db94597a5ce057f9f92657530b6660df100ef1fcff04cd5d9638847c8ada7f7b431a73f34fd2
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/is-array-buffer@npm:2.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/2f2523cd8cc4538131e408eb31664983fecb0c8724956788b015aaf3ab85a0c976b50f4f09b176f1ed7bbe79f3edf80743be7a80a11f22cd9ce1285d77161aaf
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/is-array-buffer@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/ae393fbd5944d710443cd5dd225d1178ef7fb5d6259c14f3e1316ec75e401bda6cf86f7eb98bfd38e5ed76e664b810426a5756b916702cbd418f0933e15e7a3b
  languageName: node
  linkType: hard

"@smithy/md5-js@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/md5-js@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/30f015e5846963189aef4204029506c145e7c6106983bf0cb0ace88fcfc90c8a97094d65b73a79a86406bfcf70932bab9b7a6074fcac481afd0e078955bc95eb
  languageName: node
  linkType: hard

"@smithy/middleware-content-length@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/middleware-content-length@npm:4.0.5"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/2bbe3afc2d29bf4153afb52adb2cadc063e745c2e1f3c630ff10bb97ce4fa8ae7e6872082ec1407b638d0c7cb896ebcc27ca190f9aa78635a8e41a2440fe680a
  languageName: node
  linkType: hard

"@smithy/middleware-endpoint@npm:^4.1.18":
  version: 4.1.18
  resolution: "@smithy/middleware-endpoint@npm:4.1.18"
  dependencies:
    "@smithy/core": "npm:^3.8.0"
    "@smithy/middleware-serde": "npm:^4.0.9"
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/shared-ini-file-loader": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/url-parser": "npm:^4.0.5"
    "@smithy/util-middleware": "npm:^4.0.5"
    tslib: "npm:^2.6.2"
  checksum: 10c0/22a6e05e427c9899041facefea8bdf8dad393bdb3ccd7ca795fb705e85ee8b9e48c6000e947bb6a8a1cfe48d1f1f1b9f894f0b588e87ce1ea5b187d041bcd6fe
  languageName: node
  linkType: hard

"@smithy/middleware-retry@npm:^4.1.19":
  version: 4.1.19
  resolution: "@smithy/middleware-retry@npm:4.1.19"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/service-error-classification": "npm:^4.0.7"
    "@smithy/smithy-client": "npm:^4.4.10"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-middleware": "npm:^4.0.5"
    "@smithy/util-retry": "npm:^4.0.7"
    "@types/uuid": "npm:^9.0.1"
    tslib: "npm:^2.6.2"
    uuid: "npm:^9.0.1"
  checksum: 10c0/6595d27404491ee3befc69ffe8ce576f26b409385d6958597c8d889fff7aff26973a54eab605348299c24760912d9606f7efe84e3adf72ab146b114096592bec
  languageName: node
  linkType: hard

"@smithy/middleware-serde@npm:^4.0.9":
  version: 4.0.9
  resolution: "@smithy/middleware-serde@npm:4.0.9"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/71dc9d920d36a3f65cc883718e8c74687a7c8074a148ab1a035e395e43c6566a3514f10b4c15a13b98194ecd1d81816932c9df8dfa5955cd347c6049893defc4
  languageName: node
  linkType: hard

"@smithy/middleware-stack@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/middleware-stack@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/2ebe346b8b868d11bf9e5028a225ad1312f7862231ae01c289059291b984127a7c18e17f1fa4d803de09f77441d839bc5e25f8ec9bed10a9a320d0393bc55930
  languageName: node
  linkType: hard

"@smithy/node-config-provider@npm:^4.1.4":
  version: 4.1.4
  resolution: "@smithy/node-config-provider@npm:4.1.4"
  dependencies:
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/shared-ini-file-loader": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/950f9e234b8ffb680d2f5b35bc7ff21f73623caf0612d59daba1991da79126ec33e1afd2f6408534b7910474665ab150bd9d341aa46950bf5903665e71c7da6f
  languageName: node
  linkType: hard

"@smithy/node-http-handler@npm:^4.1.1":
  version: 4.1.1
  resolution: "@smithy/node-http-handler@npm:4.1.1"
  dependencies:
    "@smithy/abort-controller": "npm:^4.0.5"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/querystring-builder": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/a61a841bc6e69c62a983031e8b3faf1ab82abaf0ccd1eb5d3e02e3d99a8be020fa8dff0b2b1f81468db43e0e7be2407785b89e9c6c04035b8b4afde08bed3a98
  languageName: node
  linkType: hard

"@smithy/property-provider@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/property-provider@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/67b828f4ddfb90a90e8a919328bb3c842612115d84d949087988fd8558cd143ec8f7dc437936ef41f9427a7ea2a6ec6a726c5f92a9c12e8c7bef831c4b4f16f0
  languageName: node
  linkType: hard

"@smithy/protocol-http@npm:^5.1.3":
  version: 5.1.3
  resolution: "@smithy/protocol-http@npm:5.1.3"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/5adc1e69b9e2d7c90acfe1a9b731c4f233173e035eb9e8e3dd5fabf63d9a765aff54912a0e94f4f4bff494f4caa9ec40bd53cdc1a94028f561ab5c9649f2790f
  languageName: node
  linkType: hard

"@smithy/querystring-builder@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/querystring-builder@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-uri-escape": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/649a046a14f25d5febba341dedd577c9fce80aa86970dc2af0b0289a2b6326731c19ddefcae172a0162a4a73306ad823533528751a0067c910efce3cabe06675
  languageName: node
  linkType: hard

"@smithy/querystring-parser@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/querystring-parser@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/e12a2e19137bc95487c51652dd20f6cd0199854986019e5461f14f797fda3cda3ec4786ff45af853aa64ab75a2a91d18f3f8320276f7e407016f80e33604564d
  languageName: node
  linkType: hard

"@smithy/service-error-classification@npm:^4.0.7":
  version: 4.0.7
  resolution: "@smithy/service-error-classification@npm:4.0.7"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
  checksum: 10c0/fe44ce36c8759c74a63adc52c47b638ee0a34ea32752d9c5923c370f0497a412ced51d8b83e444303d8d9d544d30d3d16fecb39c9f5cda8622b293704ce999a2
  languageName: node
  linkType: hard

"@smithy/shared-ini-file-loader@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/shared-ini-file-loader@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/9fafb7d4cf10398cf07a2ad7b619b05f136a2a774b1d104eb43b1862f1297d1f88f7e6d72198df43bef35cdf5938b8b5bcf0e896a8bb406474920d0f653a0a4b
  languageName: node
  linkType: hard

"@smithy/signature-v4@npm:^5.1.3":
  version: 5.1.3
  resolution: "@smithy/signature-v4@npm:5.1.3"
  dependencies:
    "@smithy/is-array-buffer": "npm:^4.0.0"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.5"
    "@smithy/util-uri-escape": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/122a918ee070215e5cea8040605d905143a724b5bb0e5c904085f7a7a4b3d87701e5674b39cc8c9e13639b077994739edcdf33c3884172f363bcf68071c2abc7
  languageName: node
  linkType: hard

"@smithy/smithy-client@npm:^4.4.10":
  version: 4.4.10
  resolution: "@smithy/smithy-client@npm:4.4.10"
  dependencies:
    "@smithy/core": "npm:^3.8.0"
    "@smithy/middleware-endpoint": "npm:^4.1.18"
    "@smithy/middleware-stack": "npm:^4.0.5"
    "@smithy/protocol-http": "npm:^5.1.3"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-stream": "npm:^4.2.4"
    tslib: "npm:^2.6.2"
  checksum: 10c0/994743c7a04e3e1b5136c3be98c3882ab9169d39143530c11553062934887b6b9b7d32de035a15f7ff0f7e6b5db6106ab3e71dc62beb473da9313ff6b8b24a37
  languageName: node
  linkType: hard

"@smithy/types@npm:^4.3.2":
  version: 4.3.2
  resolution: "@smithy/types@npm:4.3.2"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/120c5d38f6362c86e6493cce3b9ca9902cd986dab773b39664ff6a95b787c45481f1b1d230f45a6f5ad0c045fb690dc96b51b9ca7b5e9487714a652ed98231f6
  languageName: node
  linkType: hard

"@smithy/url-parser@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/url-parser@npm:4.0.5"
  dependencies:
    "@smithy/querystring-parser": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/19cb3c8a80a7a42936d47011e5991cee6d548f233cde2bf36ccb6c547d075bbc30e3be67e92f60aaf17c4f3875766be319a3da8399af40767a77b04aea3d9ee5
  languageName: node
  linkType: hard

"@smithy/util-base64@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-base64@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ad18ec66cc357c189eef358d96876b114faf7086b13e47e009b265d0ff80cec046052500489c183957b3a036768409acdd1a373e01074cc002ca6983f780cffc
  languageName: node
  linkType: hard

"@smithy/util-body-length-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-browser@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/574a10934024a86556e9dcde1a9776170284326c3dfcc034afa128cc5a33c1c8179fca9cfb622ef8be5f2004316cc3f427badccceb943e829105536ec26306d9
  languageName: node
  linkType: hard

"@smithy/util-body-length-node@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-node@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/e91fd3816767606c5f786166ada26440457fceb60f96653b3d624dcf762a8c650e513c275ff3f647cb081c63c283cc178853a7ed9aa224abc8ece4eeeef7a1dd
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/util-buffer-from@npm:2.2.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^2.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/223d6a508b52ff236eea01cddc062b7652d859dd01d457a4e50365af3de1e24a05f756e19433f6ccf1538544076b4215469e21a4ea83dc1d58d829725b0dbc5a
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-buffer-from@npm:4.0.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/be7cd33b6cb91503982b297716251e67cdca02819a15797632091cadab2dc0b4a147fff0709a0aa9bbc0b82a2644a7ed7c8afdd2194d5093cee2e9605b3a9f6f
  languageName: node
  linkType: hard

"@smithy/util-config-provider@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-config-provider@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/cd9498d5f77a73aadd575084bcb22d2bb5945bac4605d605d36f2efe3f165f2b60f4dc88b7a62c2ed082ffa4b2c2f19621d0859f18399edbc2b5988d92e4649f
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-browser@npm:^4.0.26":
  version: 4.0.26
  resolution: "@smithy/util-defaults-mode-browser@npm:4.0.26"
  dependencies:
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/smithy-client": "npm:^4.4.10"
    "@smithy/types": "npm:^4.3.2"
    bowser: "npm:^2.11.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ba10af21bd302f4705a808673eb3811e36a78c396f7ee93e2dfea5ded7d78470c789d3bc7a23e3d6232b43b7b91f57fbfbd383d11042e6993dc9c49030cbd0ef
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-node@npm:^4.0.26":
  version: 4.0.26
  resolution: "@smithy/util-defaults-mode-node@npm:4.0.26"
  dependencies:
    "@smithy/config-resolver": "npm:^4.1.5"
    "@smithy/credential-provider-imds": "npm:^4.0.7"
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/property-provider": "npm:^4.0.5"
    "@smithy/smithy-client": "npm:^4.4.10"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/0a682393db1617681fc132c39d9f01accd5c3c250be457ebb514001d83d34252d404fe6315ee0cc5176e0efc7fdeec64e848299bdefe6113d3c70f81717b665b
  languageName: node
  linkType: hard

"@smithy/util-endpoints@npm:^3.0.7":
  version: 3.0.7
  resolution: "@smithy/util-endpoints@npm:3.0.7"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.4"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/7024005a8a4f77ebae52d1dce538d76db3567c6fb22b06ba601dba4d4d8668cb4dbadd229015d02bb6bdb1a5aaa6b2d1c826cfcf412257ceb9dfe52c7ab95fca
  languageName: node
  linkType: hard

"@smithy/util-hex-encoding@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-hex-encoding@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/70dbb3aa1a79aff3329d07a66411ff26398df338bdd8a6d077b438231afe3dc86d9a7022204baddecd8bc633f059d5c841fa916d81dd7447ea79b64148f386d2
  languageName: node
  linkType: hard

"@smithy/util-middleware@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/util-middleware@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/74d9bdbcea4c4aa5304197417c370346b230b7a89893ba0dee0d9771b6ead2628a53fb8a64a3822bf1a30a176ebba2c16ece7003c21880a7ff54be0955356606
  languageName: node
  linkType: hard

"@smithy/util-retry@npm:^4.0.7":
  version: 4.0.7
  resolution: "@smithy/util-retry@npm:4.0.7"
  dependencies:
    "@smithy/service-error-classification": "npm:^4.0.7"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/09c633f59ac51203d917548ceb4caf7678e24c87eea024e97e8d62a918be4a76a1c517622b7e9841cf0e9f50778d6787f62efe6c25ae514ed7068e2323303c72
  languageName: node
  linkType: hard

"@smithy/util-stream@npm:^4.2.4":
  version: 4.2.4
  resolution: "@smithy/util-stream@npm:4.2.4"
  dependencies:
    "@smithy/fetch-http-handler": "npm:^5.1.1"
    "@smithy/node-http-handler": "npm:^4.1.1"
    "@smithy/types": "npm:^4.3.2"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/45d2945656a68822272eb5e37e447bd161861722d841712d087cc0aaf93ad0da8162eef2164d1a35f55a7124cb8815b357b766c21442b23ea972b1d5345f0526
  languageName: node
  linkType: hard

"@smithy/util-uri-escape@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-uri-escape@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/23984624060756adba8aa4ab1693fe6b387ee5064d8ec4dfd39bb5908c4ee8b9c3f2dc755da9b07505d8e3ce1338c1867abfa74158931e4728bf3cfcf2c05c3d
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^2.0.0":
  version: 2.3.0
  resolution: "@smithy/util-utf8@npm:2.3.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^2.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/e18840c58cc507ca57fdd624302aefd13337ee982754c9aa688463ffcae598c08461e8620e9852a424d662ffa948fc64919e852508028d09e89ced459bd506ab
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-utf8@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/28a5a5372cbf0b3d2e32dd16f79b04c2aec6f704cf13789db922e9686fde38dde0171491cfa4c2c201595d54752a319faaeeed3c325329610887694431e28c98
  languageName: node
  linkType: hard

"@smithy/util-waiter@npm:^4.0.7":
  version: 4.0.7
  resolution: "@smithy/util-waiter@npm:4.0.7"
  dependencies:
    "@smithy/abort-controller": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/14caffd913b9b18ff4f33d6bb1f05eef2e354104a6db2b69654d7db4582c4be46b202d46af87a66177a8a3a99082fa8b0948195de8aeb63998c6ed5b04f2bd3d
  languageName: node
  linkType: hard

"@types/fluent-ffmpeg@npm:^2.1.24":
  version: 2.1.27
  resolution: "@types/fluent-ffmpeg@npm:2.1.27"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/2362e7d240d4d8a0b775b5abf9ab5f26a85b17dc335276f0c3ff3bf935f913ccc9e1e10e756a6a3cebf78c1cd1eff835a253f73a3cd1bbcb343b28089a9957f3
  languageName: node
  linkType: hard

"@types/fs-extra@npm:^11.0.4":
  version: 11.0.4
  resolution: "@types/fs-extra@npm:11.0.4"
  dependencies:
    "@types/jsonfile": "npm:*"
    "@types/node": "npm:*"
  checksum: 10c0/9e34f9b24ea464f3c0b18c3f8a82aefc36dc524cc720fc2b886e5465abc66486ff4e439ea3fb2c0acebf91f6d3f74e514f9983b1f02d4243706bdbb7511796ad
  languageName: node
  linkType: hard

"@types/jsonfile@npm:*":
  version: 6.1.4
  resolution: "@types/jsonfile@npm:6.1.4"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/b12d068b021e4078f6ac4441353965769be87acf15326173e2aea9f3bf8ead41bd0ad29421df5bbeb0123ec3fc02eb0a734481d52903704a1454a1845896b9eb
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 24.3.0
  resolution: "@types/node@npm:24.3.0"
  dependencies:
    undici-types: "npm:~7.10.0"
  checksum: 10c0/96bdeca01f690338957c2dcc92cb9f76c262c10398f8d91860865464412b0f9d309c24d9b03d0bdd26dd47fa7ee3f8227893d5c89bc2009d919a525a22512030
  languageName: node
  linkType: hard

"@types/node@npm:^20.6.2":
  version: 20.19.11
  resolution: "@types/node@npm:20.19.11"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: 10c0/9eecc4be04f1a8afbb8f8059b322fd0bbceeb02f96669bbaa52fb0b264c2e3269432a8833ada4be7b335e18d6b438b2d2c0274f5b3f54cc2081cb7c5374a6561
  languageName: node
  linkType: hard

"@types/uuid@npm:^9.0.1, @types/uuid@npm:^9.0.8":
  version: 9.0.8
  resolution: "@types/uuid@npm:9.0.8"
  checksum: 10c0/b411b93054cb1d4361919579ef3508a1f12bf15b5fdd97337d3d351bece6c921b52b6daeef89b62340fd73fd60da407878432a1af777f40648cbe53a01723489
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10c0/21ba8f574ea57a3106d6d35623f2c4a9111d9ee3e9a5be47baed46ec2457d2eac46e07a5c4a60186f88cb98abbe3e24f2d4cca70bc2b12f1692523e2209a9ccf
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.4
  resolution: "agent-base@npm:7.1.4"
  checksum: 10c0/c2c9ab7599692d594b6a161559ada307b7a624fa4c7b03e3afdb5a5e31cd0e53269115b620fcab024c5ac6a6f37fa5eb2e004f076ad30f5f7e6b8b671f7b35fe
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.2.0
  resolution: "ansi-regex@npm:6.2.0"
  checksum: 10c0/20a2e55ae9816074a60e6729dbe3daad664cd967fc82acc08b02f5677db84baa688babf940d71f50acbbb184c02459453789705e079f4d521166ae66451de551
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"async@npm:^0.2.9":
  version: 0.2.10
  resolution: "async@npm:0.2.10"
  checksum: 10c0/714d284dc6c3ae59f3e8b347083e32c7657ba4ffc4ff945eb152ad4fb08def27e768992fcd4d9fd3b411c6b42f1541862ac917446bf2a1acfa0f302d1001f7d2
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"autorepurpose-worker@workspace:.":
  version: 0.0.0-use.local
  resolution: "autorepurpose-worker@workspace:."
  dependencies:
    "@aws-sdk/client-s3": "npm:^3.864.0"
    "@types/fluent-ffmpeg": "npm:^2.1.24"
    "@types/fs-extra": "npm:^11.0.4"
    "@types/node": "npm:^20.6.2"
    "@types/uuid": "npm:^9.0.8"
    axios: "npm:^1.7.2"
    bullmq: "npm:^5.58.0"
    dotenv: "npm:^16.5.0"
    fluent-ffmpeg: "npm:^2.1.2"
    fs-extra: "npm:^11.2.0"
    ioredis: "npm:^5.7.0"
    tsx: "npm:^4.20.3"
    typescript: "npm:^5.2.2"
    uuid: "npm:^9.0.1"
  languageName: unknown
  linkType: soft

"axios@npm:^1.7.2":
  version: 1.11.0
  resolution: "axios@npm:1.11.0"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.4"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10c0/5de273d33d43058610e4d252f0963cc4f10714da0bfe872e8ef2cbc23c2c999acc300fd357b6bce0fc84a2ca9bd45740fa6bb28199ce2c1266c8b1a393f2b36e
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"bowser@npm:^2.11.0":
  version: 2.12.1
  resolution: "bowser@npm:2.12.1"
  checksum: 10c0/017e8cc63ce2dec75037340626e1408f68334dac95f953ba7db33a266c019f1d262346d2be3994f9a12b7e9c02f57c562078719b8c5e8e8febe01053c613ffbc
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/6d117a4c793488af86b83172deb6af143e94c17bc53b0b3cec259733923b4ca84679d506ac261f4ba3c7ed37c46018e2ff442f9ce453af8643ecd64f4a54e6cf
  languageName: node
  linkType: hard

"bullmq@npm:^5.58.0":
  version: 5.58.1
  resolution: "bullmq@npm:5.58.1"
  dependencies:
    cron-parser: "npm:^4.9.0"
    ioredis: "npm:^5.4.1"
    msgpackr: "npm:^1.11.2"
    node-abort-controller: "npm:^3.1.1"
    semver: "npm:^7.5.4"
    tslib: "npm:^2.0.0"
    uuid: "npm:^9.0.0"
  checksum: 10c0/1e36ca908bf688f209c05b8f1c011cbe0171874a2b55866d98bdaf970a1d85796225ebe1164fdd938ac00ca5c2e1fcb7301e73407371b2c4a46847f58b32c472
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"cluster-key-slot@npm:^1.1.0":
  version: 1.1.2
  resolution: "cluster-key-slot@npm:1.1.2"
  checksum: 10c0/d7d39ca28a8786e9e801eeb8c770e3c3236a566625d7299a47bb71113fb2298ce1039596acb82590e598c52dbc9b1f088c8f587803e697cb58e1867a95ff94d3
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"cron-parser@npm:^4.9.0":
  version: 4.9.0
  resolution: "cron-parser@npm:4.9.0"
  dependencies:
    luxon: "npm:^3.2.1"
  checksum: 10c0/348622bdcd1a15695b61fc33af8a60133e5913a85cf99f6344367579e7002896514ba3b0a9d6bb569b02667d6b06836722bf2295fcd101b3de378f71d37bed0b
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.3.4":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"denque@npm:^2.1.0":
  version: 2.1.0
  resolution: "denque@npm:2.1.0"
  checksum: 10c0/f9ef81aa0af9c6c614a727cb3bd13c5d7db2af1abf9e6352045b86e85873e629690f6222f4edd49d10e4ccf8f078bbeec0794fafaf61b659c0589d0c511ec363
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.1":
  version: 2.0.4
  resolution: "detect-libc@npm:2.0.4"
  checksum: 10c0/c15541f836eba4b1f521e4eecc28eefefdbc10a94d3b8cb4c507689f332cc111babb95deda66f2de050b22122113189986d5190be97d51b5a2b23b938415e67c
  languageName: node
  linkType: hard

"dotenv@npm:^16.5.0":
  version: 16.6.1
  resolution: "dotenv@npm:16.6.1"
  checksum: 10c0/15ce56608326ea0d1d9414a5c8ee6dcf0fffc79d2c16422b4ac2268e7e2d76ff5a572d37ffe747c377de12005f14b3cc22361e79fc7f1061cce81f77d2c973dc
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"esbuild@npm:~0.25.0":
  version: 0.25.9
  resolution: "esbuild@npm:0.25.9"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.25.9"
    "@esbuild/android-arm": "npm:0.25.9"
    "@esbuild/android-arm64": "npm:0.25.9"
    "@esbuild/android-x64": "npm:0.25.9"
    "@esbuild/darwin-arm64": "npm:0.25.9"
    "@esbuild/darwin-x64": "npm:0.25.9"
    "@esbuild/freebsd-arm64": "npm:0.25.9"
    "@esbuild/freebsd-x64": "npm:0.25.9"
    "@esbuild/linux-arm": "npm:0.25.9"
    "@esbuild/linux-arm64": "npm:0.25.9"
    "@esbuild/linux-ia32": "npm:0.25.9"
    "@esbuild/linux-loong64": "npm:0.25.9"
    "@esbuild/linux-mips64el": "npm:0.25.9"
    "@esbuild/linux-ppc64": "npm:0.25.9"
    "@esbuild/linux-riscv64": "npm:0.25.9"
    "@esbuild/linux-s390x": "npm:0.25.9"
    "@esbuild/linux-x64": "npm:0.25.9"
    "@esbuild/netbsd-arm64": "npm:0.25.9"
    "@esbuild/netbsd-x64": "npm:0.25.9"
    "@esbuild/openbsd-arm64": "npm:0.25.9"
    "@esbuild/openbsd-x64": "npm:0.25.9"
    "@esbuild/openharmony-arm64": "npm:0.25.9"
    "@esbuild/sunos-x64": "npm:0.25.9"
    "@esbuild/win32-arm64": "npm:0.25.9"
    "@esbuild/win32-ia32": "npm:0.25.9"
    "@esbuild/win32-x64": "npm:0.25.9"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/openharmony-arm64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10c0/aaa1284c75fcf45c82f9a1a117fe8dc5c45628e3386bda7d64916ae27730910b51c5aec7dd45a6ba19256be30ba2935e64a8f011a3f0539833071e06bf76d5b3
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"fast-xml-parser@npm:5.2.5":
  version: 5.2.5
  resolution: "fast-xml-parser@npm:5.2.5"
  dependencies:
    strnum: "npm:^2.1.0"
  bin:
    fxparser: src/cli/cli.js
  checksum: 10c0/d1057d2e790c327ccfc42b872b91786a4912a152d44f9507bf053f800102dfb07ece3da0a86b33ff6a0caa5a5cad86da3326744f6ae5efb0c6c571d754fe48cd
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.5.0
  resolution: "fdir@npm:6.5.0"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/e345083c4306b3aed6cb8ec551e26c36bab5c511e99ea4576a16750ddc8d3240e63826cc624f5ae17ad4dc82e68a253213b60d556c11bfad064b7607847ed07f
  languageName: node
  linkType: hard

"fluent-ffmpeg@npm:^2.1.2":
  version: 2.1.3
  resolution: "fluent-ffmpeg@npm:2.1.3"
  dependencies:
    async: "npm:^0.2.9"
    which: "npm:^1.1.1"
  checksum: 10c0/0397379ec3237c10b2389edeef26fdaf93f36d1b20b0f28f8945fe6d9121dcee9b0c615bf7d44edb7abd37233e0d24f0db39389668d3c86a1a2a0d59e3f4457b
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.11
  resolution: "follow-redirects@npm:1.15.11"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/d301f430542520a54058d4aeeb453233c564aaccac835d29d15e050beb33f339ad67d9bddbce01739c5dc46a6716dbe3d9d0d5134b1ca203effa11a7ef092343
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"form-data@npm:^4.0.4":
  version: 4.0.4
  resolution: "form-data@npm:4.0.4"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    hasown: "npm:^2.0.2"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/373525a9a034b9d57073e55eab79e501a714ffac02e7a9b01be1c820780652b16e4101819785e1e18f8d98f0aee866cc654d660a435c378e16a72f2e7cac9695
  languageName: node
  linkType: hard

"fs-extra@npm:^11.2.0":
  version: 11.3.1
  resolution: "fs-extra@npm:11.3.1"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/61e5b7285b1ca72c68dfe1058b2514294a922683afac2a80aa90540f9bd85370763d675e3b408ef500077d355956fece3bd24b546790e261c3d3015967e2b2d9
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.6":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.7.5":
  version: 4.10.1
  resolution: "get-tsconfig@npm:4.10.1"
  dependencies:
    resolve-pkg-maps: "npm:^1.0.0"
  checksum: 10c0/7f8e3dabc6a49b747920a800fb88e1952fef871cdf51b79e98db48275a5de6cdaf499c55ee67df5fa6fe7ce65f0063e26de0f2e53049b408c585aa74d39ffa21
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10c0/45b66a945cf13ec2d1f29432277201313babf4a01d9e52f44b31ca923434083afeca03f18417f599c9ab3d0e7b618ceb21257542338b57c54b710463b4a53e37
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"ioredis@npm:^5.4.1, ioredis@npm:^5.7.0":
  version: 5.7.0
  resolution: "ioredis@npm:5.7.0"
  dependencies:
    "@ioredis/commands": "npm:^1.3.0"
    cluster-key-slot: "npm:^1.1.0"
    debug: "npm:^4.3.4"
    denque: "npm:^2.1.0"
    lodash.defaults: "npm:^4.2.0"
    lodash.isarguments: "npm:^3.1.0"
    redis-errors: "npm:^1.2.0"
    redis-parser: "npm:^3.0.0"
    standard-as-callback: "npm:^2.1.0"
  checksum: 10c0/c63c521a953bfaf29f8c8871b122af38e439328336fa238f83bfbb066556f64daf69ed7a4ec01fc7b9ee1f0862059dd188b8c684150125d362d36642399b30ee
  languageName: node
  linkType: hard

"ip-address@npm:^10.0.1":
  version: 10.0.1
  resolution: "ip-address@npm:10.0.1"
  checksum: 10c0/1634d79dae18394004775cb6d699dc46b7c23df6d2083164025a2b15240c1164fccde53d0e08bd5ee4fc53913d033ab6b5e395a809ad4b956a940c446e948843
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.2.0
  resolution: "jsonfile@npm:6.2.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/7f4f43b08d1869ded8a6822213d13ae3b99d651151d77efd1557ced0889c466296a7d9684e397bd126acf5eb2cfcb605808c3e681d0fdccd2fe5a04b47e76c0d
  languageName: node
  linkType: hard

"lodash.defaults@npm:^4.2.0":
  version: 4.2.0
  resolution: "lodash.defaults@npm:4.2.0"
  checksum: 10c0/d5b77aeb702caa69b17be1358faece33a84497bcca814897383c58b28a2f8dfc381b1d9edbec239f8b425126a3bbe4916223da2a576bb0411c2cefd67df80707
  languageName: node
  linkType: hard

"lodash.isarguments@npm:^3.1.0":
  version: 3.1.0
  resolution: "lodash.isarguments@npm:3.1.0"
  checksum: 10c0/5e8f95ba10975900a3920fb039a3f89a5a79359a1b5565e4e5b4310ed6ebe64011e31d402e34f577eca983a1fc01ff86c926e3cbe602e1ddfc858fdd353e62d8
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"luxon@npm:^3.2.1":
  version: 3.7.1
  resolution: "luxon@npm:3.7.1"
  checksum: 10c0/f83bc23a4c09da9111bc2510d2f5346e1ced4938379ebff13e308fece2ea852eb6e8b9ed8053b8d82e0fce05d5dd46e4cd64d8831ca04cfe32c0954b6f087258
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10c0/9f3bd35e41d40d02469cb30470c55ccc21cae0db40e08d1d0b1dff01cc8cc89a6f78e9c5d2b7c844e485ec0a8abc2238111213fdc5b2038e6d1012eacf316f78
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"msgpackr-extract@npm:^3.0.2":
  version: 3.0.3
  resolution: "msgpackr-extract@npm:3.0.3"
  dependencies:
    "@msgpackr-extract/msgpackr-extract-darwin-arm64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-darwin-x64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-arm": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-arm64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-x64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-win32-x64": "npm:3.0.3"
    node-gyp: "npm:latest"
    node-gyp-build-optional-packages: "npm:5.2.2"
  dependenciesMeta:
    "@msgpackr-extract/msgpackr-extract-darwin-arm64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-darwin-x64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-arm":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-arm64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-x64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-win32-x64":
      optional: true
  bin:
    download-msgpackr-prebuilds: bin/download-prebuilds.js
  checksum: 10c0/e504fd8bf86a29d7527c83776530ee6dc92dcb0273bb3679fd4a85173efead7f0ee32fb82c8410a13c33ef32828c45f81118ffc0fbed5d6842e72299894623b4
  languageName: node
  linkType: hard

"msgpackr@npm:^1.11.2":
  version: 1.11.5
  resolution: "msgpackr@npm:1.11.5"
  dependencies:
    msgpackr-extract: "npm:^3.0.2"
  dependenciesMeta:
    msgpackr-extract:
      optional: true
  checksum: 10c0/f35ffd218661e8afc52490cde3dbf2656304e7940563c5313aa2f45e31ac5bdce3b58f27e55b785c700085ee76f26fc7afbae25ae5abe05068a8f000fd0ac6cd
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"node-abort-controller@npm:^3.1.1":
  version: 3.1.1
  resolution: "node-abort-controller@npm:3.1.1"
  checksum: 10c0/f7ad0e7a8e33809d4f3a0d1d65036a711c39e9d23e0319d80ebe076b9a3b4432b4d6b86a7fab65521de3f6872ffed36fc35d1327487c48eb88c517803403eda3
  languageName: node
  linkType: hard

"node-gyp-build-optional-packages@npm:5.2.2":
  version: 5.2.2
  resolution: "node-gyp-build-optional-packages@npm:5.2.2"
  dependencies:
    detect-libc: "npm:^2.0.1"
  bin:
    node-gyp-build-optional-packages: bin.js
    node-gyp-build-optional-packages-optional: optional.js
    node-gyp-build-optional-packages-test: build-test.js
  checksum: 10c0/c81128c6f91873381be178c5eddcbdf66a148a6a89a427ce2bcd457593ce69baf2a8662b6d22cac092d24aa9c43c230dec4e69b3a0da604503f4777cd77e282b
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.4.1
  resolution: "node-gyp@npm:11.4.1"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/475d5c51ef44cee15668df4ad2946e92ad66397adaae4f695afc080ea7a8812c8d93341c1eabe42a46ee615bbde90123b548c7f61388be48c6b0bbc5ea9c53fe
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.3
  resolution: "picomatch@npm:4.0.3"
  checksum: 10c0/9582c951e95eebee5434f59e426cddd228a7b97a0161a375aed4be244bd3fe8e3a31b846808ea14ef2c8a2527a6eeab7b3946a67d5979e81694654f939473ae2
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10c0/fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"redis-errors@npm:^1.0.0, redis-errors@npm:^1.2.0":
  version: 1.2.0
  resolution: "redis-errors@npm:1.2.0"
  checksum: 10c0/5b316736e9f532d91a35bff631335137a4f974927bb2fb42bf8c2f18879173a211787db8ac4c3fde8f75ed6233eb0888e55d52510b5620e30d69d7d719c8b8a7
  languageName: node
  linkType: hard

"redis-parser@npm:^3.0.0":
  version: 3.0.0
  resolution: "redis-parser@npm:3.0.0"
  dependencies:
    redis-errors: "npm:^1.0.0"
  checksum: 10c0/ee16ac4c7b2a60b1f42a2cdaee22b005bd4453eb2d0588b8a4939718997ae269da717434da5d570fe0b05030466eeb3f902a58cf2e8e1ca058bf6c9c596f632f
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 10c0/fb8f7bbe2ca281a73b7ef423a1cbc786fb244bd7a95cbe5c3fba25b27d327150beca8ba02f622baea65919a57e061eb5005204daa5f93ed590d9b77463a567ab
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.4":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10c0/aca305edfbf2383c22571cb7714f48cadc7ac95371b4b52362fb8eeffdfbc0de0669368b82b2b15978f8848f01d7114da65697e56cd8c37b0dab8c58e543f9ea
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.7
  resolution: "socks@npm:2.8.7"
  dependencies:
    ip-address: "npm:^10.0.1"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/2805a43a1c4bcf9ebf6e018268d87b32b32b06fbbc1f9282573583acc155860dc361500f89c73bfbb157caa1b4ac78059eac0ef15d1811eb0ca75e0bdadbc9d2
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"standard-as-callback@npm:^2.1.0":
  version: 2.1.0
  resolution: "standard-as-callback@npm:2.1.0"
  checksum: 10c0/012677236e3d3fdc5689d29e64ea8a599331c4babe86956bf92fc5e127d53f85411c5536ee0079c52c43beb0026b5ce7aa1d834dd35dd026e82a15d1bcaead1f
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strnum@npm:^2.1.0":
  version: 2.1.1
  resolution: "strnum@npm:2.1.1"
  checksum: 10c0/1f9bd1f9b4c68333f25c2b1f498ea529189f060cd50aa59f1876139c994d817056de3ce57c12c970f80568d75df2289725e218bd9e3cdf73cd1a876c9c102733
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/f789ed6c924287a9b7d3612056ed0cda67306cd2c80c249fd280cf1504742b12583a2089b61f4abbd24605f390809017240e250241f09938054c9b363e51c0a6
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.6.2":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"tsx@npm:^4.20.3":
  version: 4.20.5
  resolution: "tsx@npm:4.20.5"
  dependencies:
    esbuild: "npm:~0.25.0"
    fsevents: "npm:~2.3.3"
    get-tsconfig: "npm:^4.7.5"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    tsx: dist/cli.mjs
  checksum: 10c0/70f9bf746be69281312a369c712902dbf9bcbdd9db9184a4859eb4859c36ef0c5a6d79b935c1ec429158ee73fd6584089400ae8790345dae34c5b0222bdb94f3
  languageName: node
  linkType: hard

"typescript@npm:^5.2.2":
  version: 5.9.2
  resolution: "typescript@npm:5.9.2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/cd635d50f02d6cf98ed42de2f76289701c1ec587a363369255f01ed15aaf22be0813226bff3c53e99d971f9b540e0b3cc7583dbe05faded49b1b0bed2f638a18
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^5.2.2#optional!builtin<compat/typescript>":
  version: 5.9.2
  resolution: "typescript@patch:typescript@npm%3A5.9.2#optional!builtin<compat/typescript>::version=5.9.2&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/34d2a8e23eb8e0d1875072064d5e1d9c102e0bdce56a10a25c0b917b8aa9001a9cf5c225df12497e99da107dc379360bc138163c66b55b95f5b105b50578067e
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 10c0/c01ed51829b10aa72fc3ce64b747f8e74ae9b60eafa19a7b46ef624403508a54c526ffab06a14a26b3120d055e1104d7abe7c9017e83ced038ea5cf52f8d5e04
  languageName: node
  linkType: hard

"undici-types@npm:~7.10.0":
  version: 7.10.0
  resolution: "undici-types@npm:7.10.0"
  checksum: 10c0/8b00ce50e235fe3cc601307f148b5e8fb427092ee3b23e8118ec0a5d7f68eca8cee468c8fc9f15cbb2cf2a3797945ebceb1cbd9732306a1d00e0a9b6afa0f635
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"uuid@npm:^9.0.0, uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/1607dd32ac7fc22f2d8f77051e6a64845c9bce5cd3dd8aa0070c074ec73e666a1f63c7b4e0f4bf2bc8b9d59dc85a15e17807446d9d2b17c8485fbc2147b27f9b
  languageName: node
  linkType: hard

"which@npm:^1.1.1":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    which: ./bin/which
  checksum: 10c0/e945a8b6bbf6821aaaef7f6e0c309d4b615ef35699576d5489b4261da9539f70393c6b2ce700ee4321c18f914ebe5644bc4631b15466ffbaad37d83151f6af59
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard
