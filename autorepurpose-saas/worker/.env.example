
# Redis Configuration (Upstash)
REDIS_URL=rediss://default:your_redis_password@your_redis_host.upstash.io:6379

# AWS S3 Configuration
AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET_NAME=auto-repurpose

# Application Configuration
WORKER_CONCURRENCY=3
WORKER_NAME=video-processor-1
APP_API_URL=https://your-app-domain.com

# Environment
NODE_ENV=production

# Logging
LOG_LEVEL=info

# Processing Settings
MAX_FILE_SIZE_GB=5
TEMP_DIR=/tmp/autorepurpose
OUTPUT_QUALITY=high

# FFmpeg Settings
FFMPEG_PATH=/usr/bin/ffmpeg
FFPROBE_PATH=/usr/bin/ffprobe
