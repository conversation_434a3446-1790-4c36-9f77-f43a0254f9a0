
#!/bin/bash

echo "🚀 Setting up AutoRepurpose Video Processing Worker"
echo "=================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if yarn is installed
if ! command -v yarn &> /dev/null; then
    echo "📦 Installing Yarn package manager..."
    npm install -g yarn
fi

# Install dependencies
echo "📦 Installing worker dependencies..."
yarn install

# Check if FFmpeg is installed
if command -v ffmpeg &> /dev/null; then
    echo "✅ FFmpeg found: $(ffmpeg -version | head -n 1)"
else
    echo "❌ FFmpeg not found. Installing FFmpeg..."
    
    # Detect OS and install FFmpeg
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v apt &> /dev/null; then
            sudo apt update && sudo apt install -y ffmpeg
        elif command -v yum &> /dev/null; then
            sudo yum install -y ffmpeg
        else
            echo "❌ Could not install FFmpeg automatically. Please install it manually."
            exit 1
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install ffmpeg
        else
            echo "❌ Homebrew not found. Please install FFmpeg manually."
            exit 1
        fi
    else
        echo "❌ Unsupported OS. Please install FFmpeg manually."
        exit 1
    fi
fi

# Create temp directory
echo "📁 Creating temp directory..."
mkdir -p /tmp/autorepurpose
chmod 755 /tmp/autorepurpose

# Check environment file
if [ ! -f ".env" ]; then
    echo "⚠️  No .env file found. Please copy .env.example to .env and configure it."
    cp .env.example .env
    echo "📝 Created .env file from template. Please edit it with your settings."
else
    echo "✅ Environment file found"
fi

# Build TypeScript
echo "🔨 Building TypeScript..."
yarn build

echo ""
echo "✅ Setup completed!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your configuration"
echo "2. Run 'yarn dev' for development"
echo "3. Run 'yarn start' for production"
echo ""
echo "Health check: http://localhost:3001/health"
