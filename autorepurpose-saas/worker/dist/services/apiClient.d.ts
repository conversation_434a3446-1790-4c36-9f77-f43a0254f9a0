import { VideoJob, PlatformOutput } from '../types/job';
export declare class ApiClient {
    private api;
    constructor();
    /**
     * Update job status in the main application
     */
    updateJobStatus(jobId: string, status: VideoJob['status'], progress?: number, errorMessage?: string): Promise<void>;
    /**
     * Create platform output record
     */
    createPlatformOutput(output: Omit<PlatformOutput, 'created_at'>): Promise<void>;
    /**
     * Mark job as completed
     */
    completeJob(jobId: string, outputs: PlatformOutput[]): Promise<void>;
    /**
     * Health check endpoint
     */
    healthCheck(): Promise<boolean>;
}
//# sourceMappingURL=apiClient.d.ts.map