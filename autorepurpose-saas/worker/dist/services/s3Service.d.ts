export declare class S3Service {
    private s3Client;
    constructor();
    /**
     * Download file from S3 to local filesystem
     */
    downloadFile(s3Key: string, localPath: string): Promise<void>;
    /**
     * Upload file from local filesystem to S3
     */
    uploadFile(localPath: string, s3Key: string, contentType?: string): Promise<string>;
    /**
     * Generate S3 key for processed video
     */
    generateProcessedKey(originalKey: string, platform: string, jobId: string): string;
    /**
     * Get file size from S3
     */
    getFileSize(s3Key: string): Promise<number>;
}
//# sourceMappingURL=s3Service.d.ts.map