import { ProcessingOptions } from '../types/job';
export declare class VideoProcessor {
    constructor();
    /**
     * Get video metadata using ffprobe
     */
    getVideoMetadata(inputPath: string): Promise<any>;
    /**
     * Process video according to platform specifications
     */
    processVideo(inputPath: string, outputPath: string, options: ProcessingOptions, onProgress?: (progress: number) => void): Promise<void>;
    /**
     * Validate video file
     */
    validateVideoFile(inputPath: string): Promise<boolean>;
}
//# sourceMappingURL=videoProcessor.d.ts.map