"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.S3Service = void 0;
const client_s3_1 = require("@aws-sdk/client-s3");
const config_1 = require("../config");
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
class S3Service {
    constructor() {
        this.s3Client = new client_s3_1.S3Client({
            region: config_1.config.aws.region,
            credentials: {
                accessKeyId: config_1.config.aws.accessKeyId,
                secretAccessKey: config_1.config.aws.secretAccessKey,
            },
        });
    }
    /**
     * Download file from S3 to local filesystem
     */
    async downloadFile(s3Key, localPath) {
        console.log(`📥 Downloading ${s3Key} from S3 to ${localPath}`);
        try {
            const command = new client_s3_1.GetObjectCommand({
                Bucket: config_1.config.aws.bucketName,
                Key: s3Key,
            });
            const response = await this.s3Client.send(command);
            if (!response.Body) {
                throw new Error('No response body from S3');
            }
            // Ensure directory exists
            await fs_extra_1.default.ensureDir(path_1.default.dirname(localPath));
            // Handle the response body
            if (response.Body) {
                // Convert stream to buffer
                const chunks = [];
                for await (const chunk of response.Body) {
                    chunks.push(chunk);
                }
                const buffer = Buffer.concat(chunks);
                await fs_extra_1.default.writeFile(localPath, buffer);
                console.log(`✅ Successfully downloaded ${s3Key}`);
            }
            else {
                throw new Error('No response body received');
            }
        }
        catch (error) {
            console.error(`❌ Error downloading ${s3Key}:`, error);
            throw error;
        }
    }
    /**
     * Upload file from local filesystem to S3
     */
    async uploadFile(localPath, s3Key, contentType) {
        console.log(`📤 Uploading ${localPath} to S3 as ${s3Key}`);
        try {
            const fileStream = fs_extra_1.default.createReadStream(localPath);
            const stats = await fs_extra_1.default.stat(localPath);
            const command = new client_s3_1.PutObjectCommand({
                Bucket: config_1.config.aws.bucketName,
                Key: s3Key,
                Body: fileStream,
                ContentType: contentType || 'video/mp4',
                ContentLength: stats.size,
            });
            await this.s3Client.send(command);
            const s3Url = `https://${config_1.config.aws.bucketName}.s3.${config_1.config.aws.region}.amazonaws.com/${s3Key}`;
            console.log(`✅ Successfully uploaded to ${s3Url}`);
            return s3Url;
        }
        catch (error) {
            console.error(`❌ Error uploading ${s3Key}:`, error);
            throw error;
        }
    }
    /**
     * Generate S3 key for processed video
     */
    generateProcessedKey(originalKey, platform, jobId) {
        const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
        const extension = path_1.default.extname(originalKey);
        const baseName = path_1.default.basename(originalKey, extension);
        return `processed/${timestamp}/${jobId}/${baseName}_${platform}${extension}`;
    }
    /**
     * Get file size from S3
     */
    async getFileSize(s3Key) {
        try {
            const command = new client_s3_1.GetObjectCommand({
                Bucket: config_1.config.aws.bucketName,
                Key: s3Key,
            });
            const response = await this.s3Client.send(command);
            return response.ContentLength || 0;
        }
        catch (error) {
            console.error(`❌ Error getting file size for ${s3Key}:`, error);
            throw error;
        }
    }
}
exports.S3Service = S3Service;
//# sourceMappingURL=s3Service.js.map