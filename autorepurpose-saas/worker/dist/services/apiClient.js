"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiClient = void 0;
const axios_1 = __importDefault(require("axios"));
const config_1 = require("../config");
class ApiClient {
    constructor() {
        this.api = axios_1.default.create({
            baseURL: config_1.config.app.apiUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }
    /**
     * Update job status in the main application
     */
    async updateJobStatus(jobId, status, progress, errorMessage) {
        try {
            console.log(`📡 Updating job ${jobId} status to: ${status} (${progress}%)`);
            const payload = {
                status,
                updated_at: new Date().toISOString(),
            };
            if (progress !== undefined) {
                payload.progress = progress;
            }
            if (errorMessage) {
                payload.error_message = errorMessage;
            }
            await this.api.post(`/api/worker/job-update`, {
                jobId,
                ...payload
            });
            console.log(`✅ Successfully updated job ${jobId} status`);
        }
        catch (error) {
            console.error(`❌ Failed to update job status:`, error);
            // Don't throw here - we don't want to fail the job just because of API issues
        }
    }
    /**
     * Create platform output record
     */
    async createPlatformOutput(output) {
        try {
            console.log(`📡 Creating platform output for job ${output.job_id}, platform: ${output.platform}`);
            await this.api.post(`/api/worker/platform-output`, {
                ...output,
                created_at: new Date().toISOString(),
            });
            console.log(`✅ Successfully created platform output`);
        }
        catch (error) {
            console.error(`❌ Failed to create platform output:`, error);
        }
    }
    /**
     * Mark job as completed
     */
    async completeJob(jobId, outputs) {
        try {
            console.log(`📡 Marking job ${jobId} as completed with ${outputs.length} outputs`);
            await this.api.post(`/api/worker/job-complete`, {
                jobId,
                outputs,
                completed_at: new Date().toISOString(),
            });
            console.log(`✅ Successfully marked job as completed`);
        }
        catch (error) {
            console.error(`❌ Failed to mark job as completed:`, error);
        }
    }
    /**
     * Health check endpoint
     */
    async healthCheck() {
        try {
            const response = await this.api.get('/api/health');
            return response.status === 200;
        }
        catch (error) {
            console.error('❌ API health check failed:', error);
            return false;
        }
    }
}
exports.ApiClient = ApiClient;
//# sourceMappingURL=apiClient.js.map