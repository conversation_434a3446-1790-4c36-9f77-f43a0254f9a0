{"version": 3, "file": "videoProcessor.js", "sourceRoot": "", "sources": ["../../src/services/videoProcessor.ts"], "names": [], "mappings": ";;;;;;AACA,kEAAmC;AACnC,gDAAwB;AACxB,wDAA0B;AAC1B,sCAAmC;AAGnC,MAAa,cAAc;IACzB;QACE,mBAAmB;QACnB,uBAAM,CAAC,aAAa,CAAC,eAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACzC,uBAAM,CAAC,cAAc,CAAC,eAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QACtC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,uBAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;gBAC1C,IAAI,GAAG,EAAE,CAAC;oBACR,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;oBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;oBACZ,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE;oBAChC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,QAAQ;oBAClC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI;oBAC1B,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,QAAQ;iBAClC,CAAC,CAAC;gBAEH,OAAO,CAAC,QAAQ,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAChB,SAAiB,EACjB,UAAkB,EAClB,OAA0B,EAC1B,UAAuC;QAEvC,OAAO,CAAC,GAAG,CAAC,2BAA2B,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,EAAE,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,cAAc,UAAU,EAAE,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAErC,iCAAiC;QACjC,MAAM,kBAAE,CAAC,SAAS,CAAC,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QAE7C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,OAAO,GAAG,IAAA,uBAAM,EAAC,SAAS,CAAC,CAAC;YAEhC,mCAAmC;YACnC,OAAO,GAAG,OAAO;iBACd,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC;iBAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC;iBAC9B,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC;iBAClC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC;iBAClC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAE1B,8BAA8B;YAC9B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC;YAC7C,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,MAAM,EAAE,CAAC,CAAC;YAE7C,wBAAwB;YACxB,IAAI,OAAO,CAAC,WAAW,KAAK,MAAM,EAAE,CAAC;gBACnC,yCAAyC;gBACzC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnC,CAAC;iBAAM,IAAI,OAAO,CAAC,WAAW,KAAK,MAAM,EAAE,CAAC;gBAC1C,mBAAmB;gBACnB,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnC,CAAC;iBAAM,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;gBACzC,eAAe;gBACf,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC;YAED,8BAA8B;YAC9B,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxB,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAClD,CAAC;YAED,wBAAwB;YACxB,IAAI,eAAM,CAAC,UAAU,CAAC,aAAa,KAAK,MAAM,EAAE,CAAC;gBAC/C,OAAO,GAAG,OAAO;qBACd,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAE,wBAAwB;qBACjD,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC;qBAC9B,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,OAAO;qBACd,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAE,8BAA8B;qBACvD,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAClC,CAAC;YAED,kCAAkC;YAClC,QAAQ,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;gBACvC,KAAK,iBAAiB,CAAC;gBACvB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,QAAQ;oBACX,uCAAuC;oBACvC,OAAO,GAAG,OAAO;yBACd,SAAS,CAAC,WAAW,EAAE,YAAY,CAAC;yBACpC,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;oBACpC,MAAM;gBAER,KAAK,gBAAgB;oBACnB,iCAAiC;oBACjC,OAAO,GAAG,OAAO;yBACd,SAAS,CAAC,WAAW,EAAE,YAAY,CAAC;yBACpC,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC;yBAChC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,WAAW;oBACrC,MAAM;gBAER,KAAK,eAAe,CAAC;gBACrB,KAAK,SAAS,CAAC;gBACf,KAAK,UAAU;oBACb,yBAAyB;oBACzB,OAAO,GAAG,OAAO;yBACd,SAAS,CAAC,WAAW,EAAE,YAAY,CAAC;yBACpC,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;oBACpC,MAAM;YACV,CAAC;YAED,oBAAoB;YACpB,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,EAAE;gBAClC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;gBAClD,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,GAAG,CAAC,CAAC;gBACzC,IAAI,UAAU,EAAE,CAAC;oBACf,UAAU,CAAC,OAAO,CAAC,CAAC;gBACtB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,iBAAiB;YACjB,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBAC1B,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;gBACjD,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,mBAAmB;YACnB,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBACrB,OAAO,CAAC,GAAG,CAAC,sCAAsC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACtE,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;YAEH,mBAAmB;YACnB,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QACvC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAExD,iCAAiC;YACjC,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,KAAK,OAAO,CAAC,CAAC;YACvF,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YAED,kBAAkB;YAClB,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;YACrD,IAAI,UAAU,GAAG,eAAM,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;gBACjD,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,gCAAgC,eAAM,CAAC,UAAU,CAAC,aAAa,KAAK,CAAC,CAAC;YAC3H,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AA7KD,wCA6KC"}