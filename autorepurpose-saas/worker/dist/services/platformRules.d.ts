import { ProcessingOptions } from '../types/job';
export declare class PlatformRulesService {
    private static rules;
    /**
     * Get processing options for a platform
     */
    static getProcessingOptions(platform: string): ProcessingOptions | null;
    /**
     * Get all supported platforms
     */
    static getSupportedPlatforms(): string[];
    /**
     * Validate platform
     */
    static isValidPlatform(platform: string): boolean;
    /**
     * Get platform display name
     */
    static getDisplayName(platform: string): string;
}
//# sourceMappingURL=platformRules.d.ts.map