{"version": 3, "file": "platformRules.js", "sourceRoot": "", "sources": ["../../src/services/platformRules.ts"], "names": [], "mappings": ";;;AAoBA,MAAa,oBAAoB;IA+F/B;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,QAAgB;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;SAChC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB;QAC1B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,QAAgB;QACrC,OAAO,QAAQ,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,QAAgB;QACpC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC5C,CAAC;;AA1IH,oDA2IC;AA1IgB,0BAAK,GAAiC;IACnD,iBAAiB,EAAE;QACjB,QAAQ,EAAE,iBAAiB;QAC3B,WAAW,EAAE,iBAAiB;QAC9B,WAAW,EAAE,MAAM;QACnB,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;QACzC,WAAW,EAAE,EAAE;QACf,UAAU,EAAE,SAAS;QACrB,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,EAAE;QACb,YAAY,EAAE,OAAO;QACrB,YAAY,EAAE,MAAM;QACpB,MAAM,EAAE,KAAK;KACd;IACD,gBAAgB,EAAE;QAChB,QAAQ,EAAE,gBAAgB;QAC1B,WAAW,EAAE,gBAAgB;QAC7B,WAAW,EAAE,MAAM;QACnB,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;QACzC,WAAW,EAAE,EAAE;QACf,UAAU,EAAE,SAAS;QACrB,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,EAAE;QACb,YAAY,EAAE,OAAO;QACrB,YAAY,EAAE,MAAM;QACpB,MAAM,EAAE,KAAK;KACd;IACD,gBAAgB,EAAE;QAChB,QAAQ,EAAE,gBAAgB;QAC1B,WAAW,EAAE,gBAAgB;QAC7B,WAAW,EAAE,MAAM;QACnB,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;QACzC,WAAW,EAAE,EAAE;QACf,UAAU,EAAE,SAAS;QACrB,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,EAAE;QACb,YAAY,EAAE,OAAO;QACrB,YAAY,EAAE,MAAM;QACpB,MAAM,EAAE,KAAK;KACd;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,QAAQ;QAClB,WAAW,EAAE,QAAQ;QACrB,WAAW,EAAE,MAAM;QACnB,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;QACzC,WAAW,EAAE,GAAG;QAChB,UAAU,EAAE,SAAS;QACrB,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,EAAE;QACb,YAAY,EAAE,OAAO;QACrB,YAAY,EAAE,MAAM;QACpB,MAAM,EAAE,KAAK;KACd;IACD,eAAe,EAAE;QACf,QAAQ,EAAE,eAAe;QACzB,WAAW,EAAE,eAAe;QAC5B,WAAW,EAAE,MAAM;QACnB,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;QACzC,WAAW,EAAE,GAAG;QAChB,UAAU,EAAE,SAAS;QACrB,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,EAAE;QACb,YAAY,EAAE,OAAO;QACrB,YAAY,EAAE,MAAM;QACpB,MAAM,EAAE,KAAK;KACd;IACD,SAAS,EAAE;QACT,QAAQ,EAAE,SAAS;QACnB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE,MAAM;QACnB,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE;QACxC,WAAW,EAAE,GAAG;QAChB,UAAU,EAAE,SAAS;QACrB,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,EAAE;QACb,YAAY,EAAE,OAAO;QACrB,YAAY,EAAE,MAAM;QACpB,MAAM,EAAE,KAAK;KACd;IACD,UAAU,EAAE;QACV,QAAQ,EAAE,UAAU;QACpB,WAAW,EAAE,UAAU;QACvB,WAAW,EAAE,MAAM;QACnB,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;QACzC,WAAW,EAAE,GAAG;QAChB,UAAU,EAAE,SAAS;QACrB,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,EAAE;QACb,YAAY,EAAE,OAAO;QACrB,YAAY,EAAE,MAAM;QACpB,MAAM,EAAE,KAAK;KACd;CACF,CAAC"}