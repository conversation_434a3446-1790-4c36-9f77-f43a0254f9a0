"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
exports.config = {
    redis: {
        url: process.env.REDIS_URL,
    },
    aws: {
        region: process.env.AWS_REGION || 'us-east-1',
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        bucketName: process.env.S3_BUCKET_NAME,
    },
    worker: {
        concurrency: parseInt(process.env.WORKER_CONCURRENCY || '3'),
        name: process.env.WORKER_NAME || 'video-processor',
    },
    app: {
        apiUrl: process.env.APP_API_URL || 'http://localhost:3000',
    },
    processing: {
        maxFileSizeGB: parseInt(process.env.MAX_FILE_SIZE_GB || '5'),
        tempDir: process.env.TEMP_DIR || '/tmp/autorepurpose',
        outputQuality: process.env.OUTPUT_QUALITY || 'high',
    },
    ffmpeg: {
        path: process.env.FFMPEG_PATH || 'ffmpeg',
        probePath: process.env.FFPROBE_PATH || 'ffprobe',
    },
    logging: {
        level: process.env.LOG_LEVEL || 'info',
    },
    env: process.env.NODE_ENV || 'development',
};
// Validate required environment variables
const requiredVars = [
    'REDIS_URL',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'S3_BUCKET_NAME'
];
for (const varName of requiredVars) {
    if (!process.env[varName]) {
        console.error(`❌ Missing required environment variable: ${varName}`);
        process.exit(1);
    }
}
console.log('✅ Configuration loaded successfully');
//# sourceMappingURL=index.js.map