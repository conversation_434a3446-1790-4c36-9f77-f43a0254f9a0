export declare const config: {
    redis: {
        url: string;
    };
    aws: {
        region: string;
        accessKeyId: string;
        secretAccessKey: string;
        bucketName: string;
    };
    worker: {
        concurrency: number;
        name: string;
    };
    app: {
        apiUrl: string;
    };
    processing: {
        maxFileSizeGB: number;
        tempDir: string;
        outputQuality: string;
    };
    ffmpeg: {
        path: string;
        probePath: string;
    };
    logging: {
        level: string;
    };
    env: string;
};
//# sourceMappingURL=index.d.ts.map