"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.redis = exports.worker = void 0;
const bullmq_1 = require("bullmq");
const ioredis_1 = __importDefault(require("ioredis"));
const config_1 = require("./config");
const jobProcessor_1 = require("./worker/jobProcessor");
// Create Redis connection
const redis = new ioredis_1.default(config_1.config.redis.url, {
    maxRetriesPerRequest: 3,
});
exports.redis = redis;
// Create job processor
const jobProcessor = new jobProcessor_1.JobProcessor();
// Create worker
const worker = new bullmq_1.Worker('video-processing', async (job) => {
    console.log(`🔄 Processing job: ${job.id}`);
    await jobProcessor.processJob(job);
}, {
    connection: redis,
    concurrency: config_1.config.worker.concurrency,
    removeOnComplete: { count: 100 },
    removeOnFail: { count: 50 },
});
exports.worker = worker;
// Worker event handlers
worker.on('ready', () => {
    console.log(`🚀 Worker '${config_1.config.worker.name}' is ready!`);
    console.log(`⚙️  Concurrency: ${config_1.config.worker.concurrency}`);
    console.log(`🔗 Redis: Connected`);
    console.log(`🎬 FFmpeg: ${config_1.config.ffmpeg.path}`);
});
worker.on('active', (job) => {
    console.log(`▶️  Job ${job.id} started processing`);
});
worker.on('completed', (job, result) => {
    console.log(`✅ Job ${job.id} completed successfully`);
});
worker.on('failed', (job, err) => {
    console.error(`❌ Job ${job?.id} failed:`, err.message);
});
worker.on('error', (err) => {
    console.error('❌ Worker error:', err);
});
worker.on('stalled', (jobId) => {
    console.warn(`⚠️  Job ${jobId} stalled`);
});
// Graceful shutdown
const shutdown = async (signal) => {
    console.log(`📡 Received ${signal}, shutting down gracefully...`);
    try {
        await worker.close();
        await redis.disconnect();
        console.log('✅ Worker shut down successfully');
        process.exit(0);
    }
    catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
    }
};
process.on('SIGTERM', () => shutdown('SIGTERM'));
process.on('SIGINT', () => shutdown('SIGINT'));
// Health check endpoint (basic HTTP server)
const http_1 = require("http");
const healthServer = (0, http_1.createServer)((req, res) => {
    if (req.url === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'healthy',
            worker: config_1.config.worker.name,
            timestamp: new Date().toISOString()
        }));
    }
    else {
        res.writeHead(404);
        res.end('Not Found');
    }
});
const PORT = process.env.PORT || 3001;
healthServer.listen(PORT, () => {
    console.log(`🏥 Health check server running on port ${PORT}`);
});
console.log(`🎥 AutoRepurpose Video Processing Worker started`);
console.log(`📦 Environment: ${config_1.config.env}`);
console.log(`🔧 Temp Directory: ${config_1.config.processing.tempDir}`);
console.log(`📡 API URL: ${config_1.config.app.apiUrl}`);
//# sourceMappingURL=index.js.map