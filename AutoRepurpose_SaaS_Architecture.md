# AutoRepurpose SaaS - Comprehensive System Architecture

## Table of Contents
1. [Platform Rules Analysis](#1-platform-rules-analysis)
2. [System Architecture](#2-system-architecture) 
3. [Database Schema](#3-database-schema)
4. [API Architecture](#4-api-architecture)
5. [Worker Architecture](#5-worker-architecture)
6. [File Flow Pipeline](#6-file-flow-pipeline)
7. [Authentication & Payment Flows](#7-authentication--payment-flows)
8. [Development Roadmap](#8-development-roadmap)

---

## 1. Platform Rules Analysis

### JSON Structure Overview
The `video_platforms_rules_updated.json` file defines a sophisticated platform specification system with:

#### Core Classification Logic
```javascript
// Form Type Classification Function
classify_video_form(duration_seconds, height, width) {
  const aspectRatio = height / width;
  return (duration_seconds <= 120 && aspectRatio >= 1.6) 
    ? 'short_form' 
    : 'long_form';
}
```

#### Platform Categories
**Short-Form Platforms (≤120s, vertical ≥1.6 aspect ratio):**
- TikTok: 9:16, 1080x1920, max 120s, H.264/AAC
- Instagram Reels: 9:16, 1080x1920, max 90s 
- YouTube Shorts: 9:16, 1080x1920, max 60s
- X/Twitter Short: 9:16/1:1, 1080x1920, max 140s
- Facebook Reels: 9:16, 1080x1920, max 120s
- Snapchat: 9:16, 1080x1920, max 180s (ads)

**Long-Form Platforms:**
- YouTube: 16:9, 1920x1080, up to 12 hours
- Instagram Feed: 1:1/4:5, 1080x1080, up to 60 minutes
- LinkedIn: 16:9/1:1, 1920x1080, max 10 minutes
- Facebook Feed: 1:1/4:5, 1080x1080, max 240 minutes

#### Conversion Presets Structure
Each platform defines quality presets:
```json
"conversion_presets": {
  "quality_high": {
    "resolution": "1080x1920",
    "bitrate": "2500 kbps", 
    "frame_rate": "30",
    "audio_bitrate": "128 kbps",
    "video_codec": "H.264",
    "audio_codec": "AAC"
  }
}
```

#### Same-Form Enforcement
- **Critical Rule**: Only same-form conversions allowed (short→short, long→long)
- Cross-form conversion explicitly disabled for content integrity
- Compatibility matrix defines valid platform pairs

---

## 2. System Architecture

### High-Level Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        CLIENT LAYER                             │
├─────────────────────────────────────────────────────────────────┤
│ Next.js Frontend (Vercel)                                      │
│ ├── Upload Interface (Resumable S3 uploads)                    │
│ ├── Platform Selection UI                                      │
│ ├── Job Status Dashboard (SSE)                                 │
│ ├── Download Management                                        │
│ └── Admin Panel (Rules Management)                             │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        API LAYER                               │
├─────────────────────────────────────────────────────────────────┤
│ Next.js API Routes + Node.js Worker Service                    │
│ ├── Authentication (Clerk)                                     │
│ ├── Payment Processing (Stripe)                                │
│ ├── Video Classification API                                   │
│ ├── Job Management API                                         │
│ ├── SSE Status Stream                                          │
│ └── Admin Rules API                                            │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    PROCESSING LAYER                             │
├─────────────────────────────────────────────────────────────────┤
│ Node.js Worker Service (Railway)                               │
│ ├── BullMQ Job Queue (Redis)                                   │
│ ├── FFmpeg Processing Pipeline                                 │
│ ├── Platform Rules Engine                                      │
│ └── Batch Processing System                                    │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      DATA LAYER                                │
├─────────────────────────────────────────────────────────────────┤
│ ├── Supabase PostgreSQL (Metadata, Jobs, Users)               │
│ ├── AWS S3 (Video Storage with 3-day retention)               │
│ ├── Upstash Redis (Job Queue + Session Cache)                 │
│ └── Platform Rules JSON (Dynamic loading)                     │
└─────────────────────────────────────────────────────────────────┘
```

### Component Relationships

#### Core Services
1. **Frontend (Next.js/Vercel)**: User interface, file uploads, real-time status
2. **API Layer (Next.js API + Worker)**: Business logic, authentication, job orchestration
3. **Worker Service (Railway)**: Video processing, FFmpeg operations
4. **Storage Systems**: S3 (files), PostgreSQL (metadata), Redis (jobs/cache)

#### Key Integrations
- **Clerk**: Authentication and user management
- **Stripe**: Subscription billing and payment processing
- **BullMQ**: Reliable job queue with Redis backend
- **FFmpeg**: Video transcoding and format conversion

---

## 3. Database Schema

### PostgreSQL Schema (Supabase)

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (synced with Clerk)
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  clerk_user_id VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255) NOT NULL,
  full_name VARCHAR(255),
  subscription_status VARCHAR(50) DEFAULT 'trial', -- trial, active, cancelled, expired
  subscription_id VARCHAR(255), -- Stripe subscription ID
  current_period_end TIMESTAMP WITH TIME ZONE,
  free_repurposings_used INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Video jobs table
CREATE TABLE video_jobs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  original_filename VARCHAR(500) NOT NULL,
  s3_input_key VARCHAR(1000) NOT NULL,
  file_size_bytes BIGINT NOT NULL,
  duration_seconds DECIMAL(10,2) NOT NULL,
  
  -- Video metadata
  width INTEGER NOT NULL,
  height INTEGER NOT NULL,
  aspect_ratio DECIMAL(10,6) NOT NULL,
  form_type VARCHAR(20) NOT NULL CHECK (form_type IN ('short_form', 'long_form')),
  
  -- Processing status
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN (
    'pending', 'classifying', 'queued', 'processing', 
    'completed', 'failed', 'expired'
  )),
  error_message TEXT,
  
  -- Platform selection
  selected_platforms JSONB NOT NULL, -- Array of platform IDs
  
  -- Processing metadata
  queue_job_id VARCHAR(255), -- BullMQ job ID
  processing_started_at TIMESTAMP WITH TIME ZONE,
  processing_completed_at TIMESTAMP WITH TIME ZONE,
  
  -- Retention
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '3 days'),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Video outputs table (one per platform conversion)
CREATE TABLE video_outputs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  job_id UUID REFERENCES video_jobs(id) ON DELETE CASCADE,
  platform_id VARCHAR(50) NOT NULL, -- e.g., 'tiktok', 'youtube_shorts'
  preset_name VARCHAR(100) NOT NULL, -- e.g., 'quality_high', 'shorts_high'
  
  -- Output file details
  s3_output_key VARCHAR(1000) NOT NULL,
  filename VARCHAR(500) NOT NULL,
  file_size_bytes BIGINT,
  
  -- Technical specs applied
  resolution VARCHAR(20) NOT NULL, -- e.g., '1080x1920'
  bitrate VARCHAR(20), -- e.g., '2500 kbps'
  video_codec VARCHAR(20),
  audio_codec VARCHAR(20),
  
  -- Processing status
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN (
    'pending', 'processing', 'completed', 'failed'
  )),
  error_message TEXT,
  processing_time_seconds INTEGER,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Platform rules cache table (for performance)
CREATE TABLE platform_rules_cache (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rules_version VARCHAR(50) NOT NULL,
  rules_data JSONB NOT NULL,
  checksum VARCHAR(64) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment transactions table
CREATE TABLE payment_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  stripe_payment_intent_id VARCHAR(255),
  stripe_subscription_id VARCHAR(255),
  amount_cents INTEGER NOT NULL,
  currency VARCHAR(3) DEFAULT 'usd',
  status VARCHAR(50) NOT NULL, -- pending, succeeded, failed, refunded
  transaction_type VARCHAR(50) NOT NULL, -- subscription, one_time
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage tracking table
CREATE TABLE usage_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  job_id UUID REFERENCES video_jobs(id) ON DELETE SET NULL,
  action VARCHAR(100) NOT NULL, -- upload, process, download
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_video_jobs_user_id ON video_jobs(user_id);
CREATE INDEX idx_video_jobs_status ON video_jobs(status);
CREATE INDEX idx_video_jobs_expires_at ON video_jobs(expires_at);
CREATE INDEX idx_video_outputs_job_id ON video_outputs(job_id);
CREATE INDEX idx_video_outputs_status ON video_outputs(status);
CREATE INDEX idx_users_clerk_id ON users(clerk_user_id);
CREATE INDEX idx_users_subscription ON users(subscription_status);
CREATE INDEX idx_payment_transactions_user ON payment_transactions(user_id);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_jobs_updated_at BEFORE UPDATE ON video_jobs
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_outputs_updated_at BEFORE UPDATE ON video_outputs
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

---

## 4. API Architecture

### Next.js API Routes (Frontend)

#### Authentication & User Management
```
POST   /api/auth/webhook              # Clerk webhook for user sync
GET    /api/user/profile              # Get current user profile
PUT    /api/user/profile              # Update user profile
GET    /api/user/usage                # Get usage statistics
```

#### Video Upload & Job Management
```
POST   /api/upload/presigned-url      # Get S3 presigned URL for upload
POST   /api/jobs/create               # Create new video processing job
GET    /api/jobs                      # List user's jobs (paginated)
GET    /api/jobs/:id                  # Get specific job details
DELETE /api/jobs/:id                  # Cancel/delete job
```

#### Platform & Classification APIs
```
POST   /api/classify/video            # Classify video form type
GET    /api/platforms                 # Get available platforms by form type
POST   /api/platforms/validate        # Validate platform selection
GET    /api/platforms/presets/:id     # Get conversion presets for platform
```

#### Real-time Status
```
GET    /api/jobs/:id/status/stream    # SSE endpoint for job status updates
POST   /api/jobs/:id/retry            # Retry failed job
```

#### Downloads
```
GET    /api/download/:outputId        # Generate presigned download URL
POST   /api/download/bulk             # Generate bulk download ZIP
```

#### Payment & Subscription
```
POST   /api/payments/create-checkout  # Create Stripe checkout session
POST   /api/payments/webhook          # Stripe webhook handler
GET    /api/subscription/status       # Get subscription status
POST   /api/subscription/cancel       # Cancel subscription
POST   /api/subscription/resume       # Resume subscription
```

#### Admin APIs (Protected)
```
GET    /api/admin/platform-rules      # Get current platform rules
PUT    /api/admin/platform-rules      # Update platform rules JSON
GET    /api/admin/stats               # System usage statistics
GET    /api/admin/jobs                # All jobs (admin view)
```

### Worker Service APIs (Railway)

#### Job Processing Endpoints
```
POST   /worker/jobs/classify          # Classify video form type
POST   /worker/jobs/process           # Start video processing
GET    /worker/jobs/:id/status        # Get job processing status
POST   /worker/jobs/:id/cancel        # Cancel processing job
```

#### Health & Monitoring
```
GET    /worker/health                 # Health check endpoint
GET    /worker/metrics                # Processing metrics
GET    /worker/queue/stats            # BullMQ queue statistics
```

#### Internal Communication
```
POST   /worker/webhook/status         # Status update webhook from worker
POST   /worker/cleanup/expired        # Cleanup expired files
```

### API Response Formats

#### Standard Response Wrapper
```typescript
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

#### Video Classification Response
```typescript
interface ClassificationResponse {
  form_type: 'short_form' | 'long_form';
  duration_seconds: number;
  dimensions: {
    width: number;
    height: number;
    aspect_ratio: number;
  };
  classification_confidence: number;
  compatible_platforms: string[];
}
```

#### Job Status Response
```typescript
interface JobStatusResponse {
  id: string;
  status: JobStatus;
  progress: number; // 0-100
  current_step: string;
  outputs: {
    platform_id: string;
    status: OutputStatus;
    file_size?: number;
    error_message?: string;
  }[];
  estimated_completion?: string;
  error_message?: string;
}
```

---

## 5. Worker Architecture

### BullMQ Job Processing Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Job Created   │───▶│  Classification │───▶│ Platform Match  │
│                 │    │     Queue       │    │     Queue       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Completion    │◀───│   Processing    │◀───│  Job Queuing    │
│   Notification  │    │     Queue       │    │     Queue       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Queue Definitions

#### 1. Classification Queue
```typescript
// Queue: video-classification
interface ClassificationJob {
  jobId: string;
  s3Key: string;
  userId: string;
  filename: string;
}

// Processing steps:
// 1. Download video metadata from S3
// 2. Extract duration, dimensions using ffprobe
// 3. Apply classification logic
// 4. Update job record with form_type
// 5. Move to platform-matching queue
```

#### 2. Platform Matching Queue
```typescript
// Queue: platform-matching
interface PlatformMatchingJob {
  jobId: string;
  formType: 'short_form' | 'long_form';
  selectedPlatforms: string[];
  videoMetadata: VideoMetadata;
}

// Processing steps:
// 1. Load platform rules from cache/JSON
// 2. Validate platform compatibility with form type
// 3. Filter invalid platforms
// 4. Create conversion jobs for each valid platform
// 5. Move to processing queue
```

#### 3. Video Processing Queue
```typescript
// Queue: video-processing
interface ProcessingJob {
  jobId: string;
  outputId: string;
  s3InputKey: string;
  platformId: string;
  presetName: string;
  conversionSpecs: ConversionSpecs;
}

// Processing steps:
// 1. Download original video from S3
// 2. Apply ffmpeg conversion with platform specs
// 3. Upload converted video to S3
// 4. Update output record with file details
// 5. Notify completion
```

### FFmpeg Processing Pipeline

#### Core Conversion Function
```bash
# Example FFmpeg command for TikTok (short_form, quality_high)
ffmpeg -i input.mp4 \
  -vf "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:black" \
  -c:v libx264 \
  -preset medium \
  -crf 23 \
  -b:v 2500k \
  -maxrate 2500k \
  -bufsize 5000k \
  -c:a aac \
  -b:a 128k \
  -ar 44100 \
  -r 30 \
  -movflags +faststart \
  output_tiktok_high.mp4
```

#### Dynamic Command Generation
```typescript
class FFmpegCommandBuilder {
  static buildCommand(
    inputPath: string, 
    outputPath: string, 
    specs: ConversionSpecs
  ): string[] {
    const command = ['ffmpeg', '-i', inputPath];
    
    // Video filters for aspect ratio and scaling
    const videoFilters = this.buildVideoFilters(specs);
    if (videoFilters.length > 0) {
      command.push('-vf', videoFilters.join(','));
    }
    
    // Video codec and bitrate settings
    command.push('-c:v', specs.video_codec);
    command.push('-b:v', specs.bitrate);
    command.push('-maxrate', specs.bitrate);
    command.push('-bufsize', `${parseInt(specs.bitrate) * 2}k`);
    
    // Audio settings
    command.push('-c:a', specs.audio_codec);
    command.push('-b:a', specs.audio_bitrate);
    command.push('-ar', '44100');
    
    // Frame rate
    command.push('-r', specs.frame_rate);
    
    // Optimization flags
    command.push('-movflags', '+faststart');
    command.push('-preset', 'medium');
    
    command.push(outputPath);
    return command;
  }
}
```

### Job Queue Configuration

#### Redis Configuration (Upstash)
```typescript
const queueConfig = {
  connection: {
    host: process.env.UPSTASH_REDIS_HOST,
    port: process.env.UPSTASH_REDIS_PORT,
    password: process.env.UPSTASH_REDIS_TOKEN,
    tls: true
  },
  defaultJobOptions: {
    removeOnComplete: 10,
    removeOnFail: 10,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 5000
    }
  }
};
```

#### Worker Process Management
```typescript
// Classification Worker
const classificationWorker = new Worker('video-classification', 
  async (job) => {
    const { jobId, s3Key, userId } = job.data;
    
    try {
      // Download and analyze video
      const metadata = await analyzeVideo(s3Key);
      const formType = classifyVideoForm(metadata);
      
      // Update job record
      await updateJobRecord(jobId, { 
        form_type: formType,
        ...metadata 
      });
      
      // Queue platform matching
      await platformMatchingQueue.add('match-platforms', {
        jobId,
        formType,
        ...job.data
      });
      
    } catch (error) {
      throw new Error(`Classification failed: ${error.message}`);
    }
  },
  queueConfig
);

// Processing Worker with concurrency control
const processingWorker = new Worker('video-processing', 
  async (job) => {
    const { jobId, outputId, s3InputKey, platformId, presetName } = job.data;
    
    // Update progress
    job.updateProgress(10);
    
    try {
      // Download original
      const inputPath = await downloadFromS3(s3InputKey);
      job.updateProgress(30);
      
      // Process video
      const outputPath = await processVideo(inputPath, platformId, presetName);
      job.updateProgress(80);
      
      // Upload result
      const s3OutputKey = await uploadToS3(outputPath);
      job.updateProgress(100);
      
      // Update database
      await updateOutputRecord(outputId, {
        status: 'completed',
        s3_output_key: s3OutputKey,
        file_size_bytes: getFileSize(outputPath)
      });
      
    } catch (error) {
      await updateOutputRecord(outputId, {
        status: 'failed',
        error_message: error.message
      });
      throw error;
    }
  },
  { ...queueConfig, concurrency: 3 } // Limit concurrent processing
);
```

---

## 6. File Flow Pipeline

### Complete Data Flow Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Upload   │───▶│   S3 Storage    │───▶│  Classification │
│   (Frontend)    │    │   (Original)    │    │    (Worker)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Download      │◀───│   S3 Storage    │◀───│   Processing    │
│  (Frontend)     │    │  (Converted)    │    │    (Worker)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1. Upload Pipeline

#### Frontend Upload Flow
```typescript
// components/VideoUploader.tsx
const uploadVideo = async (file: File) => {
  // Validate file size (5GB limit)
  if (file.size > 5 * 1024 * 1024 * 1024) {
    throw new Error('File size exceeds 5GB limit');
  }
  
  // Get presigned URL
  const { presignedUrl, s3Key } = await fetch('/api/upload/presigned-url', {
    method: 'POST',
    body: JSON.stringify({
      filename: file.name,
      contentType: file.type,
      fileSize: file.size
    })
  }).then(r => r.json());
  
  // Resumable upload to S3
  const uploadResult = await uploadWithProgress(file, presignedUrl, {
    onProgress: (progress) => setUploadProgress(progress),
    chunkSize: 10 * 1024 * 1024, // 10MB chunks
    resumable: true
  });
  
  // Create processing job
  const job = await createProcessingJob({
    originalFilename: file.name,
    s3InputKey: s3Key,
    fileSize: file.size
  });
  
  return job;
};
```

#### S3 Storage Structure
```
bucket: autorepurpose-videos/
├── uploads/
│   ├── user-{userId}/
│   │   ├── {jobId}/
│   │   │   ├── original.{ext}           # Original uploaded file
│   │   │   ├── outputs/
│   │   │   │   ├── tiktok_high.mp4     # Platform-specific outputs
│   │   │   │   ├── instagram_reels.mp4
│   │   │   │   └── youtube_shorts.mp4
│   │   │   └── metadata.json           # Processing metadata
│   │   └── ...
│   └── ...
├── temp/                               # Temporary processing files
└── archive/                           # Backup before deletion
```

### 2. Classification Pipeline

#### Video Analysis Worker
```typescript
// services/videoAnalyzer.ts
export class VideoAnalyzer {
  static async analyzeVideo(s3Key: string): Promise<VideoMetadata> {
    const tempPath = await this.downloadFromS3(s3Key);
    
    try {
      // Use ffprobe for metadata extraction
      const ffprobeCommand = `ffprobe -v quiet -print_format json -show_streams -show_format "${tempPath}"`;
      const metadata = await execAsync(ffprobeCommand);
      const parsed = JSON.parse(metadata);
      
      const videoStream = parsed.streams.find(s => s.codec_type === 'video');
      
      return {
        duration_seconds: parseFloat(parsed.format.duration),
        width: videoStream.width,
        height: videoStream.height,
        aspect_ratio: videoStream.width / videoStream.height,
        video_codec: videoStream.codec_name,
        audio_codec: parsed.streams.find(s => s.codec_type === 'audio')?.codec_name,
        frame_rate: this.parseFrameRate(videoStream.r_frame_rate),
        bitrate: parseInt(parsed.format.bit_rate)
      };
    } finally {
      await fs.unlink(tempPath); // Cleanup temp file
    }
  }
  
  static classifyFormType(metadata: VideoMetadata): FormType {
    const aspectRatio = metadata.height / metadata.width;
    return (metadata.duration_seconds <= 120 && aspectRatio >= 1.6) 
      ? 'short_form' 
      : 'long_form';
  }
}
```

### 3. Processing Pipeline

#### Platform Rules Engine
```typescript
// services/platformRulesEngine.ts
export class PlatformRulesEngine {
  private static rulesCache: PlatformRules;
  
  static async loadRules(): Promise<PlatformRules> {
    if (!this.rulesCache) {
      // Try loading from database cache first
      const cached = await this.loadFromCache();
      if (cached) {
        this.rulesCache = cached;
      } else {
        // Fallback to JSON file
        const rules = await this.loadFromFile('/app/platform_rules.json');
        await this.saveToCache(rules);
        this.rulesCache = rules;
      }
    }
    return this.rulesCache;
  }
  
  static async validatePlatformCompatibility(
    formType: FormType, 
    selectedPlatforms: string[]
  ): Promise<ValidationResult> {
    const rules = await this.loadRules();
    const compatiblePlatforms = rules.form_compatibility_matrix[`${formType}_platforms`];
    
    const validPlatforms = selectedPlatforms.filter(platform => 
      compatiblePlatforms.includes(platform)
    );
    
    const invalidPlatforms = selectedPlatforms.filter(platform => 
      !compatiblePlatforms.includes(platform)
    );
    
    return {
      valid: invalidPlatforms.length === 0,
      validPlatforms,
      invalidPlatforms,
      errors: invalidPlatforms.map(p => `${p} not compatible with ${formType}`)
    };
  }
  
  static getConversionSpecs(platformId: string, presetName: string): ConversionSpecs {
    const rules = this.rulesCache;
    const platformConfig = rules.platforms[platformId];
    
    // Determine form type from platform ID
    const formType = this.extractFormType(platformId);
    const preset = platformConfig.form_types[formType].conversion_presets[presetName];
    
    return preset;
  }
}
```

#### Video Processing Worker
```typescript
// workers/videoProcessor.ts
export class VideoProcessor {
  static async processVideo(
    inputS3Key: string,
    platformId: string,
    presetName: string,
    jobId: string,
    outputId: string
  ): Promise<ProcessingResult> {
    
    const inputPath = await this.downloadFromS3(inputS3Key);
    const outputPath = this.generateOutputPath(jobId, platformId, presetName);
    
    try {
      // Get conversion specifications
      const specs = PlatformRulesEngine.getConversionSpecs(platformId, presetName);
      
      // Build FFmpeg command
      const command = FFmpegCommandBuilder.buildCommand(inputPath, outputPath, specs);
      
      // Execute conversion with progress tracking
      await this.executeFFmpeg(command, (progress) => {
        this.updateJobProgress(outputId, progress);
      });
      
      // Upload result to S3
      const outputS3Key = await this.uploadToS3(outputPath, jobId, platformId);
      
      // Get file statistics
      const stats = await fs.stat(outputPath);
      
      return {
        success: true,
        outputS3Key,
        fileSize: stats.size,
        processingTime: Date.now() - startTime
      };
      
    } finally {
      // Cleanup temporary files
      await this.cleanup([inputPath, outputPath]);
    }
  }
  
  private static async executeFFmpeg(
    command: string[], 
    onProgress: (progress: number) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const process = spawn('ffmpeg', command.slice(1));
      
      let duration = 0;
      let timeProcessed = 0;
      
      process.stderr.on('data', (data) => {
        const output = data.toString();
        
        // Parse duration from FFmpeg output
        const durationMatch = output.match(/Duration: (\d{2}):(\d{2}):(\d{2}\.\d{2})/);
        if (durationMatch) {
          duration = this.parseDuration(durationMatch[0]);
        }
        
        // Parse current time from FFmpeg output
        const timeMatch = output.match(/time=(\d{2}):(\d{2}):(\d{2}\.\d{2})/);
        if (timeMatch && duration > 0) {
          timeProcessed = this.parseDuration(timeMatch[0]);
          const progress = Math.min(90, (timeProcessed / duration) * 90); // Max 90% during processing
          onProgress(progress);
        }
      });
      
      process.on('close', (code) => {
        if (code === 0) {
          onProgress(100);
          resolve();
        } else {
          reject(new Error(`FFmpeg process failed with code ${code}`));
        }
      });
    });
  }
}
```

### 4. Download Pipeline

#### Download API with Presigned URLs
```typescript
// pages/api/download/[outputId].ts
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { outputId } = req.query;
  const userId = await getCurrentUserId(req);
  
  // Verify user owns this output
  const output = await supabase
    .from('video_outputs')
    .select(`
      *,
      video_jobs!inner (
        user_id,
        expires_at
      )
    `)
    .eq('id', outputId)
    .eq('video_jobs.user_id', userId)
    .single();
  
  if (!output.data) {
    return res.status(404).json({ error: 'Output not found' });
  }
  
  // Check if expired
  if (new Date(output.data.video_jobs.expires_at) < new Date()) {
    return res.status(410).json({ error: 'File has expired' });
  }
  
  // Generate presigned download URL (1 hour expiry)
  const presignedUrl = await s3Client.getSignedUrl(GetObjectCommand, {
    Bucket: process.env.S3_BUCKET,
    Key: output.data.s3_output_key,
    ResponseContentDisposition: `attachment; filename="${output.data.filename}"`
  }, { expiresIn: 3600 });
  
  // Log download activity
  await logActivity(userId, 'download', { outputId, filename: output.data.filename });
  
  res.json({ downloadUrl: presignedUrl });
}
```

### 5. Retention & Cleanup Pipeline

#### Automated Cleanup System
```typescript
// services/cleanupService.ts
export class CleanupService {
  static async runDailyCleanup(): Promise<CleanupReport> {
    const expiredJobs = await this.findExpiredJobs();
    const cleanupResults: CleanupResult[] = [];
    
    for (const job of expiredJobs) {
      try {
        // Archive to backup location (optional)
        if (process.env.ENABLE_BACKUP === 'true') {
          await this.archiveJobFiles(job);
        }
        
        // Delete S3 files
        await this.deleteJobFiles(job);
        
        // Update database status
        await this.markJobAsExpired(job.id);
        
        cleanupResults.push({
          jobId: job.id,
          filesDeleted: job.file_count,
          bytesFreed: job.total_size_bytes,
          success: true
        });
        
      } catch (error) {
        cleanupResults.push({
          jobId: job.id,
          success: false,
          error: error.message
        });
      }
    }
    
    return {
      processedJobs: cleanupResults.length,
      successfulCleanups: cleanupResults.filter(r => r.success).length,
      totalBytesFreed: cleanupResults.reduce((sum, r) => sum + (r.bytesFreed || 0), 0),
      results: cleanupResults
    };
  }
  
  private static async deleteJobFiles(job: ExpiredJob): Promise<void> {
    // Delete original file
    await s3Client.deleteObject({
      Bucket: process.env.S3_BUCKET,
      Key: job.s3_input_key
    });
    
    // Delete all output files
    for (const output of job.outputs) {
      await s3Client.deleteObject({
        Bucket: process.env.S3_BUCKET,
        Key: output.s3_output_key
      });
    }
  }
}

// Cron job setup (in worker service)
cron.schedule('0 2 * * *', async () => { // Daily at 2 AM
  console.log('Running daily cleanup...');
  const report = await CleanupService.runDailyCleanup();
  console.log('Cleanup completed:', report);
  
  // Notify admin if significant issues
  if (report.successfulCleanups / report.processedJobs < 0.9) {
    await notifyAdmin('Cleanup issues detected', report);
  }
});
```

---

## 7. Authentication & Payment Flows

### Clerk Authentication Integration

#### Frontend Authentication Setup
```typescript
// app/layout.tsx
import { ClerkProvider } from '@clerk/nextjs';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <ClerkProvider
      publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      appearance={{
        baseTheme: "dark",
        variables: {
          colorPrimary: "#3b82f6"
        }
      }}
    >
      <html lang="en">
        <body>{children}</body>
      </html>
    </ClerkProvider>
  );
}
```

#### User Synchronization
```typescript
// pages/api/auth/webhook.ts - Clerk webhook handler
import { Webhook } from 'svix';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const webhook = new Webhook(process.env.CLERK_WEBHOOK_SECRET);
  
  try {
    const evt = webhook.verify(req.body, req.headers);
    
    switch (evt.type) {
      case 'user.created':
        await createUserRecord(evt.data);
        break;
        
      case 'user.updated':
        await updateUserRecord(evt.data);
        break;
        
      case 'user.deleted':
        await deleteUserRecord(evt.data);
        break;
    }
    
    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(400).json({ error: 'Webhook verification failed' });
  }
}

async function createUserRecord(clerkUser: any) {
  await supabase.from('users').insert({
    clerk_user_id: clerkUser.id,
    email: clerkUser.email_addresses[0]?.email_address,
    full_name: `${clerkUser.first_name} ${clerkUser.last_name}`.trim(),
    subscription_status: 'trial',
    free_repurposings_used: 0
  });
}
```

#### Protected Route Middleware
```typescript
// middleware.ts
import { authMiddleware } from '@clerk/nextjs';

export default authMiddleware({
  publicRoutes: ['/api/health', '/api/webhooks/(.*)'],
  ignoredRoutes: ['/api/auth/webhook'],
  apiRoutes: ['/api/(.*)'],
  afterAuth(auth, req, evt) {
    // Handle users who aren't authenticated
    if (!auth.userId && !auth.isPublicRoute) {
      return redirectToSignIn({ returnBackUrl: req.url });
    }
    
    // Redirect authenticated users to dashboard if they're on public routes
    if (auth.userId && auth.isPublicRoute && req.nextUrl.pathname === '/') {
      return NextResponse.redirect(new URL('/dashboard', req.url));
    }
  },
});

export const config = {
  matcher: ['/((?!.*\\..*|_next).*)', '/', '/(api|trpc)(.*)'],
};
```

### Stripe Payment Integration

#### Subscription Management
```typescript
// lib/stripe.ts
import Stripe from 'stripe';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export const SUBSCRIPTION_PLANS = {
  PRO: {
    priceId: process.env.STRIPE_PRO_PRICE_ID!,
    amount: 3000, // $30.00
    currency: 'usd',
    interval: 'month'
  }
};

// Create checkout session
export async function createCheckoutSession(
  userId: string,
  priceId: string,
  customerId?: string
): Promise<Stripe.Checkout.Session> {
  
  const session = await stripe.checkout.sessions.create({
    customer: customerId,
    customer_email: customerId ? undefined : await getUserEmail(userId),
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?payment=success`,
    cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/pricing?payment=cancelled`,
    metadata: {
      userId,
    },
    subscription_data: {
      metadata: {
        userId,
      },
    },
    allow_promotion_codes: true,
    billing_address_collection: 'required',
    customer_update: {
      name: 'auto',
    },
  });

  return session;
}
```

#### Stripe Webhook Handler
```typescript
// pages/api/payments/webhook.ts
import { buffer } from 'micro';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const buf = await buffer(req);
  const signature = req.headers['stripe-signature'] as string;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(buf, signature, process.env.STRIPE_WEBHOOK_SECRET!);
  } catch (error) {
    console.error('Webhook signature verification failed:', error);
    return res.status(400).send('Webhook signature verification failed');
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
        break;

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        break;

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    res.status(500).json({ error: 'Webhook handler failed' });
  }
}

async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  const userId = session.metadata?.userId;
  if (!userId) return;

  const subscription = await stripe.subscriptions.retrieve(session.subscription as string);
  
  await supabase
    .from('users')
    .update({
      subscription_status: 'active',
      subscription_id: subscription.id,
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
    })
    .eq('clerk_user_id', userId);

  // Record transaction
  await supabase.from('payment_transactions').insert({
    user_id: userId,
    stripe_subscription_id: subscription.id,
    amount_cents: subscription.items.data[0].price.unit_amount,
    currency: subscription.currency,
    status: 'succeeded',
    transaction_type: 'subscription'
  });
}
```

#### Usage Tracking & Limits
```typescript
// lib/usageManager.ts
export class UsageManager {
  static async checkUsageLimit(userId: string): Promise<UsageLimitResult> {
    const user = await supabase
      .from('users')
      .select('subscription_status, free_repurposings_used, current_period_end')
      .eq('clerk_user_id', userId)
      .single();

    if (!user.data) {
      throw new Error('User not found');
    }

    const { subscription_status, free_repurposings_used, current_period_end } = user.data;

    // Check subscription status
    if (subscription_status === 'trial') {
      const hasFreesLeft = free_repurposings_used < 1;
      return {
        allowed: hasFreesLeft,
        reason: hasFreesLeft ? undefined : 'Free trial limit reached',
        remainingFree: Math.max(0, 1 - free_repurposings_used),
        requiresSubscription: !hasFreesLeft
      };
    }

    if (subscription_status === 'active') {
      // Check if subscription is still valid
      const isActive = current_period_end && new Date(current_period_end) > new Date();
      return {
        allowed: isActive,
        reason: isActive ? undefined : 'Subscription expired',
        isSubscriber: true,
        subscriptionExpired: !isActive
      };
    }

    return {
      allowed: false,
      reason: 'No valid subscription or trial',
      requiresSubscription: true
    };
  }

  static async consumeFreeRepurposing(userId: string): Promise<void> {
    await supabase.rpc('increment_free_usage', { user_id: userId });
  }

  static async logUsage(userId: string, action: string, details: any): Promise<void> {
    await supabase.from('usage_logs').insert({
      user_id: userId,
      action,
      details,
      ip_address: req.socket.remoteAddress,
      user_agent: req.headers['user-agent']
    });
  }
}
```

#### Frontend Usage Display
```typescript
// components/UsageDashboard.tsx
export function UsageDashboard() {
  const { user } = useUser();
  const [usage, setUsage] = useState<UsageData | null>(null);

  useEffect(() => {
    if (user) {
      fetchUsage();
    }
  }, [user]);

  const fetchUsage = async () => {
    const response = await fetch('/api/user/usage');
    const data = await response.json();
    setUsage(data);
  };

  return (
    <div className="usage-dashboard">
      <div className="usage-card">
        <h3>Current Plan</h3>
        {usage?.subscription_status === 'trial' ? (
          <div>
            <p>Free Trial</p>
            <p>{usage.remaining_free} repurposing remaining</p>
            {usage.remaining_free === 0 && (
              <Button onClick={upgradeToProModal}>
                Upgrade to Pro ($30/month)
              </Button>
            )}
          </div>
        ) : (
          <div>
            <p>Pro Plan - $30/month</p>
            <p>Unlimited repurposings</p>
            <p>Next billing: {new Date(usage.current_period_end).toLocaleDateString()}</p>
            <Button variant="outline" onClick={manageBilling}>
              Manage Billing
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
```

---

## 8. Development Roadmap

### 4-7 Day MVP Development Plan

## Day 1: Foundation & Core Setup

### Morning (4 hours)
**Project Initialization & Database Setup**
- [ ] Create Next.js project with TypeScript
- [ ] Set up Supabase project and configure environment variables
- [ ] Create PostgreSQL database schema (run all table creation scripts)
- [ ] Set up AWS S3 bucket with CORS and lifecycle policies
- [ ] Configure Upstash Redis instance

### Afternoon (4 hours)
**Authentication & Basic API Structure**
- [ ] Integrate Clerk authentication (pages, middleware, webhook)
- [ ] Create user sync system with database
- [ ] Build protected API route structure
- [ ] Set up basic user dashboard layout
- [ ] Create user table synchronization with Clerk webhook

**Deliverables:**
- ✅ Working authentication system
- ✅ Database connected and seeded
- ✅ Basic dashboard accessible to authenticated users

---

## Day 2: File Upload & Classification System

### Morning (4 hours)
**S3 Integration & Upload Pipeline**
- [ ] Implement presigned URL generation API
- [ ] Create resumable file upload component (5GB support)
- [ ] Build progress tracking UI
- [ ] Set up S3 storage structure and naming conventions

### Afternoon (4 hours)
**Video Classification Engine**
- [ ] Install and configure FFmpeg on development environment
- [ ] Create video metadata extraction service using ffprobe
- [ ] Implement form type classification logic (duration + aspect ratio)
- [ ] Build platform rules loading system from JSON file
- [ ] Create job record creation API

**Deliverables:**
- ✅ Users can upload videos up to 5GB
- ✅ Videos are automatically classified as short_form or long_form
- ✅ Platform compatibility determined by form type

---

## Day 3: Worker Service & Processing Pipeline

### Morning (4 hours)
**Worker Service Setup**
- [ ] Set up Node.js worker service project structure
- [ ] Configure BullMQ with Redis connection
- [ ] Create job queue definitions (classification, platform-matching, processing)
- [ ] Implement basic job processing workflow

### Afternoon (4 hours)
**FFmpeg Processing Pipeline**
- [ ] Create FFmpeg command builder system
- [ ] Implement platform-specific conversion presets
- [ ] Build video processing worker with progress tracking
- [ ] Set up error handling and retry mechanisms
- [ ] Create S3 upload system for processed videos

**Deliverables:**
- ✅ Working job queue system
- ✅ Video processing converts to platform-specific formats
- ✅ Progress tracking functional

---

## Day 4: Frontend Interface & Real-time Updates

### Morning (4 hours)
**Platform Selection UI**
- [ ] Create platform selection interface based on form type
- [ ] Build preset quality selection (high/medium/low)
- [ ] Implement job submission system
- [ ] Add form validation and user feedback

### Afternoon (4 hours)
**Real-time Job Status**
- [ ] Implement Server-Sent Events (SSE) for job status updates
- [ ] Create job status dashboard with progress bars
- [ ] Build download interface with presigned URLs
- [ ] Add job management features (cancel, retry, delete)

**Deliverables:**
- ✅ Complete user interface for video repurposing
- ✅ Real-time status updates working
- ✅ Download system functional

---

## Day 5: Payment Integration & Usage Limits

### Morning (4 hours)
**Stripe Integration**
- [ ] Set up Stripe account and webhook endpoints
- [ ] Create checkout session API for $30/month subscription
- [ ] Implement subscription status tracking
- [ ] Build billing management interface

### Afternoon (4 hours)
**Usage Management**
- [ ] Implement usage tracking system
- [ ] Create free trial limit enforcement (1 free repurposing)
- [ ] Build subscription validation for unlimited access
- [ ] Add usage dashboard and upgrade prompts

**Deliverables:**
- ✅ Working payment system
- ✅ Free trial with 1 repurposing limit
- ✅ Subscription unlock unlimited usage

---

## Day 6: Cleanup System & Admin Panel

### Morning (4 hours)
**File Retention & Cleanup**
- [ ] Implement 3-day file retention system
- [ ] Create automated cleanup cron jobs
- [ ] Build file expiration tracking
- [ ] Set up cleanup notifications and logging

### Afternoon (4 hours)
**Admin Panel & Platform Rules Management**
- [ ] Create admin-only routes and interface
- [ ] Build platform rules JSON editor
- [ ] Implement rules validation system
- [ ] Add system monitoring dashboard

**Deliverables:**
- ✅ Automatic file cleanup after 3 days
- ✅ Admin panel for platform rules management
- ✅ System health monitoring

---

## Day 7: Testing, Deployment & Polish

### Morning (4 hours)
**Deployment & Production Setup**
- [ ] Deploy Next.js frontend to Vercel
- [ ] Deploy worker service to Railway
- [ ] Configure production environment variables
- [ ] Set up domain and SSL certificates
- [ ] Test production deployment end-to-end

### Afternoon (4 hours)
**Final Testing & Bug Fixes**
- [ ] Comprehensive end-to-end testing
- [ ] Performance optimization
- [ ] Error handling improvements
- [ ] User experience polish
- [ ] Documentation completion

**Deliverables:**
- ✅ Production-ready application deployed
- ✅ All core features tested and working
- ✅ User documentation completed

---

### Priority Feature Matrix

#### Must-Have (MVP Core)
1. **File Upload** - 5GB limit, resumable uploads
2. **Video Classification** - Form type detection
3. **Platform Conversion** - FFmpeg processing with presets
4. **Payment System** - $30/month subscription with 1 free trial
5. **Download System** - 3-day retention with auto-cleanup
6. **Real-time Status** - Job progress tracking

#### Should-Have (Enhanced Experience)
1. **Admin Panel** - Platform rules management
2. **Usage Dashboard** - Analytics and billing management
3. **Error Recovery** - Job retry and better error messages
4. **Mobile Responsive** - Mobile-friendly interface

#### Could-Have (Future Iterations)
1. **Batch Processing** - Multiple video upload
2. **Advanced Presets** - Custom quality settings
3. **API Access** - Developer API for integrations
4. **Team Plans** - Multi-user subscriptions

### Technical Risk Mitigation

#### High-Risk Areas & Solutions
1. **FFmpeg Performance**
   - Risk: Long processing times for large files
   - Solution: Implement processing timeouts, queue prioritization

2. **File Storage Costs**
   - Risk: S3 storage costs for large files
   - Solution: Aggressive 3-day cleanup, compression optimization

3. **Worker Service Reliability**
   - Risk: Processing failures and stuck jobs
   - Solution: Job retries, dead letter queues, health checks

4. **Payment Integration**
   - Risk: Webhook reliability and subscription edge cases
   - Solution: Idempotent webhook handlers, subscription validation

### Infrastructure Requirements

#### Development Environment
- Node.js 18+
- FFmpeg 5.0+
- Docker (for local Redis/PostgreSQL)
- Git with environment variable management

#### Production Services
- **Vercel**: Next.js hosting ($0 for hobby tier)
- **Railway**: Worker service hosting (~$20/month)
- **Supabase**: PostgreSQL database (free tier)
- **AWS S3**: File storage (~$10-50/month depending on usage)
- **Upstash Redis**: Job queue (free tier available)
- **Clerk**: Authentication ($25/month for production)
- **Stripe**: Payment processing (2.9% + $0.30 per transaction)

**Estimated Monthly Cost: $60-100 for initial launch**

### Success Metrics

#### Technical Metrics
- Upload success rate > 98%
- Processing success rate > 95%
- Average processing time < 2 minutes per platform
- API response time < 500ms

#### Business Metrics
- Free trial to paid conversion > 10%
- User retention after first week > 40%
- Average revenue per user (ARPU) = $30/month
- Customer support requests < 5% of users

This roadmap provides a comprehensive path to launch AutoRepurpose SaaS within 4-7 days, with clear priorities and risk mitigation strategies for each phase of development.
